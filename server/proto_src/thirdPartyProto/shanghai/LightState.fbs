namespace MECData;

// @brief 信号灯组灯色状态
enum DE_LightState:byte {
    unavailable = 0,                                          // 未知
    dark = 1,                                                 // 灭灯
    stopThenProceed = 2,                                      // 红闪
    stopAndRemain = 3,                                        // 红灯
    preMovement = 4,                                          // 红黄灯
    permissiveMovementAllowed = 5,                            // 绿灯（非保护，可能存在冲突流向）
    protectedMovementAllowed = 6,                             // 绿灯（保护，不存在冲突流向）
    intersectionClearance = 7,                                // 黄灯
    cautionConflictingTraffic = 8                             // 黄闪
}
