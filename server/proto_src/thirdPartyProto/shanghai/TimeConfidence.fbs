namespace MECData;

// @brief 95%置信水平的时间精度

enum DE_TimeConfidence:byte{
    unavailable=0,                                     //不可用
    time100000=1,                                      //优于100s
    time050000=2,                                      //优于50s
    time020000=3,                                      //优于20s
    time010000=4,                                      //优于10s
    time002000=5,                                      //优于2s
    time001000=6,                                      //优于1s
    time000500=7,                                      //优于0.5s
    time000200=8,                                      //优于0.2s
    time000100=9,                                      //优于0.1s
    time000050=10,                                     //优于0.05s
    time000020=11,                                     //优于0.02s
    time000010=12,                                     //优于0.01s
    time000005=13,                                     //优于0.005s
    time000002=14,                                     //优于0.002s
    time000001=15,                                     //优于0.001s
    time0000005=16,                                    //优于0.000,5s
    time0000002=17,                                    //优于0.000,2s
    time0000001=18,                                    //优于0.000,1s
    time00000005=19,                                   //优于0.000,05s
    time00000002=20,                                   //优于0.000,02s
    time00000001=21,                                   //优于0.000,01s
    time000000005=22,                                  //优于0.000,005s
    time000000002=23,                                  //优于0.000,002s
    time000000001=24,                                  //优于0.000,001s
    time0000000005=25,                                 //优于0.000,000,5s
    time0000000002=26,                                 //优于0.000,000,2s
    time0000000001=27,                                 //优于0.000,000,1s
    time00000000005=28,                                //优于0.000,000,05s
    time00000000002=29,                                //优于0.000,000,02s
    time00000000001=30,                                //优于0.000,000,01s
    time000000000005=31,                               //优于0.000,000,005s
    time000000000002=32,                               //优于0.000,000,002s
    time000000000001=33,                               //优于0.000,000,001s
    time0000000000005=34,                              //优于0.000,000,000,5s
    time0000000000002=35,                              //优于0.000,000,000,2s
    time0000000000001=36,                              //优于0.000,000,000,1s
    time00000000000005=37,                             //优于0.000,000,000,05s
    time00000000000002=38,                             //优于0.000,000,000,02s
    time00000000000001=39                              //优于0.000,000,000,01s
}
