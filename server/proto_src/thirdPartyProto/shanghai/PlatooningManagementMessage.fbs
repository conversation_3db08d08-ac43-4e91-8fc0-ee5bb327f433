include "DebugTimeRecords.fbs";

namespace MECData;

// @brief 车辆在编队中的角色
enum DE_RoleInPlatooning: uint8 {
    LEADER = 0,         // 头车
    FOLLOWER = 1,       // 跟驰车辆
    TAIL = 2,           // 尾车
    FREE_VEHICLE = 3    // 未加入编队
}

// @brief 车辆在编队中的状态
enum DE_StatusInPlatooning: uint8 {
    NAVIGATING = 0,                     // 领航
    BEGIN_TO_DISMISS = 1,               // 开始解散
    ASK_FOR_JOINING = 2,                // 申请入队
    JOINING = 3,                        // 入队
    FOLLOWING = 4,                      // 跟随
    ASK_FOR_LEAVING = 5,                // 申请离队
    LEAVING = 6                         // 离队
}

// @brief 车队成员节点
table DF_MemberNode {
    vid: [uint8](required);             // 车辆ID，通常为车辆OBU ID
}

// @brief 车队成员管理
table DF_MemberManagement {
    member_list: [DF_MemberNode];       // 现在成员
    joining_list: [DF_MemberNode];      // 加入中成员
    leaving_list: [DF_MemberNode];      // 离队中成员
    capacity: uint8;                    // 最大可容纳成员车辆数，取值范围：1~32
    open_to_join: bool;                 // 开放外部车辆入队
}

// @brief 面向无连接的车队管理消息
table MSG_PlatooningManagementMessage {
    id: [uint8](required);              // 车辆OBU ID
    moy: uint32;                        // 今年已经过去的分钟数
    secmark: uint16;                    // 当前分钟已经过去的毫秒数
    pid: [uint8](required);             // 车队ID，长17字节
    role: DE_RoleInPlatooning;          // 车队角色
    status: DE_StatusInPlatooning;      // 在车队中的状态
    leading_ext: DF_MemberManagement;   // 车队头车相关扩展信息
    time_records: DF_DebugTimeRecords;  // 消息模块间流转记录
    msg_id: int64;                      // 消息雪花ID
}

root_type MSG_PlatooningManagementMessage;
