include "NodeReferenceID.fbs";
include "MovementEx.fbs";

namespace MECData;

// @brief 信号灯优先请求信息

table DF_ReqSignalPriority {
    intersectionId:DF_NodeReferenceID(required);            // 请求交叉口
    requiredMov:DF_MovementEx(required);                    // 请求转向
    estimatedArrivalTime:ushort;     		                // 预计到达时刻距当前时刻的时刻偏移，单位：10 ms
    distance2Intersection:ushort;                           // 距离交叉口的距离，单位：0.1m
}
