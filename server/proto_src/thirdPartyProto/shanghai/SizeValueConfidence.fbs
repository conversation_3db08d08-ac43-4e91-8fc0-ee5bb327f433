namespace MECData;

// @brief 物体尺寸置信度
enum DE_SizeValueConfidence: uint8 {
    unavailable = 0,        // 不可用
    size_100_00 = 1,        // 100 m
    size_050_00 = 2,        // 50 m
    size_020_00 = 3,        // 20 m
    size_010_00 = 4,        // 10 m
    size_005_00 = 5,        // 5 m
    size_002_00 = 6,        // 2 m
    size_001_00 = 7,        // 1 m
    size_000_50 = 8,        // 50 cm
    size_000_20 = 9,        // 20 cm
    size_000_10 = 10,       // 10 cm
    size_000_05 = 11,       // 5 cm
    size_000_02 = 12,       // 2 cm
    size_000_01 = 13        // 1 cm
}
