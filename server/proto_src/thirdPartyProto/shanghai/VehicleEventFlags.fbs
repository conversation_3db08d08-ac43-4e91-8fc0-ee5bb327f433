namespace MECData;

// @brief 车辆特殊状态
table DF_VehicleEventFlags {
    events:short;                           // 车辆特殊状态，具备此状态时，对应二进制位置1
                                            //eventHazardLights=0，车辆警示灯亮起
                                            //eventStoplineViolation=1，车辆在到达路口前预测自己可能会来不及刹车而越过停止线
                                            //eventABSactivated=2，ABS系统被触发并超过100毫秒
                                            //eventTractionControlLoss=3，电子系统控制牵引力被触发并超过100毫秒
                                            //eventStabilityControlactivated=4，车身稳定控制被触发并超过100毫秒
                                            //eventHazardousMaterials=5，危险品运输车
                                            //eventReserved1=6，
                                            //eventHardBraking=7，车辆急刹车，并且减速度大于0.4G
                                            //eventLightsChanged=8，过去2 秒内，车灯状态改变
                                            //eventWipersChanged=9，过去2 秒内，车辆雨刷（前窗或后窗）状态改变
                                            //eventFlatTire=10，车辆发现至少1 个轮胎爆胎了
                                            //eventDisabledVehicle=11，车辆故障，无法行驶
                                            //eventAirbagDeployment =12，至少1个安全气囊弹出
}
