include "ModuleIOConfig.fbs";
include "CommunicationProtocol.fbs";

namespace MECData;

// @brief 标准消息序列化格式
enum DE_SerializationMethod: byte {
    JSON = 0,                                   // JSON
    PROTOBUF = 1,                               // Protocol Buffer
    FLATBUFFERS = 2,                            // Flatbuffers
}

// @brief MQTT协议连接参数配置
table DE_MQTTConfig {
    host: string;                               // MQTT broker主机名
    port: uint16;                               // MQTT broker端口号
    username: string;                           // 用户名
    password: string;                           // 密码
    client_id: string;                          // MQTT Client ID
    tls_enabled: bool;                          // 启用TLS
    certificate_path: string;                   // MQTT连接证书
}

// @brief UDP协议连接参数配置
table DE_UDPConfig {
    server_address: string;                     // 对端主机号
    server_port: uint16;                        // 对端监听端口号
    client_address: string;                     // 本地绑定地址
    client_port: uint16;                        // 本地监听端口号
}

// @brief UNIX Domain Socket 协议连接参数配置
table DE_USOCKConfig {
    socket_file: string;                        // UNIX Domain Socket文件名
}

// @brief TCP协议连接参数配置
table DE_TCPConfig {
    server_address: string;                     // 对端主机号
    server_port: uint16;                        // 对端监听端口号
    client_address: string;                     // 本地绑定地址
    client_port: uint16;                        // 本地监听端口号
}

// @brief 网络连接参数配置
union DF_CommunicationConfig {
    DE_MQTTConfig,                              // MQTT 连接参数配置
    DE_UDPConfig,                               // UDP 连接参数配置
    DE_USOCKConfig,                             // UNIX Domain Socket 连接参数配置
    DE_TCPConfig,                               // TCP 连接参数配置
}

// @brief 消息缓存模型
enum DE_QueueingModel: byte {
    FIFO = 0,                                   // 先进先出
    FILO = 1,                                   // 先进后出
}

// @brief 缓存大小配置
table DE_CacheConfig {
    max_size_bytes: uint64;                     // 可缓存最大消息长度
    max_entries: uint64;                        // 可缓存最大消息数量
}

// @brief 消息缓存策略
table DF_QueueCacheConfig {
    queue_model: DE_QueueingModel = FIFO;       // 消息队列模型，默认先进先出
    cache_config: DE_CacheConfig;               // 缓存大小配置
}

// @brief RPC接入点连接参数
table DF_EndpointConfig {
  host: string;                                 // 接入点所在主机名，一般取"::"
  port: uint16;                                 // 接入点监听的端口号
  protocol: DE_CommunicationProtocol;           // 接入点采用的底层通信协议
}

// @brief RPC接入点信息
table DF_Endpoint {
  name: string;                                 // 接入点名称
  description: string;                          // 接入点描述
  endpoint_config: DF_EndpointConfig;           // 接入点连接参数
  io_config: DF_ModuleIOConfig;                 // MECData标准消息输入输出需求配置
}

// @brief MEC核心服务连接配置
table DE_MECServiceConfig {
  host: string;                                 // MEC核心服务所在主机名，一般为127.0.0.1
  port: uint16;                                 // MEC核心服务监听端口号，一般为30000
}

// @brief ZStub详细设置
table DF_ZStubSettings {
  mecdata_schema_file: string;                  // MECData schema文件路径
  local_fs_root: string;                        // 对应用可见的根目录
  mec_service: DE_MECServiceConfig;             // MEC核心服务连接配置
  comm_protocol: DE_CommunicationProtocol;      // ZStub底层通信协议
  comm_config: DF_CommunicationConfig;          // ZStub RPC通信参数配置
  queue_model: DF_QueueCacheConfig;             // 消息缓存模型
  module_connectable_endpoint: [DF_Endpoint];   // 可供外部模块接入的额外RPC接入点
}

// @brief ZStub配置
table DF_ZStubConfig {
  enabled: bool;                                // 是否为本应用启用ZStub
  settings: DF_ZStubSettings;                   // ZStub详细设置
}
