include "Position3D.fbs";
include "ReferenceLink.fbs";
include "ReferencePath.fbs";
include "RSITimeDetails.fbs";
include "DebugTimeRecords.fbs";
include "ParticipantID.fbs";
include "Image.fbs";
include "EventSource.fbs";
include "ObstacleData.fbs";
include "EventAction.fbs";
include "EventOperationType.fbs";

namespace MECData;

// @brief 交通事件
table MSG_TrafficEvent {
    eventId: uint = 4294967295;                     // 事件ID
    eventType: ushort = 65535;                      // 事件类型，编号规则按GB/T 29100-2012
    source: ubyte = 255;                            // 事件来源
                                                    // 0-未知
                                                    // 1-交警
                                                    // 2-政府机构
                                                    // 3-气象部门
                                                    // 4-互联网服务
                                                    // 5-本地检测
    eventPos: DF_Position3D(required);              // 事件位置
    eventRadius: ushort;                            // 事件影响半径，单位：0.1m
    description: string;                            // 事件描述
    timeDetails: DF_RSITimeDetails;                 // 事件时效
    priority: int;                                  // 事件优先级，只使用前3个bit，值B00000000到B11100000表示优先级从低到高
    referencePaths: [DF_ReferencePath];             // 关联路径
    pathRadius: ushort;                             // 关联路径宽度，单位：0.1m
    referenceLinks:[DF_ReferenceLink];              // 关联道路
    eventConfidence: ubyte;                         // 事件置信度，单位：0.5%
    time_ext: uint64;                               // 事件发生时间，毫秒级UNIX时间戳
    time_records: DF_DebugTimeRecords;              // 消息模块间流转记录
    participant_ids: [DF_ParticipantID];            // 涉及交通参与者ID
    obstacles: [DF_ObstacleData];                   // 涉及障碍物
    images: [DF_Image];                             // 事件图片及视频
    devices: [uint16];                              // 检测出事件的应用模块实例ID
    event_source: DF_EventSource;                   // 事件来源
    msg_id: int64;                                  // 消息雪花ID
    session_id: int64;                              // 会话ID
    action: DE_EventAction;                         // 事件操作动作
    operation_type: DE_EventOperationType;          // 事件转发操作类型
}

root_type MSG_TrafficEvent;
