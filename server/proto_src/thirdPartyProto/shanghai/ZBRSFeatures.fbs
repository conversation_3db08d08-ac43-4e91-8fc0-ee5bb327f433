include "ErrorLevel.fbs";
include "SSLCertificate.fbs";
include "OperationTags.fbs";
include "MapCollection.fbs";
include "PacketFlowRecord.fbs";
include "Mileage.fbs";
include "Position3D.fbs";
include "Snowflake.fbs";
include "LogConfig.fbs";
include "ZStub.fbs";

namespace MECData;

// @brief ZBRS核心服务版本
table DF_ZBRSVersion {
    major: uint16;                  // 主版本号
    minor: uint16;                  // 次版本号
    patch: uint16;                  // 补丁版本号
    pre_release: string;            // 预发布版本号
    build_date: string;             // 构建日期，格式yyyy-mm-dd
}

// @brief 消息路由记录
table DF_PacketRoute {
    mec_data_type: uint16;          // 消息类型代号
    src_module_id: uint16;          // 来源模块实例id
    dst_module_ids: [uint16];       // 应转发的模块实例id
}

// @brief OTA服务器连接参数配置
table DF_OTAServerConfig {
    ota_host: string;               // OTA服务器主机名
    ota_port: uint16;               // OTA服务监听端口号
    username: string;               // 连接用户名，查询时不填
    password: string;               // 连接密码或密钥，查询时不填
}

// @brief Docker Registry 服务连接配置
table DF_DockerRegistryConfig {
    registry_host: string;          // Docker Registry 服务主机名
    registry_port: uint16;          // Docker Registry 服务监听端口号
    secure: bool;                   // 使用SSL安全连接
    username: string;               // 连接用户名，查询时不填
    password: string;               // 连接密码或密钥，查询时不填
}

// @brief 崩溃报告上传参数配置
table DE_CrashReportServerConfig {
    server_host: string;            // 崩溃报告接收服务主机名
    server_port: uint16;            // 崩溃报告服务监听端口号
}

// @brief 崩溃报告策略
table DF_CrashReportConfig {
    crash_report_server_config: DE_CrashReportServerConfig;     // 崩溃报告上传参数配置
    coredump_path: string;                      // coredump储存路径，一般取/opt/mec/dmp
    max_days_to_keep: uint16;                   // coredump最大保存天数，单位：天
}

// @brief GPU工具链环境信息
table DE_SystemGPUToolchainEnv {
    cuda_version: string;                       // CUDA版本
    cudnn_version: string;                      // CuDNN版本
}

// @brief 系统架构
enum DE_SystemArchitecture: byte {
    X86_64 = 0,                                 // x86_64
    AARCH64 = 1,                                // aarch64
}

// @brief ZBRS设备环境信息
table DF_ZBRSEnvironment {
    zbrs_version: DF_ZBRSVersion;               // zbrs 版本号
    internal_modules: [string];                 // 可用内建模块列表
    system_arch: DE_SystemArchitecture;         // 系统架构
    gpu_toolchain: DE_SystemGPUToolchainEnv;    // GPU工具链信息
    glibc_version: string;                      // GLIBC版本
    docker_api_version: string;                 // Docker Engine API 版本
    installed_apps: [string];                   // 已安装模块列表UUID (包含未启动的模块)
}

// @brief ZBRS本地设备配置参数
table DF_ZBRSLocalMachineConfig {
    serial_number: string;                          // MEC设备序列号
    model: string;                                  // MEC设备型号
    description: string;                            // MEC设备描述
    pos: DF_Position3D;                             // MEC设备参考经纬度
    node: DF_NodeReferenceID;                       // MEC设备所在交叉口参考ID
    mileage: DF_Mileage;                            // MEC设备所在桩号
    routes: [DF_PacketRoute];                       // 消息路由设置
    ota_server: DF_OTAServerConfig;                 // OTA 服务器配置
    docker_registry: DF_DockerRegistryConfig;       // Docker Registry 配置
    log_settings: DF_LogConfig;                     // 日志设置
    ssl_certificates: [DF_SSLCertificate];          // SSL 证书管理
    crash_report_settings: DF_CrashReportConfig;    // 崩溃报告设置
    immediately_report_level: DE_ErrorLevel;        // 即时告警层级设置
    map_collection: DE_MapCollection;               // 地图管理器合集
    snowflake: DF_Snowflake;                        // 雪花id
    tags: DE_OperationTags;                         // 运维标记
    mqtt_broker: DE_MQTTConfig;                     // 业务MQTT broker平台配置
}

// @brief ZBRS路侧系统元数据
table MSG_ZBRSFeatures {
    local: DF_ZBRSLocalMachineConfig;               // 本地设备配置参数
    env: DF_ZBRSEnvironment;                        // ZBRS设备环境信息
    packet_stats: DF_PacketFlowRecord;              // 消息流拓扑图及消息流统计
    msg_id: int64;                                  // 消息雪花ID
}

root_type MSG_ZBRSFeatures;
