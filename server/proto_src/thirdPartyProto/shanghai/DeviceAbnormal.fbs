namespace MECData;

// @brief 外接设备状态

enum DE_DeviceState: uint8 {
    OK = 0,                             // 正常
    OFF = 1,                            // 设备关闭
    ABNORMAL = 2                        // 设备异常
}

// @brief 外接设备异常类型

enum DE_DeviceAbnormalType: uint8 {
    DEV_STATE_OK = 0,                   // 无异常
    DEV_AB_POWER = 1,                   // 电源异常或断开
    DEV_AB_NETWORK = 2,                 // 网络连接问题
    DEV_AB_ENV = 3,                     // 运行环境异常
    DEV_AB_FUNC = 4,                    // 设备功能异常
    DEV_AB_INTERR = 5,                  // 设备内部错误
    DEV_AB_UNKNOWN = 255                // 未知类型
}

// @brief 外接设备异常状态

table DF_DeviceAbnormal {
    state: DE_DeviceState = OK;         // 外接设备状态
    abnormalType: DE_DeviceAbnormalType = DEV_STATE_OK;     // 外接设备异常类型
    description: string;                // 异常描述
}
