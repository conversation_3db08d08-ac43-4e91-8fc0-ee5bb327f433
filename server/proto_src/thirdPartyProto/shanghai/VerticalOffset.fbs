include "VertOffsetB07.fbs";
include "VertOffsetB08.fbs";
include "VertOffsetB09.fbs";
include "VertOffsetB10.fbs";
include "VertOffsetB11.fbs";
include "VertOffsetB12.fbs";

namespace MECData;

// @brief 海拔
table DF_Elevation {
    ele:short;     // 海拔，单位：10cm，取值范围-409.5m ~ +6143.9m
}

// @brief 7bit垂直方向位置偏差
union DF_VerticalOffset {
    DF_VertOffsetB07,                                          // 7bit垂直方向位置偏差
    DF_VertOffsetB08,                                          // 8bit垂直方向位置偏差
    DF_VertOffsetB09,                                          // 9bit垂直方向位置偏差
    DF_VertOffsetB10,                                          // 10bit垂直方向位置偏差
    DF_VertOffsetB11,                                          // 11bit垂直方向位置偏差
    DF_VertOffsetB12,                                          // 12bit垂直方向位置偏差
    DF_Elevation                                               // 海拔绝对值
}
