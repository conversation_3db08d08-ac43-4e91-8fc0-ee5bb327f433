namespace MECData;

// @brief 工单状态
enum DF_TicketAction: uint8 {
    create = 0,         // 创建
    process = 1,        // 处理中
    hold = 2,           // 暂停处理
    resume = 3,         // 恢复处理
    done = 4,           // 处理成功
    failed = 5,         // 处理失败
    close = 255         // 关闭工单
}

// @brief 云边协同工单
table MSG_Ticket {
    app_id: uint16;             // 需要处理此工单的应用模块实例ID
    ticket_id: string;          // 工单唯一ID
    ticket_type: uint8 = 0;     // 工单类型，由具体业务约定
    ticket_subtype: uint8 = 0;  // 工单子类型，由具体业务约定
    action: DF_TicketAction;    // 工单动作
    timestamp: int64;           // 消息的毫秒级UNIX时间戳
    data_type_code: uint16 = 0; // 标准数据类型代号
    data: [uint8];              // 工单内容，由具体业务约定
    msg_id: int64;              // 消息雪花ID
}

root_type MSG_Ticket;
