namespace MECData;

// @brief 用UTC世界标准时间形式，描述一个信号灯灯组状态的完整计时状态
table DF_UTCTiming {
    startUTCTime:ushort = 65535;                                // 如果当前该灯组状态已开始（未结束），则该数值为当前状态开始的时刻；
                                                                // 如果当前该灯组状态未开始，则表示当前该灯组状态下一次开始的时刻。
                                                                // 单位：0.1s
    minEndUTCTime:ushort;                                       // 表示该灯组状态下一次以最短时间结束所对应的时刻（不管当前时刻该灯组状态是否开始）
                                                                // 单位：0.1s
    maxEndUTCTime:ushort;                                       // 表示该灯组状态下一次以最长时间结束所对应的时刻（不管当前时刻该灯组状态是否开始）
                                                                // 单位：0.1s
    likelyEndUTCTime:ushort = 65535;                            // 表示该灯组状态估计下一次结束的时刻（不管当前时刻该灯组状态是否开始）
                                                                // 如果该信号灯灯组是定周期、固定时长，则该数值就表示该灯组状态下一次结束的准确时刻
                                                                // 如果信号灯当前灯组是非固定配时（感应配时、手动控制等），则该数值表示预测的结束时刻，且预测时刻必须在minEndUTCTime和maxEndUTCTime之间，可能由历史数据或一些事件触发等来进行预测
                                                                // 单位：0.1s
    timeConfidence:ushort;                                      // 上述likelyEndUTCTime 预测时间的置信度水平，单位：0.1s
    nextStartUTCTime:ushort;                                    // 如果当前该灯组状态已开始（未结束），则该数值表示该灯组状态估计下一次开始的时刻
                                                                // 如果当前该灯组状态未开始，则表示该灯组状态估计第二次开始的时刻
                                                                // 通常用在一些经济驾驶模式（ECO Drive）等相关的应用中
                                                                // 单位：0.1s
    nextEndUCTTime:ushort;	                                    // 如果当前该灯组状态已开始（未结束），则该数值表示该灯组状态下一次开始后再结束的估计时刻
                                                                // 如果当前该灯组状态未开始，则表示该灯组状态第二次开始后再结束的估计时刻
                                                                // 与nextStartUTCTime 配合使用，通常用在一些经济驾驶模式（ECO Drive）等相关的应用中
                                                                // 单位：0.1s
}
