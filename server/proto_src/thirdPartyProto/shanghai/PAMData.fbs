include "Position3D.fbs";
include "RoadPoint.fbs";
include "PositionOffsetLLV.fbs";

namespace MECData;

// @brief 场站支持的自动泊车类型
enum DE_AVPType: uint8 {
    p0 = 0,                                         // 原始停车场
    p1 = 1,                                         // 标准停车场
    p2 = 2,                                         // 具备身份识别功能的停车场
    p3 = 3,                                         // 具备路侧基础设施的停车场
    p4 = 4,                                         // 具备路侧基础设施和V2X设施的停车场
    p5 = 5                                          // 具备自动泊车功能停车场
}

// @brief 场站地图中的停车场信息。
table DF_ParkingLotInfo {
    id: uint16;                                     // 停车场ID
    name: string;                                   // 车位名称
    number: uint16;                                 // 停车位数量
    building_layer_num: uint8;                      // 停车场建筑层数
    avp_type: DE_AVPType;                           // 场站支持的自动泊车类型
}

// @brief 场站内部行驶路线转向
table DF_PAMMovement {
    nodes: [uint16];                                // 通往下游节点合集
}

// @brief 停车位位置，左上角和右上角连成的边为停车位的入口
table DF_ParkingSlotPosition {
    top_left: DF_PositionOffsetLLV;                 // 左上顶点相对位置
    top_right: DF_PositionOffsetLLV;                // 右上顶点相对位置
    bottom_left: DF_PositionOffsetLLV;              // 左下顶点相对位置
}

// @brief 停车位状态
enum DE_SlotStatus: uint8 {
    UNKNOWN = 0,                                    // 未知
    AVAILABLE = 1,                                  // 可用
    OCCUPIED = 2,                                   // 已占用
    RESERVED = 3                                    // 已保留
}

// @brief 停车位角度
enum DE_ParkingSpaceTheta: uint8 {
    UNKNOWN = 0,                                    // 未知
    VERTICAL = 1,                                   // 垂直式
    SIDE = 2,                                       // 侧式
    OBLIQUE = 3                                     // 斜式
}

// @brief 停车位上锁状态
enum DE_ParkingLock: uint8 {
    UNKNOWN = 0,                                    // 未知
    NOLOCK = 1,                                     // 无锁
    LOCKED = 2,                                     // 已上锁
    UNLOCKED = 3                                    // 已开锁
}

// @brief 停车位
table DF_ParkingSlot {
    slot_id: uint16;                                // 停车位ID
    position: DF_ParkingSlotPosition;               // 停车位位置
    sign: string;                                   // 停车位名称，如“B101”
    parking_type: uint16;                           // 停车位类型，含对应属性的对应二进制位取1：
                                                    // 0 = 未知
                                                    // 1 = 一般
                                                    // 2 = 残疾人专用
                                                    // 3 = 小型
                                                    // 4 = 附属车位(attached)
                                                    // 5 = 充电
                                                    // 6 = 立体
                                                    // 7 = 女士专用
                                                    // 8 = 超大
                                                    // 9 = 私人
    status: DE_SlotStatus;                          // 停车位状态
    parking_space_theta: DE_ParkingSpaceTheta;      // 停车位角度
    parking_lock: DE_ParkingLock;                   // 停车位上锁类型
}

// @brief 场站内部行驶路线
table DF_PAMDrive {
    upstream_pamnode_id: uint16;                    // 上游节点id
    drive_id: uint8;                                // 行驶路线id
    two_way_seperation: bool;                       // 是否为对向分隔
    speed_limit:  uint16;                           // 限速，单位：0.02 m/s，取值范围0~8191
    height_restriction: uint8;                      // 限高，单位：0.1m
    drive_width: uint8;                             // 宽度，单位：1cm
    points: [DF_RoadPoint];                         // 位置点集合
    movements: [DF_PAMMovement];                    // 行驶路线转向
    parking_slots: [DF_ParkingSlot];                // 目的停车位
}

// @brief 场站内部地图节点
table DF_PAMNode {
    id: uint16;                                     // 节点ID
    ref_pos: DF_Position3D;                         // 参考经纬度
    floor: int8;                                    // 楼层
    attributes: uint8;                              // 场站地图节点属性，含对应属性的对应二进制位取1：
                                                    // 0 = 入口
                                                    // 1 = 出口
                                                    // 2 = 上楼层
                                                    // 3 = 下楼层
                                                    // 4 = ETC电子收费
                                                    // 5 = MTC人工收费
                                                    // 6 = 先付款后出场
                                                    // 7 = 已阻塞/已禁用
    in_drives: [DF_PAMDrive];                       // 连接到此节点的所有道路的集合
}

// @brief 单车停车区域路径引导信息
table DF_ParkingGuide {
    veh_id: [uint8];                                // 引导车辆的OBU ID
    drive_path: [uint16];                           // 引导路径经过的节点ID从路径起点到终点排列
    target_parking_slot: uint16;                    // 目标停车位ID，如并非前往停车位则填写无效值0
}

// @brief 场站内部地图的消息。包含了场站内道路消息、车位信息等，以及路侧向车辆提供的场站内引导信息。
table MSG_PAMData {
    moy: uint32;                                    // 今年已经过去的分钟数
    parking_lot_info: [DF_ParkingLotInfo];          // 停车场基本信息
    pam_nodes: [DF_PAMNode];                       // 停车场内部地图
    parking_area_guidance: [DF_ParkingGuide];       // 车辆引导信息
}

root_type MSG_PAMData;
