namespace MECData;

// @brief ZBRS服务OTA更新指令，区别于更新应用模块的指令

table MSG_RootOTA {
    version_major: uint16;                      // 主版本号
    version_minor: uint16;                      // 次版本号
    version_patch: uint16;                      // 补丁版本号
    version_pre_release: string;                // 预发布版本号
    build_date: uint32;                         // 构建日期，毫秒级UNIX时间戳
    sha_short_8: uint32;                        // git commit id 后8位
    checksum_sha256: string;                    // OTA资源包文件SHA256摘要，用于下载后校验
    download: bool = true;                      // MEC是否需要从OTA服务器下载OTA资源包
    msg_id: int64;                              // 雪花ID
}

root_type MSG_RootOTA;
