include "NodeReferenceID.fbs";
include "RegulatorySpeedLimit.fbs";
include "RoadPoint.fbs";
include "Movement.fbs";
include "Lane.fbs";

namespace MECData;

// @brief 有向道路
table DF_Link {
    name:string;                                        // 名称
    upstreamNodeId:DF_NodeReferenceID(required);        // 上游交叉口
    speedLimits:[DF_RegulatorySpeedLimit];              // 限速信息
    laneWidth:ushort;                                   // 车道宽度，单位：cm
    points:[DF_RoadPoint];                              // 参考线
    movements:[DF_Movement];                            // 下游转向关系
    lanes:[DF_Lane](required);                          // 包含车道
}
