namespace MECData;

// @brief 车辆的入场请求类型

table DE_ParkingRequest {                            
    parkingRequest:short;       //存在下列情况时，字段对应二进制位置1
                                //enter=0，入场
                                //exit=1，出场
                                //park=2，停车
                                //pay=3，支付
                                //unloadPassenger=4，下客
                                //pickupPassenger=5，上客
                                //unloadCargo=6，卸货
                                //loadCargo=7，装货
                                //reserved1=8，保留
                                //reverved2=9，保留
                                //reserved3=10，保留
                                //reverved4=11，保留
}
