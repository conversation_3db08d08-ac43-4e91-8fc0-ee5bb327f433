namespace MECData;

// @brief 数据流统计1分钟粒度记录

table DE_TimepointCount {
    gen_time: int64;                    // 记录生成时刻毫秒级UNIX时间戳
    count: uint32;                      // ZBRS转发在1min内转发记录条数
}

// @brief 数据流统计记录

table DF_PacketFlowCount {
    data_type: uint16;                  // 数据类型代号
    reserved: bool;                     // 是否为MECData标准数据类型
    src_module_id: uint16 = 65535;      // 数据来源应用模块实例ID
    dst_module_id: uint16 = 65535;      // 数据去向应用模块实例ID
    counts: [DE_TimepointCount];        // 持续30min的1min粒度数据流统计
    description: string;                // 描述
}

// @brief 数据流统计

table DF_PacketFlowRecord {
    flow_counts: [DF_PacketFlowCount];  // 数据流统计记录
}
