include "NodeReferenceID.fbs";
include "SignalControlMode.fbs";

namespace MECData;

// @brief 实际执行相位阶段
table DF_PhasicExec {
    phasic_id: int32 = 0;               // 相位阶段ID，不可从信号机读出时按相序顺序编号
    order: uint8 = 255;                 // 相位阶段在信号控制方案中出现的序号，从0起递增
    movements: [string] (required);     // 获得路权的转向
    green: int16 = 0;                   // 绿灯时间，单位：s
    yellow: int16 = 0;                  // 黄灯时间，单位：s；在下一相位阶段仍有路权的不统计
    allred: int16 = 0;                  // 全红时间，单位：s；在下一相位阶段仍有路权的不统计
}

// @brief 信号执行情况
table MSG_SignalExecution {
    node_id: DF_NodeReferenceID (required);     // 交叉口ID
    sequence: uint32 = 0;                       // 监控到的周期序号
    control_mode: DE_SignalControlMode;         // 实际信号控制模式
    cycle: int16 = 0;                           // 周期，单位：s
    base_signal_scheme_id: int32 = 0;           // 基础信号控制方案ID
    start_time: int64 = 0;                      // 周期开始时刻，秒级UNIX时间戳
    phases: [DF_PhasicExec];                    // 实际执行的相位阶段，按相序排列
    msg_id: int64;                              // 消息雪花ID
}

root_type MSG_SignalExecution;
