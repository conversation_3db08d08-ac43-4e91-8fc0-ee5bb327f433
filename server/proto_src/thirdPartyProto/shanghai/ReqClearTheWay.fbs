include "NodeReferenceID.fbs";
include "ReferencePath.fbs";
include "DDateTime.fbs";

namespace MECData;

// @brief 清空道路请求信息

table DF_ReqClearTheWay {
    upstreamNode: DF_NodeReferenceID(required);             // 清空道路上游交叉口
    downstreamNode: DF_NodeReferenceID(required);           // 清空道路下游交叉口
    targetLane: ubyte = 255;                                // 目标清空车道
    relatedPath: DF_ReferencePath;                          // 清空道路区间详情
    tBegin: DF_DDateTime;                                   // 清空开始时刻
    tEnd: DF_DDateTime;                                     // 清空结束时刻
    target_lane_ext_id: string;                             // 目标清空车道扩展ID
}
