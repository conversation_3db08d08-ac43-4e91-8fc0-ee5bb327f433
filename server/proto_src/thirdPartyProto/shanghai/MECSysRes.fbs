namespace MECData;

// @brief 进程状态

enum DE_ProcessState: uint8 {
    R = 0,      // running
    S = 1,      // sleeping
    D = 2,      // sleeping in an uninterruptible wait
    Z = 3,      // zombie
    T = 4,      // traced or stopped
    W = 5       // paging
}

// @brief CPU核状态

table DE_CPUCore {
    id: uint8;                              // 核序号，-1预留为表示整个CPU的运行状态
    occupancy: uint16;                      // 占用率，单位：0.01%
    temperature: uint8;                     // 温度，单位：0.1C
    vendor: string;                         // 制造商
    model: string;                          // 型号
    clock_speed: uint16 = 65535;            // 时钟频率，单位0.01MHz，65535为无效值
    max_clock_speed: uint16 = 65535;        // 最大时钟频率，单位0.01MHz，65535为无效值
}

// @brief GPU或AI卡状态

table DF_GPU {
    name: string;                           // 名称
    occupancy: uint16;                      // 占用率，单位：0.01%
    temperature: uint8;                     // 温度，单位：0.1C
}

// @brief 进程状态

table DF_ProcessStatus {
    pid: uint16;                            // 进程PID
    state: DE_ProcessState;                 // 进程状态
    name: string;                           // 进程名称或命令行
    cpu: uint16;                            // 进程CPU占用率，单位：0.01%
    mem: uint64;                            // 进程占用内存大小，单位：kB
}

// @brief 本地储存状态

table DF_LocalStorage {
    file_system: string;                    // 文件系统名称
    mount_point: string;                    // 挂载点
    total_blocks: uint64;                   // 块总数
    used_blocks: uint64;                    // 已用块数量
    available_blocks: uint64;               // 可用块数量
    used: uint8;                            // 占用率，单位：1%
}

// @brief MEC计算资源占用监控

table MSG_MECSysRes {
    time: uint64;                           // 消息生成时刻的毫秒级UNIX时间戳
    cycle: uint8;                           // 数据统计周期，单位：s
    cpu: uint16;                            // CPU占用率，单位：0.01%
    mem_occupied: uint64;                   // 内存占用，单位：Bytes
    mem_total: uint64;                      // 总内存大小，单位：Bytes
    storage: uint64;                        // 储存占用率，单位：0.01%
    processes: [DF_ProcessStatus];          // 进程详情
    gpus: [DF_GPU];                         // GPU详情
    cpus: [DE_CPUCore];                     // CPU详情
    gpu: uint16;                            // GPU占用率，单位：0.01%
    storages: [DF_LocalStorage];            // 储存占用详情
    msg_id: int64;                          // 消息雪花ID
}

root_type MSG_MECSysRes;
