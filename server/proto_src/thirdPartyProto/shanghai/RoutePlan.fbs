include "NodeReferenceID.fbs";

namespace MECData;

// @brief 路径规划
table MSG_RoutePlan {
    ptc_id: uint16 = 0;             // 交通参与者ID
    trip_request_id: int32 = 0;     // 路径规划请求ID
    travel_distance: int16 = 0;     // 规划路径全长，单位：0.01 km
    travel_time: int16 = 0;         // 预计出行时间，单位：0.1 minutes
    links: [string];                // 途径道路扩展ID，依路径先后经过顺序排列
    nodes: [DF_NodeReferenceID];    // 途径交叉口，依路径先后经过顺序排列
    msg_id: int64;                  // 消息雪花ID
}

root_type MSG_RoutePlan;
