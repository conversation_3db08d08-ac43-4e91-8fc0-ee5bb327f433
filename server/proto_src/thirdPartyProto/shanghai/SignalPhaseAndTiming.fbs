include "IntersectionState.fbs";
include "DebugTimeRecords.fbs";

namespace MECData;

// @brief 信号灯消息
table MSG_SignalPhaseAndTiming {
    moy:uint;                                                // 今年已经过去的分钟数
    timeStamp:ushort;                                        // 当前分钟已经过去的毫秒数
    name:string;                                             // SPAT数据名称
    intersections:[DF_IntersectionState](required);          // 交叉口信号灯状态
    time_records: DF_DebugTimeRecords;                      // 消息模块间流转记录
    msg_id: int64;                                          // 消息雪花ID
}

root_type MSG_SignalPhaseAndTiming;
