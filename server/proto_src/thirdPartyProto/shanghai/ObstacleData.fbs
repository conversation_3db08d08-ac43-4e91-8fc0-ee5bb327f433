include "PositionOffsetLLV.fbs";
include "PositionConfidenceSet.fbs";
include "HeadingConfidence.fbs";
include "SpeedConfidence.fbs";
include "ObstacleType.fbs";
include "SourceType.fbs";
include "ObjectSize.fbs";
include "SizeValueConfidence.fbs";
include "Polygon.fbs";

namespace MECData;

table DF_ObstacleData {
    obs_type: DE_ObstacleType = unknown;        // 障碍物类型
    obj_type_confidence: uint8;                 // 障碍物类型置信度，单位：0.5%
    obs_id: uint16;                             // 障碍物id
    source: DE_SourceType;                      // 消息来源
    moy: uint32;                                // 今年已经过去的分钟数
    secmark: uint16;                            // 当前分钟已经过去的毫秒数
    pos: DF_PositionOffsetLLV;                  // 障碍物位置
    pos_confidence: DF_PositionConfidenceSet;   // 障碍物位置置信度
    speed: uint16;                              // 速度，单位：0.02m/s
    speed_confidence: DE_SpeedConfidence;       // 速度置信度
    heading: int16;                             // 航向角，正北偏转角，单位：0.0125°
    heading_confidence: DE_HeadingConfidence;   // 航向角置信度
    ver_speed: uint16;
    ver_speed_confidence: DE_SpeedConfidence;
    size: DF_ObjectSize;                            // 障碍物尺寸
    obj_size_confidence: DE_SizeValueConfidence;    // 障碍物尺寸置信度
    tracking: uint16;                           // 已连续跟踪时间，单位：s
    polygon: DF_Polygon;                        // 3D障碍物顶点列表
}
