include "BasicVehicleClass.fbs";
include "NodeReferenceID.fbs";
include "DebugTimeRecords.fbs";
include "Direction8.fbs";
include "Maneuver.fbs";

namespace MECData;

// @brief 交通流统计结果与检测框绑定关系
table DE_DetectorAreaStatInfo {
    detector_area_id: uint32;           // 检测框id
}

// @brief 交通流统计结果与车道绑定关系
table DE_LaneStatInfo {
    ext_id: string (required);          // 车道唯一扩展ID
    direction: DE_Direction8;           // 车道所属进口道方位
}

// @brief 交通流统计结果与路段绑定关系
table DE_SectionStatInfo {
    ext_id: string (required);          // 路段（Section）唯一扩展ID
}

// @brief 交通流统计结果与道路绑定关系
table DE_LinkStatInfo {
    ext_id: string (required);          // 道路（Link）唯一扩展ID
    direction: DE_Direction8;           // 进口道所属方位
}

// @brief 交通流统计结果与车道间连接绑定关系
table DE_ConnectingLaneStatInfo {
    ext_id: string (required);          // 上下游车道间连接关系（Connecting Lane）唯一扩展ID
}

// @brief 交通流统计结果与车道与下游道路连接绑定
table DE_ConnectionStatInfo {
    ext_id: string (required);          // 车道与下游道路连接关系（Connection）唯一扩展ID
}

// @brief 交通流统计结果与转向绑定关系
table DE_MovementStatInfo {
    ext_id: string (required);          // 转向（Movement）扩展ID
    from_direction: DE_Direction8;      // 转向对应上游进口道方位
    maneuver: DE_Maneuver;              // 转向标记（左转、直行等）
}

// @brief 交通流统计结果与交叉口绑定关系
table DE_NodeStatInfo {
    id: DF_NodeReferenceID (required);  // 交叉口参考ID
}

// @brief 交通流统计结果与地图元素绑定关系
union DE_TrafficFlowStatMapElement {
    DE_DetectorAreaStatInfo,            // 检测框级别元素绑定
    DE_LaneStatInfo,                    // 车道级别元素绑定
    DE_SectionStatInfo,                 // 路段级别元素绑定
    DE_LinkStatInfo,                    // 道路级别元素绑定
    DE_ConnectingLaneStatInfo,          // 车道间连接关系级别绑定
    DE_ConnectionStatInfo,              // 车道与下游道路连接关系级别绑定
    DE_MovementStatInfo,                // 转向级别绑定
    DE_NodeStatInfo                     // 交叉口级别绑定
}

// @brief 交通流固定周期模式统计
table DE_TrafficFlowStatByInterval {
    interval: uint32;                   // 统计时间窗长度，单位：sec
}

// @brief 交通流实际信号周期模式统计
table DE_TrafficFlowStatBySignalCycle {
    sequence: uint32;                   // 实际执行信号控制周期序号
}

// @brief 交通流统计模式
union DE_TrafficFlowStatType {
    DE_TrafficFlowStatByInterval,       // 固定周期模式
    DE_TrafficFlowStatBySignalCycle,    // 实际信控执行周期模式
}

// @brief 交通流信号控制拓展指标
table DF_SignalControlStatExtension {
    phasic_order: uint8;                // 相位阶段序号，从0起递增
    green_start_queue: uint32 = 4294967295;
                                        // 绿初排队长度，单位：0.01 m, 4294967295为默认无效值
    red_start_queue: uint32 = 4294967295;
                                        // 红初排队长度，单位：0.01 m, 4294967295为默认无效值
    green_utilization: uint16 = 65535;  // 绿灯利用率，单位0.01%, 65535为默认无效值
}

// @brief 交通流地图元素拓展指标
table DF_MapElementStatExtension {
    timestamp: uint64;                  // 扩展指标生成时间，UNIX时间戳
    capacity: uint32 = 4294967295;      // 通行能力，单位：0.01 pcu/h, 4294967295为默认无效值
    avg_saturation: uint16 = 65535;     // 平均饱和度，单位：0.01%, 65535为默认无效值
    avg_occupation: uint16 = 65535;     // 平均空间占有率，单位：0.01%, 65535为默认无效值
    avg_time_occupation: uint16 = 65535;
                                        // 平均时间占有率，单位：0.01%, 65535为默认无效值
    avg_green_start_queue: uint32 = 4294967295;
                                        // 平均绿初排队长度，单位：0.01m, 4294967295为默认无效值
    avg_red_start_queue: uint32 = 4294967295;
                                        // 平均红初排队长度，单位：0.01m, 4294967295为默认无效值
    green_utilization: uint16 = 65535;  // 平均绿灯利用率，单位0.01%, 65535为默认无效值
    last_interval_volume: uint64 = 18446744073709551615;
                                        // 上一统计时段内小时流率（不含时间窗重叠部分）
                                        // 单位：0.01 pcu/h, 18446744073709551615为默认无效值
    last_interval_speed_area: uint32 = 4294967295;    
                                        // 上一统计时段内区间平均速度（不含时间窗重叠部分）
                                        // 单位：0.01 m/s, 4294967295为默认无效值
}

// @brief 交通流其他自定义拓展指标
table DF_OtherStatExtension {
    stat_name: string(required);        // 指标名称
    numeric_value: int64 = 0;           // 数值类型指标值
    string_value: string;               // 字符类型指标值
}

// @brief 交通流拓展指标
table DF_TrafficFlowStatExtension {
    map_element: [DF_MapElementStatExtension];      // 地图元素拓展指标合集
    signal: [DF_SignalControlStatExtension];        // 信号控制拓展指标合集
    others: [DF_OtherStatExtension];                // 其他自定义拓展指标合集
}

// @brief 路网元素交通流指标统计结果
table DF_TrafficFlowStat {
    map_element: DE_TrafficFlowStatMapElement;      // 指标绑定路网元素
    ptc_type: uint8 = 255;              // 交通参与者类型：unknown (0), motor (1), non-motor (2), pedestrian (3), rsu (4)
    veh_type: DE_BasicVehicleClass = unknownVehicleClass;
                                        // 车辆类型，非机动车时统一取默认值unknownVehicleClass
    volume: uint64 = 18446744073709551615;
                                        // 小时流率，单位：0.01 pcu/h, 18446744073709551615为默认无效值
    speed_point: uint32 = 4294967295;   // 地点平均速度，单位：0.01 m/s, 4294967295为默认无效值
    speed_area: uint32 = 4294967295;    // 区间平均速度，单位：0.01 m/s, 4294967295为默认无效值
    density: uint64 = 18446744073709551615;
                                        // 密度，单位：0.01 pcu/km/lane, 18446744073709551615为默认无效值
    delay: uint16 = 65535;              // 车均延误，单位：0.1 sec/vehicle, 65535为默认无效值
    queue_length: uint16 = 65535;       // 瞬时排队长度，单位：0.1 m, 65535为默认无效值
    congestion: uint8 = 255;            // 拥堵指数，255默认无效值
    ext: DF_TrafficFlowStatExtension;   // 拓展指标
    occupation: uint16 = 65535;         // 空间占有率，单位：0.01%, 65535为默认无效值
    time_headway: uint32 = 4294967295;  // 车头时距，单位：0.01s, 4294967295为默认无效值
    space_headway: uint32 = 4294967295; // 车头间距，单位：0.01m, 4294967295为默认无效值
    travel_time: uint16 = 65535;        // 车均出行时间，单位：0.1 sec/vehicle, 65535为默认无效值
    green_utilization: uint16 = 65535;  // 绿灯利用率，单位：0.01%, 65535为默认无效值
    saturation: uint16 = 65535;         // 饱和度，单位：0.01%, 65535为默认无效值
    stops: uint8 = 255;                 // 车均停车次数，单位：0.1 times, 255为默认无效值
    queued_vehicles: uint16 = 65535;    // 瞬时排队车辆数，单位：vehicles
    time_occupation: uint16 = 65535;    // 时间占有率，单位：0.01%, 65535为默认无效值
}

// @brief 交通流综合统计信息
table MSG_TrafficFlow {
    node: DF_NodeReferenceID;           // 所属交叉口
    gen_time: uint64;                   // 消息生成时刻，毫秒级UNIX时间戳
    stat_type: DE_TrafficFlowStatType (required);       // 统计模式
    stats: [DF_TrafficFlowStat];        // 统计指标合集
    time_records: DF_DebugTimeRecords;  // 消息模块流转记录
    msg_id: int64;                      // 消息唯一雪花id
}

root_type MSG_TrafficFlow;
