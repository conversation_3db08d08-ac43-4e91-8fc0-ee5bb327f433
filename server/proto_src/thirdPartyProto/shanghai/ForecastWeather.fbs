include "Position3D.fbs";
include "HourlyForecast.fbs";

namespace MECData;

// @brief 天气预告
table MSG_ForecastWeather {
    divisionCode:uint = 4294967295;                             // 行政区代码，见http://www.mca.gov.cn/article/sj/xzqh/2020/
    timezone:short = 32767;                                     // 时区，取值范围：-1200~1200, 例：800=GMT +8:00
    pos:DF_Position3D;                                          // 参考位置
    updateTime:uint = 4294967295;                               // 更新时间，今年已经过去的分钟数
    forecast:[DF_HourlyForecast](required);                     // 小时天气预报
    msg_id: int64;                                              // 消息雪花ID
}

root_type MSG_ForecastWeather;
