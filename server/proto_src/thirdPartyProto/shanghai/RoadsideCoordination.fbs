include "Position3D.fbs";
include "VehicleCoordination.fbs";
include "LaneCoordination.fbs";
include "DebugTimeRecords.fbs";

namespace MECData;

// @brief 路侧协调消息

table MSG_RoadSideCoordination {
    id:uint16;                                                  // 协议接口模块实例ID
    secMark:ushort = 65535;                                     // 当前分钟已过去的毫秒数
    refPos:DF_Position3D(required);                             // 消息参考位置经纬度
    coordinates:[DF_VehicleCoordination];                       // 车辆级协调信息
    laneCoordinates:[DF_LaneCoordination];                      // 道路或车道级协调信息
    time_records: DF_DebugTimeRecords;                          // 消息流转记录
    msg_id: int64;                                              // 消息雪花ID
    rsu_id: [uint8];                                            // RSU ID
}

root_type MSG_RoadSideCoordination;
