include "PositionOffsetLLV.fbs";
include "Description.fbs";
include "ReferencePath.fbs";
include "ReferenceLink.fbs";
include "RSITimeDetails.fbs";
include "EventOperationType.fbs";

namespace MECData;

// @brief 道路交通标志信息（RSI）
table DF_RTSData {
    rtsId:ubyte= 255;                               // RTS消息ID或标志标线ID
    signType:ushort= 65535;                         // 标志标线类型，编号规则见GB 5768.2-2009
    priority:ubyte;                                 // 事件优先级，只使用前3个bit，值B00000000到B11100000表示优先级从低到高
    signPos:DF_PositionOffsetLLV;                   // 标志标线位置
    timeDetails:DF_RSITimeDetails;                  // 消息时效
    description:DF_Description;                     // 描述
    referencePaths:[DF_ReferencePath];              // 关联路径
    referenceLinks:[DF_ReferenceLink];              // 关联道路
    msg_id: int64;                                  // 消息雪花ID
    session_id: int64;                              // 会话ID
    operation_type: DE_EventOperationType;          // 消息转发操作类型
}
