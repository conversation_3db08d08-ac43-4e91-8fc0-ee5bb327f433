namespace MECData;

// @brief 车道被共享的情况
table DF_LaneSharing {
    shareWith:short;            // 存在下列情况时，字段对应二进制位置1
                                // overlappingLaneDescriptionProvided=0，与其他已定义的车道有重合
                                // multipleLanesTreatedAsOneLane=1，多车道合一
                                // otherNonMotorizedTrafficTypes=2，存在其他非机动车（畜力车等）
                                // individualMotorizedVehicleTraffic=3，存在个人机动车
                                // busVehicleTraffic=4，存在公交车
                                // taxiVehicleTraffic=5，存在出租车
                                // pedestriansTraffic=6，存在行人
                                // cyclistVehicleTraffic=7，存在自行车
                                // trackedVehicleTraffic=8，存在有轨车辆
                                // pedestrianTraffic=9，行人交通
}
