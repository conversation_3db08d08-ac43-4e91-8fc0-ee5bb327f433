include "LightState.fbs";

namespace MECData;

// @brief 转向绿灯时间偏移
table DF_PhasicMovementOffset {
    movement_ext_id: string;    // 需要偏移的转向的扩展ID
    offset: int16;              // 时间偏移量，单位：s
}

// @brief 转向绿闪时间长度
table DF_GreenFlashTime {
    movement_ext_id: string;    // 转向的扩展ID
    flash_time: int16;          // 绿闪时间长度，单位：s
}

// @brief 非绿灯获得路权灯色
// 默认所有获得路权过渡到无路权的过渡灯色为黄色，时间长度按黄灯时间计。
table DF_NonGreenROWLightState {
    movement_ext_id: string;    // 获得路权时不显示绿灯的转向的扩展ID
    light: DE_LightState;       // 信号灯组灯色状态
}

// @brief 相位阶段
table DF_Phasic {
    id: int32;                  // 相位阶段ID
    order: uint8;               // 相位阶段在信号控制方案中出现的序号，从0起递增
    scat_no: string;            // SCATS系统序号
    movements: [string];        // 可获得路权的转向的扩展ID
    green: int16;               // 绿灯时长，单位：s
    yellow: int16;              // 黄灯时长，单位：s
    allred: int16;              // 全红时长，单位：s
    min_green: int16;           // 最小绿灯时间，单位：s
    max_green: int16;           // 最大绿灯时间，单位：s
    late_starts: [DF_PhasicMovementOffset];         // 绿灯晚启动转向，无需晚启动的转向不列入
    early_ends: [DF_PhasicMovementOffset];          // 绿灯早结束转向，无需早结束的转向不列入
    non_greens: [DF_NonGreenROWLightState];         // 获得路权时不显示绿灯的转向
    green_flash: [DF_GreenFlashTime];               // 转向绿灯闪烁时间长度，使用默认绿闪时间无需调整的转向不列入
    on_demand: bool = false;    // 感应控制、自适应控制模式时是否作为备用相位
}
