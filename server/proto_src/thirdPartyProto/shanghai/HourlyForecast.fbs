namespace MECData;

// @brief 小时天气预告
table DF_HourlyForecast {
    time: uint= 4294967295;                         // 今年已经过去的分钟数
    description: string;                            // 描述
    weatherCode: ubyte = 255;                       // 天气代码，见https://docs.seniverse.com/api/start/code.html
    temperature: short;                             // 温度，单位：0.1摄氏度
    humidity: ubyte;                                // 湿度，单位：%
    windDirection: ushort;                          // 风向，单位：°，正北偏转角
    windSpeed: ushort;                              // 风速，单位：0.01km/h
    windScale: ubyte;                               // 蒲福风级
    msg_id: int64;                                  // 消息雪花ID
}
