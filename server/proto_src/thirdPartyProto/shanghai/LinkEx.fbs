include "NodeReferenceID.fbs";
include "RegulatorySpeedLimit.fbs";
include "RoadPoint.fbs";
include "MovementEx.fbs";
include "Section.fbs";
include "RoadClass.fbs";
include "Direction8.fbs";

namespace MECData;

// @brief 有向道路
table DF_LinkEx {
    name: string;                           // 名称
    upstreamNodeId: DF_NodeReferenceID (required);      // 上游交叉口
    speedLimits: [DF_RegulatorySpeedLimit];             // 限速
    refLine: [DF_RoadPoint];                // 道路参考线关键点序列，自上游往下游排列
    movements_ex: [DF_MovementEx];          // 下游转向连接关系
    sections: [DF_Section];                 // 路段，自上游往下游排列，断面车道数变化处划分
    ext_id: string;                         // 道路扩展ID
    direction: DE_Direction8 = N;           // 道路所属交叉口进口道方位
    road_class: DE_RoadClass = UNCLASSIFIED;    // 道路分类
    length: uint32;                         // 长度，单位：m
}
