include "Position3D.fbs";
include "ReferencePath.fbs";
include "ReferenceLink.fbs";
include "DebugTimeRecords.fbs";
include "EventOperationType.fbs";

namespace MECData;

// @brief 标志标线信息
table MSG_TrafficSign {
    signId:uint = 4294967295;                       // 标志标线ID
    signType:ushort = 65535;                        // 标志标线类型，编号规则见GB 5768.2-2009
    signPos:DF_Position3D(required);                // 标志标线位置
    description:string;                             // 描述
    timeStart:int;                                  // 生效起始时刻，今年已过去的分钟数
    timeEnd:int;                                    // 生效结束时刻，今年已过去的分钟数
    priority:int;                                   // 事件优先级，只使用前3个bit，值B00000000到B11100000表示优先级从低到高
    referencePaths:[DF_ReferencePath];              // 关联路径
    referenceLinks:[DF_ReferenceLink];              // 关联道路
    pathRadius:ushort;                              // 关联路径影响范围宽度，单位：0.1m
    referenceUpstreamNodesId:[ushort];              // 上游交叉口
    referenceDownstreamNodesId:[ushort];            // 下游交叉口
    referenceLanes:[short];                         // 关联车道
    region:ushort;                                  // 交叉口区域号    
    time_records: DF_DebugTimeRecords;              // 消息模块间流转记录
    msg_id: int64;                                  // 消息雪花ID
    session_id: int64;                              // 会话ID
    operation_type: DE_EventOperationType;          // 转发操作类型
}

root_type MSG_TrafficSign;
