include "Position3D.fbs";
include "ReferPosition.fbs";
include "PositionConfidenceSet.fbs";
include "TransmissionState.fbs";
include "MotionConfidenceSet.fbs";
include "AccelerationSet4Way.fbs";
include "BrakeSystemStatus.fbs";
include "VehicleSize.fbs";
include "VehicleSafetyExtensions.fbs";
include "VehicleClassification.fbs";
include "VehicleCharEx.fbs";
include "TimeConfidence.fbs";
include "NodeReferenceID.fbs";
include "DebugTimeRecords.fbs";
include "SourceType.fbs";

namespace MECData;

table MSG_SafetyMessage {
    ptcType: ubyte = 255;                               // 交通参与者类型
                                                        // 0 = 未知类型
                                                        // 1 = 机动车
                                                        // 2 = 非机动车
                                                        // 3 = 行人
                                                        // 4 = RSU接收BSM获得的V2X交通参与者
    ptcId: ushort = 65535;                              // 交通参与者ID
    obuId: [ubyte];                                     // 交通参与者OBU ID
    source: DE_SourceType = unknown;                    // 数据来源
    device: [uint16](required);                         // 数据来源应用模块id
    plateNo: string;                                    // 车牌号
    moy: uint = 4294967295;                             // 今年已经过去的分钟数
    secMark: ushort = 65535;                            // 当前分钟已过去的毫秒数
    timeConfidence: DE_TimeConfidence;                  // 时间精度
    pos: DF_Position3D(required);                       // 交通参与者实时位置
    referPos: DF_ReferPosition;                         // 交通参与者所在的传感设备坐标
    region: ushort;                                     // 所在交叉口所属区域
    nodeId: DF_NodeReferenceID;                         // 所在交叉口
    sectionId:  uint8;                                  // 所在路段ID
    laneId: int8;                                       // 所在车道ID
    accuracy: DF_PositionConfidenceSet;                 // 实时位置精度
    transmission: DE_TransmissionState;                 // 车辆档位状态
    speed: ushort = 65535;                              // 车辆或其他交通参与者的速度大小，单位：0.02 m/s。数值8191表示无效数值。
    heading: ushort = 65535;                            // 车辆或交通参与者的航向角。为运动方向与正北方向的顺时针夹角，单位：0.0125°
    angle: byte;                                        // 车辆方向盘转角，向右为正，向左为负。单位：1.5°。数值127为无效数值
    motionCfd: DF_MotionConfidenceSet;                  // 运动状态精度
    accelSet: DF_AccelerationSet4Way;                   // 四轴加速度
    brakes: DF_BrakeSystemStatus;                       // 车辆制动状态
    size: DF_VehicleSize;                               // 交通参与者尺寸
    vehicleClass: DF_VehicleClassification;             // 交通参与者类型
    safetyExt: DF_VehicleSafetyExtensions;              // 车辆安全辅助信息
    vehCharEx: DF_VehicleCharEx;                        // 额外车辆特征
    section_ext_id: string;                             // 所在路段扩展ID
    lane_ext_id: string;                                // 所在车道扩展ID
    time_records: DF_DebugTimeRecords;                  // 消息模块流转记录
    msg_id: int64;                                      // 消息雪花ID
}

root_type MSG_SafetyMessage;
