include "Position3D.fbs";
include "IARData.fbs";

namespace MECData;

// @brief 车辆请求及意图

table MSG_VehIntentionAndRequest {
    id:string(required);                            // 车辆ID，同MSG_SafetyMessage的obuId字段
    secMark:ushort = 65535;                         // 当前分钟已过去的毫秒数
    refPos:DF_Position3D(required);                 // 车辆实时位置
    intAndReq:DF_IARData(required);                 // 车辆的行驶计划和请求信息
    msg_id: int64;                                  // 消息雪花ID
    rsu_id: [uint8];                                // 接收消息的RSU ID
}

root_type MSG_VehIntentionAndRequest;
