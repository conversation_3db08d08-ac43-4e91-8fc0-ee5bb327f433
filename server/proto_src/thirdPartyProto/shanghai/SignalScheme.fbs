include "Phasic.fbs";
include "NodeReferenceID.fbs";
include "SignalControlMode.fbs";
include "DebugTimeRecords.fbs";

namespace MECData;

// @brief 信号控制方案来源
enum DE_SignalSchemeSource: uint8 {
    SIGNAL_CONTROLLER = 0,                  // 自信号控制机读取
    ROADSIDE_ALGO = 1,                      // 路侧算法自动生成
    ROADSIDE_MANUAL = 2,                    // 路侧手动生成（含自动生成后手动修改）
    CLOUD_ALGO = 3,                         // 平台算法自动生成
    CLOUD_MANUAL = 4                        // 平台手动生成（含自动生成后手动修改）
}

// @brief 信号控制方案
table MSG_SignalScheme {
    scheme_id: int32;                       // 信号控制方案ID
    node_id: DF_NodeReferenceID;            // 所属交叉口
    cycle: uint16;                          // 周期，单位：s
    control_mode: DE_SignalControlMode;     // 信号控制模式
    min_cycle: uint16;                      // 最小周期长度，单位：s
    max_cycle: uint16;                      // 最大周期长度，单位：s
    base_signal_scheme_id: int32 = 0;       // 基准信号控制方案ID，用于基于已有信号控制方案优化的情况
    offset: int16 = 0;                      // 用于协调控制的相位差，单位：s
    phases: [DF_Phasic];                    // 相位阶段，按出现顺序排列
    time_records: DF_DebugTimeRecords;      // 消息流转记录
    msg_id: int64;                          // 雪花ID
    extra: string;                          // 额外说明
    source: DE_SignalSchemeSource;          // 信号控制方案来源
}

root_type MSG_SignalScheme;
