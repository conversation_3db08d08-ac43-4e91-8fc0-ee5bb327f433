namespace MECData;

// @brief 标线颜色
enum DE_RoadMarkColor : uint8 {
    WHITE = 0,                  // 白
    BLUE = 1,                   // 蓝
    YELLOW = 2,                 // 黄
    GREEN = 3,                  // 绿
    ORANGE = 4,                 // 橙
    RED = 5                     // 红
}

// @brief 标线类型
enum DE_RoadMarkType: uint8 {
    NONE = 0,
    SOLID = 1,                  // 实线
    BROKEN = 2,                 // 虚线
    SOLID_SOLID = 3,            // 双实线
    SOLID_BROKEN = 4,           // 实线+虚线
    BROKEN_SOLID = 5,           // 虚线+实线
    BROKEN_BROKEN = 6,          // 双虚线
    BOTTS_DOTS = 7,             // 路钉
    GRASS = 8,                  // 绿化
    CURB = 9,                   // 路缘
    EDGE = 10                   // 导流岛边缘线等不可侵入空间
}

// @brief 车道标线穿越规则规则
enum DE_RoadMarkCrossRule: uint8 {
    NO_PASSING = 0,             // 不可穿越
    CAUTION = 1,                // 穿越时谨慎驾驶
    NONE = 2                    // 无规则
}

// @brief 车道标线单线信息
table DF_RoadMarkLine {
    length: uint32;                         // 长度，单位：0.01m
    space: uint32;                          // 间隔长度，单位：0.01m
    t_offset: int32;                        // ST坐标系下T轴坐标，单位：0.1m
    s_offset: uint32;                       // ST坐标系下S轴坐标，单位：0.1m
    width: uint32;                          // 线型宽度，单位：0.01m
    color: DE_RoadMarkColor;                // 标线颜色
}

// @brief 标线信息
table DF_RoadMark {
    s_offset: uint32;                       // ST坐标系下S轴坐标，单位：0.1m
    mark_type: DE_RoadMarkType;             // 标线类型
    color: DE_RoadMarkColor;                // 标线颜色
    lane_change_to_left: bool = false;      // 可向左变道
    lane_change_to_right: bool = false;     // 可向右变道
    width: uint8;                           // 宽度，单位：0.01m
    height: uint8;                          // 高度，单位：0.01m
    lines: [DF_RoadMarkLine];               // 所有标线组合
}
