include "PositionOffsetLLV.fbs";
include "PositionConfidenceSet.fbs";
include "AccelerationSet4Way.fbs";
include "VehicleSize.fbs";
include "MotionConfidenceSet.fbs";
include "TransmissionState.fbs";
include "VehicleClassification.fbs";
include "NodeReferenceID.fbs";
include "VehicleCharEx.fbs";
include "ReferPosition.fbs";
include "BrakeSystemStatus.fbs";
include "VehicleSafetyExtensions.fbs";
include "SourceType.fbs";

namespace MECData;

// @brief 交通参与者

table DF_ParticipantData{
    ptcType: ubyte = 255;                                       // 交通参与者类型
                                                                // 0 = 未知类型
                                                                // 1 = 机动车
                                                                // 2 = 非机动车
                                                                // 3 = 行人
                                                                // 4 = RSU接收BSM获得的V2X交通参与者
    ptcId: ushort = 65535;                                      // 交通参与者ID
    source: DE_SourceType = unknown;                            // 数据来源
    id: [ubyte];                                                // 交通参与者OBU ID
    plateNo: string;                                            // 车牌号
    secMark: ushort = 65535;                                    // 当前分钟已过去的毫秒数
    pos: DF_PositionOffsetLLV(required);                        // 交通参与者实时位置相对RSM消息参考位置的偏移
    posConfidence: DF_PositionConfidenceSet(required);          // 实时位置精度
    transmission: DE_TransmissionState;                         // 车辆档位状态
    speed: ushort = 65535;                                      // 车辆或其他交通参与者的速度大小，单位：0.02 m/s。数值8191表示无效数值。
    heading: short = 32767;                                     // 车辆或交通参与者的航向角。为运动方向与正北方向的顺时针夹角，单位：0.0125°
    angle: byte;                                                // 车辆方向盘转角，向右为正，向左为负。单位：1.5°。数值127为无效数值
    motionCfd: DF_MotionConfidenceSet;                          // 运动状态精度
    accelSet: DF_AccelerationSet4Way;                           // 四轴加速度
    size: DF_VehicleSize(required);                             // 交通参与者尺寸
    vehicleClass: DF_VehicleClassification;                     // 交通参与者类型
    referPos: DF_ReferPosition;                                 // 交通参与者所在的传感设备坐标
    device: [uint16](required);                                 // 数据来源应用模块id
    moy: uint = 4294967295;                                     // 今年已经过去的分钟数
    nodeId: DF_NodeReferenceID;                                 // 所在交叉口
    sectionId:  uint8;                                          // 所在路段ID
    laneId: int8;                                               // 所在车道ID
    section_ext_id: string;                                     // 所在路段扩展ID
    lane_ext_id: string;                                        // 所在车道扩展ID
    vehCharEx: DF_VehicleCharEx;                                // 额外车辆特征
    brakes: DF_BrakeSystemStatus;                               // 车辆制动状态
    safetyExt: DF_VehicleSafetyExtensions;                      // 车辆安全辅助信息
    msg_id: int64;                                              // 交通参与者信息雪花ID
}
