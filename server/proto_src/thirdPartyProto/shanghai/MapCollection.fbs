include "NodeReferenceID.fbs";
include "Map.fbs";

namespace MECData;

// @brief 地图序列化格式

enum DE_MapContentType: byte {
    UNKNOWN = 0,                    // 未知
    JSON = 1,                       // JSON
    FLATBUFFERS = 2,                // Flatbuffers
    PROTOBUF = 3                    // Protocol Buffers
}

// @brief 已配置地图记录

table DF_MapContent {
    name: string (required);        // 地图名称
    version: string (required);     // 地图版本号，建议使用语义化版本2.0.0
    time: int64;                    // 更新时间，毫秒级UNIX时间戳
    node: DF_NodeReferenceID;       // 基准交叉口
    map_content_type: DE_MapContentType;        // 地图序列化格式
    map_content: [uint8] (nested_flatbuffer: "MSG_Map", required);      // 地图内容
}

// @brief 地图管理器

table DE_MapCollection {
    maps: [DF_MapContent];          // 已配置地图合集
}
