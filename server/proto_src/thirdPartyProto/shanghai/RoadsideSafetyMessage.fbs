include "Position3D.fbs";
include "ParticipantData.fbs";
include "DebugTimeRecords.fbs";

namespace MECData;

// @brief 路侧安全信息

table MSG_RoadsideSafetyMessage {
    id: uint16;                                         // 消息ID
    refPos: DF_Position3D(required);                    // 参考基点位置
    participants: [DF_ParticipantData](required);       // 交通参与者
    time_records: DF_DebugTimeRecords;                  // 消息模块流转记录
    msg_id: int64;                                      // 消息雪花ID
    rsu_id: [uint8];                                    // RSU id
}

root_type MSG_RoadsideSafetyMessage;
