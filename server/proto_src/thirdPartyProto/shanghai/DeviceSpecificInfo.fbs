include "Position3D.fbs";
include "NodeReferenceID.fbs";
include "Direction8.fbs";
include "SSLCertificate.fbs";
include "CommunicationProtocol.fbs";
include "SignalControlMode.fbs";
include "SignalControllerConfig.fbs";

namespace MECData;

// @brief 摄像头外参
table DF_CamExternalParameters {
    format_info: string;                            // 外参格式标记
    version: string;                                // 版本号
    data: [uint8];                                  // 外参BLOB数据
}

// @brief 摄像头特异性信息
table DF_CameraModuleInfo {
    node: DF_NodeReferenceID;                       // 关联交叉口
    pos: DF_Position3D;                             // 安装位置
    direction: ushort;                              // 安装朝向，正北偏转角，单位：0.1°
    direction8: DE_Direction8;                      // 设置进口道朝向
    user: string;                                   // 用户名
    password: string;                               // 密码
    stream_address: string;                         // 流媒体拉流地址
    external_parameters: [DF_CamExternalParameters];    // 摄像头外参
    perception_radius: uint64;                      // 感知范围半径，单位：0.01m
}

// @brief 毫米波雷达特异性信息
table DF_MMWRadarModuleInfo {
    node: DF_NodeReferenceID;                       // 关联交叉口
    pos: DF_Position3D;                             // 安装位置
    direction: ushort;                              // 安装朝向，正北偏转角，单位：0.1°
    direction8: DE_Direction8;                      // 设置进口道朝向
    perception_radius: uint64;                      // 感知范围半径，单位：0.01m
}

// @brief 信号机支持指令
table DE_TrafficSignalSupportedCommand {
    full_signal_scheme: bool;                       // 支持下发完整信号控制方案
    phase_jump: bool;                               // 支持跳相位
    step_control: bool;                             // 支持步进控制
    scheme_constraints: bool;                       // 支持接收信控方案约束
}

// @brief 信号机即时消息
table DF_TrafficSignalStatusInfo {
    current_schedule_id: uint8 = 255;               // 当前生效调度表ID
    current_day_plan_id: uint8 = 255;               // 当前日计划ID
    current_mode: DE_SignalControlMode;             // 当前信号控制模式
    current_scheme: int32;                          // 当前正在运行的信号控制方案id
}

// @brief 信号机特异性信息
table DF_TrafficSignalModuleInfo {
    node: DF_NodeReferenceID;                                   // 关联交叉口
    supported_instructions: DE_TrafficSignalSupportedCommand;   // 信号机支持指令
    status_details: DF_TrafficSignalStatusInfo;                 // 信号机即时消息
    config: DF_SignalControllerConfig;                          // 信号机配置
    scheme_constraints: [DF_SignalSchemeConstraint];            // 信控方案约束
}

// @brief 支持V2X消息标准版本
enum DE_V2XMessageStandard: byte {
    T_CSAE_53_2020 = 0,                             // T/CSAE 53-2020
    T_CSAE_157_2020 = 1                             // T/CSAE 157-2020
}

// @brief V2X标准消息代号（RSU协议用）
enum DE_V2XMsgType: uint16 {
    UNKNOWN = 0x0000,                                   // 未知
    BSM = 0x2201,                                       // BSM
    RSM = 0x2202,                                       // RSM
    MAP = 0x2203,                                       // MAP
    SPAT = 0x2204,                                      // SPAT
    RSI_STATIC = 0x2205,                                // RSI-静态应用
    RSI_HALF_DYNAMIC = 0x2206,                          // RSI-半动态应用
    RSI_DYNAMIC = 0x2207,                               // RSI-动态应用
    RSC = 0x2208,                                       // RSC
    VIR = 0x2209,                                       // VIR
    PAM = 0x220A,                                       // PAM
    CLPMM = 0x220B,                                     // CLPMM
    SSM = 0x220C,                                       // SSM
    PSM = 0x220D,                                       // PSM
    VPM = 0x220E,                                       // VPM
    TEST = 0x22FF                                       // TEST
}

// @brief V2X消息收发权限
table DF_V2XMsgConfig {
    msg_type: DE_V2XMsgType;                            // 消息类型
    send: bool = true;                                  // 广播权限是否开启
    recv: bool = true;                                  // 接收权限是否开启
}

// @brief V2X消息广播权限配置
table DF_RSUBroadcastConfig {
    msg_configs: [DF_V2XMsgConfig];                     // 消息收发权限控制
    bsm_upload: bool;                                   // BSM自动向上位机上传
    status_upload_interval: uint8;                      // RSU工作状态自动上传时间间隔，单位：s
                                                        // 取0时关闭自动上传
}

// @brief RSU工作状态
table DF_RSUStatus {
    service_status: uint8;                              // RSU核心服务运行状态代码
    cellular_status: uint8;                             // 蜂窝网络状态代码
    ntp_status: uint8;                                  // NTP授时状态代码
    ptp_status: uint8;                                  // PTP授时状态代码
    gnss_status: uint8;                                 // GNSS工作状态代码
    v2x_status: uint8;                                  // V2X工作状态代码
    can_status: uint8;                                  // CAN模组工作状态代码
    ca_status: uint8;                                   // CA证书状态代码
    env_status: uint8;                                  // 工作环境状态代码
}

// @brief RSU特异性信息
table DF_RSUModuleInfo {
    rsu_id: [uint8];                                    // RSU ID
    supported_v2x_standards: [DE_V2XMessageStandard];   // 支持的所有V2X标准消息版本
    status: DF_RSUStatus;                               // RSU工作状态
    broadcast_config: DF_RSUBroadcastConfig;            // RSU广播权限配置
}

// @brief 平台特异性信息
table DF_CloudInfo {
    host: string;                   // 平台主机号或IP
    port: uint16;                   // 平台端口号
    communication_protocol: DE_CommunicationProtocol;   // 通信协议
}

// @brief 激光雷达特异性信息

table DF_LidarModuleInfo {
    node: DF_NodeReferenceID;                       // 关联交叉口
    pos: DF_Position3D;                             // 安装位置
    direction: uint16 = 65535;                      // 安装朝向，正北偏转角，单位：0.1°，360°扫描无固定朝向时填写默认值65535（无效值）
    direction8: DE_Direction8;                      // 设置进口道朝向
    perception_radius: uint64;                      // 感知范围半径，单位：0.01m
    scan_frequency: uint32;                         // 扫描频率，单位0.01Hz
    horizonal_field_of_view: uint16 = 65535;        // 横向视场角，单位：0.1°，无效值65535
    horizonal_resolution: uint32 = 4294967295;      // 横向分辨率，单位：0.01°，无效值65535
    vertical_field_of_view: uint16 = 65535;         // 纵向视场角，单位：0.1°，无效值65535
    vertical_resolution: uint32 = 4294967295;       // 纵向分辨率，单位：0.01°，无效值65535
}

// @brief 外接设备特异性信息，应根据对接设备实际类型选择对应类型的特异性信息
union DF_DeviceSpecificInfo {
    DF_CameraModuleInfo,            // 摄像头
    DF_MMWRadarModuleInfo,          // 毫米波雷达
    DF_TrafficSignalModuleInfo,     // 信号机
    DF_RSUModuleInfo,               // RSU
    DF_CloudInfo,                   // 平台
    DF_LidarModuleInfo              // 激光雷达
}
