include "NodeReferenceID.fbs";
include "BasicVehicleClass.fbs";
include "Position3D.fbs";

namespace MECData;

// @brief 专用车道控制方案
table DF_DLScheme{
    scheme_id: int32 = 2147483647;                          // 专用车道控制方案ID
    intersection: DF_NodeReferenceID;                       // 所属交叉口
    classes: [DE_BasicVehicleClass];                        // 控制对象车辆，取unknownVehicleClass应用于所有车型
    extra_filter: string;                                   // 其他车型筛选条件
    section: uint8;                                         // 路段ID
    lane_ref_id: int8;                                      // 车道参考ID
    vms_ext_id: string;                                     // 可变情报版扩展ID
    lane_ext_id: string;                                    // 车道扩展ID
    ref_position: DF_Position3D;                            // 消息参考点
    radius: uint16 = 0;                                     // 控制范围半径，单位：0.1m
    expires_in: uint16 = 65535;                             // 控制方案有效期长度，单位：min，取0则保持生效
}
