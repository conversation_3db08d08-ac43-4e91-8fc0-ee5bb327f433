namespace MECData;

// @brief 驾驶行为类型

table DE_DriveBehavior {                                                    
    behavior:short;     // 存在下列情况时，字段对应二进制位置1
                        // goStraightForward=0，直行
                        // laneChangingToLeft=1，向左变更车道
                        // laneChangingToRight=2，向右变更车道
                        // rampIn=3，驶入
                        // rampOut=4，驶出
                        // intersectionStraightThrough=5，直行通过交叉口
                        // intersectionTurnLeft=6，左转通过交叉口
                        // intersectionTurnRight=7，右转通过交叉口
                        // intersectionUTurn=8，调头通过交叉口
                        // stopandgo=9，停车让行
                        // stop=10，停止
                        // slowdown=11，减速慢行
                        // speedup=12，加速行驶
                        // parking=13，泊车
}
