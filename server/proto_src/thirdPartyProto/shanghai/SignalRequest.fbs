include "SignalScheme.fbs";
include "SignalControlMode.fbs";
include "DebugTimeRecords.fbs";
include "SignalControllerConfig.fbs";

namespace MECData;

// @brief 方案级控制指令
table DF_SignalRequestByScheme {
    signal_scheme: [uint8](nested_flatbuffer: "MSG_SignalScheme");      // 信号控制方案
}

// @brief 相位级控制指令：相位跳转、延长、早断
table DF_SignalRequestByPhase {
    next_phasic_id: int32;                  // 指示需要跳转的相位阶段号
                                            // 可填写当前相位阶段号，用于延长当前相位，延长时间由effective_time字段指明
    effective_time: uint8 = 0;              // 控制指令等待生效时间，单位：s
                                            // 跳转到非当前相位阶段时，填写0则立即生效
}

// @brief 相位级控制指令：相位跳转、延长、早断
table DF_SignalRequestPhaseActivate {
    phasic_id: int32;                       // 需要激活的相位id
}

// @brief 信号机关键配置参数下发指令
table DF_SignalRequestByConfig {
    config: DF_SignalControllerConfig;      // 信号机参数配置
}

// @brief 信号控制方案约束下发指令
table DF_SignalRequestBySchemeConstraint {
    scheme_constraints: [DF_SignalSchemeConstraint];      // 信号控制方案约束
}

// @brief 信号控制请求详情
union DF_SignalRequestDetails {
    DF_SignalRequestByScheme,               // 方案级控制
    DF_SignalRequestByPhase,                // 相位级控制：相位跳转、延长、早断
    DF_SignalRequestPhaseActivate,          // 相位级控制：备用相位激活
    DF_SignalRequestByConfig,               // 信号机关键配置参数下发
    DF_SignalRequestBySchemeConstraint      // 信号控制方案约束下发
}

// @brief 信号控制请求来源
enum DE_SignalRequestSource: uint8 {
    UNKNOWN = 0,                            // 未知
    LOCAL_ALGORITHM = 1,                    // MEC本地算法自动下发
    LOCAL_DASHBOARD = 2,                    // MEC本地web配置界面下发
    DEVICE_INPUT = 3,                       // 外接设备下发（行人过街按钮等）
    CLOUD_ALGORITHM = 4,                    // 云端算法自动下发
    CLOUD_DASHBOARD = 5                     // 云端界面手动下发
}

// @brief 信号控制请求
table MSG_SignalRequest {
    control_mode: DE_SignalControlMode;     // 信号机控制模式
    details: DF_SignalRequestDetails;       // 信号控制请求详情
    time_records: DF_DebugTimeRecords;      // 消息流转记录
    msg_id: int64;                          // 雪花ID
    source: DE_SignalRequestSource;         // 信控请求来源
}

root_type MSG_SignalRequest;
