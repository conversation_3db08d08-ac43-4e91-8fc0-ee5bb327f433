namespace MECData;

// @brief 日期与时间

table DF_DDateTime {
    year: ushort;                                     // 公元年，取值范围：0~4095
    month: ubyte;                                     // 月，取值范围：0~12
    day: ubyte;                                       // 日，取值范围：0~31
    hour: ubyte;                                      // 时，取值范围：0~24
    minute: ubyte;                                    // 分，取值范围：0~60
    second: ushort;                                   // 毫秒，取值范围：0~60000
    offset: short;                                    // 所用时区偏离UTC时间的分钟数，取值范围：-720~721
}
