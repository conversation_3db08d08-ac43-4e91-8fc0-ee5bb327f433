namespace MECData;

// @brief 单点车速引导值
table DE_GuideSpeedPointValue {
    speed: int32;               // 引导车速，单位：0.1m/s
}

// @brief 区间车速引导值
table DE_GuideSpeedIntervalValue {
    speed_lbound: int32;        // 引导车速区间上界，单位：0.1m/s
    speed_ubound: int32;        // 引导车速区间下界，单位：0.1m/s
}

// @brief // 引导车速详情
union DF_GuideSpeedValue {
    DE_GuideSpeedPointValue,    // 单点车速值
    DE_GuideSpeedIntervalValue  // 区间车速值
}

// @brief 车速引导指令详情
table DF_SpeedGuideInfo {
    time: int64;                // 引导参考时刻，毫秒级UNIX时间戳
    speed: int32;               // 引导车速，单位：0.1m/s
    guide: DF_GuideSpeedValue;  // 引导车速详情
}

// @brief 车辆车速引导
table MSG_SpeedGuide {
    mec_id: string;             // 引导用MEC ID
    veh_id: uint16;             // 车辆ID
    veh_id_ext: string;         // 车辆扩展ID
    time: int64;                // 消息毫秒级UNIX时间戳
    guide_info: [DF_SpeedGuideInfo];    // 引导指令详情
    msg_id: int64;              // 消息雪花ID
}

root_type MSG_SpeedGuide;
