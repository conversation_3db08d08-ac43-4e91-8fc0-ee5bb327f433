namespace MECData;

// @brief 用户权限
enum DE_UserPermission: byte {
  NONE =  0,                                            // 无相关配置
  READ_ONLY = 1,                                        // 关键信息只读
  READ_WRITE = 2,                                       // 关键信息可写入
}

// @brief 用户角色
enum DE_UserType: byte {
  ADMIN = 0,                                            // 管理员
  DEVELOPER = 1,                                        // 开发者
  OPERATOR = 2,                                         // 一般操作员
}

// @brief 用户信息
table DF_ZBRSUserConfig {
  username: string;                                     // 用户名
  user_type: DE_UserType = OPERATOR;                    // 用户角色
  user_permission: DE_UserPermission = READ_ONLY;       // 用户权限
}
