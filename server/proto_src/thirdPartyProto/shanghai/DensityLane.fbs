namespace MECData;

// @brief 车道交通流密度
table MSG_DensityLane {
    deviceId: uint16;                                           // 检测传感器对应协议接口模块实例ID
    startDetectAeraId: int32;                                   // 起始检测框ID
    endDetectAeraId: int32;                                     // 终点检测框ID
    moy: uint = 4294967295;                                     // 今年已经过去的分钟数
    secMark: ushort = 65535;                                    // 当前分钟已过去的毫秒数
    cycle: ushort = 65535;                                      // 检测周期，单位：s
    density: ushort= 65535;                                     // 密度，单位：0.01 pcu/km
}

root_type MSG_DensityLane;
