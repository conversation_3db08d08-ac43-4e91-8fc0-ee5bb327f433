include "NodeReferenceID.fbs";
include "BasicVehicleClass.fbs";
include "Position3D.fbs";

namespace MECData;

// @brief 动态限速方案详情
table DF_SpeedLimitScheme{
    scheme_id: int32 = 2147483647;                          // 限速方案ID
    intersection: DF_NodeReferenceID;                       // 所属交叉口
    classes: [DE_BasicVehicleClass];                        // 对象车型
    section: uint8;                                         // 路段ID
    lane_ref_id: int8;                                      // 车道ID
    vms_ext_id: string;                                     // 可变情报板ID
    lane_ext_id: string;                                    // 车道扩展ID
    ref_position: DF_Position3D;                            // 消息参考位置
    radius: uint16 = 0;                                     // 消息影响范围半径，单位：0.1m
    speed_limit: uint16 = 32767;                            // 限速值，单位：0.02 m/s
    expires_in: uint16 = 65535;                             // 消息有效期，单位：min
}
