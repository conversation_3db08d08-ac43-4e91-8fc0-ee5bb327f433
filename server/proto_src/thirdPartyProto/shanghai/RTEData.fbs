include "PositionOffsetLLV.fbs";
include "Description.fbs";
include "ReferencePath.fbs";
include "ReferenceLink.fbs";
include "RSITimeDetails.fbs";
include "EventOperationType.fbs";

namespace MECData;

// @brief 道路交通事件信息（RSI）
table DF_RTEData {
    rteId:ubyte= 255;                               // RTE消息ID或事件ID
    eventType:ushort= 65535;                        // 事件类型，编号规则按GB/T 29100-2012
    eventSource:ubyte= 255;                         // 事件来源
                                                    // 0-未知
                                                    // 1-交警
                                                    // 2-政府机构
                                                    // 3-气象部门
                                                    // 4-互联网服务
                                                    // 5-本地检测
    priority:ubyte;                                 // 事件优先级，只使用前3个bit，值B00000000到B11100000表示优先级从低到高
    eventPos:DF_PositionOffsetLLV;                  // 事件位置
    eventRadius:ushort;                             // 事件影响半径，单位：0.1m
    description:DF_Description;                     // 事件描述
    timeDetails:DF_RSITimeDetails;                  // 事件时效
    referencePaths:[DF_ReferencePath];              // 关联路径
    referenceLinks:[DF_ReferenceLink];              // 关联道路
    eventConfidence:ubyte;                          // 事件置信度，单位：0.5%
    msg_id: int64;                                  // 消息ID
    session_id: int64;                              // 会话ID
    operation_type: DE_EventOperationType;          // 事件转发操作类型
}
