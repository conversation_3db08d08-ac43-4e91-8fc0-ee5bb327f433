include "ReferenceLink.fbs";
include "ReferencePath.fbs";
include "DDateTime.fbs";
include "DriveBehavior.fbs";
include "CoordinationInfo.fbs";
include "Description.fbs";

namespace MECData;

// @brief 对道路或车道的引导信息
table DF_LaneCoordination {
    targetLane:DF_ReferenceLink(required);	                    // 目标车道或者路段信息
    relatedPath:DF_ReferencePath;                               // 关联路径信息
    tBegin:DF_DDateTime;                                        // 协作规划开始时间
    tEnd:DF_DDateTime;                                          // 协作规划结束时间
    recommendedSpeed:short;                                     // 建议车速，单位：0.02m/s
    recommendedBehavior:DE_DriveBehavior;                       // 建议或允许驾驶行为
    info:DE_CoordinationInfo;                                   // 协调场景类型
    description:DF_Description;                                 // 额外描述
}
