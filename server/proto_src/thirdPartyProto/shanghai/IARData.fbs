include "PathPlanningPoint.fbs";
include "DriveBehavior.fbs";
include "DriveRequest.fbs";

namespace MECData;

// @brief 车辆的行驶计划和请求信息

table DF_IARData {
    currentPos:DF_PathPlanningPoint;            // 车辆当前在地图中的位置
    pathPlanning:[DF_PathPlanningPoint];        // 规划的行驶路线，按时序先后排列
    currentBehavior:DE_DriveBehavior;           // 规划的行驶路线相关的驾驶行为
    reqs:[DF_DriveRequest];                     // 请求消息
}
