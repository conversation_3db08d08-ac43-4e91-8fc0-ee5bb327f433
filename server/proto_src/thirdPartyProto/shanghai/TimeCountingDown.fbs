namespace MECData;

// @brief 用倒计时形式，描述一个信号灯灯组状态的完整计时状态
table DF_TimeCountingDown {
    startTime:ushort = 65535;                                   // 如果当前该灯组状态已开始（未结束），则该数值为0
                                                                // 如果当前该灯组状态未开始，则表示当前时刻距离该灯组状态下一次开始的时间
                                                                // 单位：0.1s
    minEndTime:ushort;                                          // 表示当前时刻距离该灯组状态下一次结束的最短时间（不管当前时刻该灯组状态是否开始）
                                                                // 单位：0.1s
    maxEndTime:ushort;                                          // 表示当前时刻距离该灯组状态下一次结束的最长时间（不管当前时刻该灯组状态是否开始）
                                                                // 单位：0.1s
    likelyEndTime:ushort = 65535;                               // 表示当前时刻距离该灯组状态下一次结束的估计时间（不管当前时刻该灯组状态是否开始）
                                                                // 如果该信号灯灯组是定周期、固定时长，则该数值就表示当前时刻距离该灯组状态下一次结束的准确时间
                                                                // 如果信号灯当前灯组是非固定配时（感应配时、手动控制等），则该数值表示预测的结束时间，且预测时间必须在minEndTime 和maxEndTime 之间，可能由历史数据或一些事件触发等来进行预测
    timeConfidence:ushort;                                      // 上述likelyEndTime 预测时间的置信度水平，单位：0.1s
    nextStartTime:ushort;                                       // 如果当前该灯组状态已开始（未结束），则该数值表示当前时刻距离该灯组状态下一次开始的估计时长
                                                                // 如果当前该灯组状态未开始，则表示当前时刻距离该灯组状态第二次开始的时间
                                                                // 通常用在一些经济驾驶模式（ECO Drive）等相关的应用中
                                                                // 单位：0.1s
    nextDuration:ushort;	                                    // 如果当前该灯组状态已开始（未结束），则该数值表示该灯组状态下一次开始后的持续时长
                                                                // 如果当前该灯组状态未开始，则表示该灯组状态第二次开始后的持续时长
                                                                // 与nextStartTime 配合使用，通常用在一些经济驾驶模式（ECO Drive）等相关的应用中。
                                                                // 单位：0.1s
}
