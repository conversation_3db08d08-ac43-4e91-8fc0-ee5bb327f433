include "AppInfo.fbs";
include "ErrorImmediateReport.fbs";
include "ErrorCollection.fbs";
include "AppOTA.fbs";
include "RootOTA.fbs";
include "VolumeLane.fbs";
include "DensityLane.fbs";
include "SpeedLaneArea.fbs";
include "SpeedLanePoint.fbs";
include "TrafficSign.fbs";
include "TrafficEvent.fbs";
include "SafetyMessage.fbs";
include "SignalPhaseAndTiming.fbs";
include "Map.fbs";
include "RoadsideCoordination.fbs";
include "RoadSideInformation.fbs";
include "RoadsideSafetyMessage.fbs";
include "VehIntentionAndRequest.fbs";
include "DedicatedLaneControl.fbs";
include "VariableSpeedLimit.fbs";
include "RampMetering.fbs";
include "NowWeather.fbs";
include "ForecastWeather.fbs";
include "ForecastRain.fbs";
include "SignalScheme.fbs";
include "TrafficFlow.fbs";
include "TrajectoryPoints.fbs";
include "QueueLength.fbs";
include "VariableMessageSign.fbs";
include "CarbonEmission.fbs";
include "SignalExecution.fbs";
include "SignalRequest.fbs";
include "CollisionWarning.fbs";
include "SpeedGuide.fbs";
include "RoutePlan.fbs";
include "PlatooningManagementMessage.fbs";
include "Ticket.fbs";
include "WildCard.fbs";
include "ZBRSFeatures.fbs";
include "MECSysRes.fbs";

namespace MECData;

table DF_AppCustomizedPacket {
    type_code: uint16;
    data: [uint8];
}

table DF_AppInfoPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_AppInfo");
}

table DF_ErrorImmediateReportPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_ErrorImmediateReport");
}

table DF_ErrorCollectionPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_ErrorCollection");
}

table DF_RootOTAPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_RootOTA");
}

table DF_AppOTAPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_AppOTA");
}

table DF_VolumeLanePacket {
    packet: [uint8] (nested_flatbuffer: "MSG_VolumeLane");
}

table DF_DensityLanePacket {
    packet: [uint8] (nested_flatbuffer: "MSG_DensityLane");
}

table DF_SpeedLaneAreaPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_SpeedLaneArea");
}

table DF_SpeedLanePointPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_SpeedLanePoint");
}

table DF_TrafficSignPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_TrafficSign");
}

table DF_TrafficEventPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_TrafficEvent");
}

table DF_SafetyMessagePacket {
    packet: [uint8] (nested_flatbuffer: "MSG_SafetyMessage");
}

table DF_SignalPhaseAndTimingPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_SignalPhaseAndTiming");
}

table DF_MapPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_Map");
}

table DF_RoadSideCoordinationPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_RoadSideCoordination");
}

table DF_RoadSideInformationPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_RoadSideInformation");
}

table DF_RoadsideSafetyMessagePacket {
    packet: [uint8] (nested_flatbuffer: "MSG_RoadsideSafetyMessage");
}

table DF_VehIntentionAndRequestPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_VehIntentionAndRequest");
}

table DF_DedicatedLaneControlPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_DedicatedLaneControl");
}

table DF_VariableSpeedLimitPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_VariableSpeedLimit");
}

table DF_RampMeteringPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_RampMetering");
}

table DF_NowWeatherPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_NowWeather");
}

table DF_ForecastWeatherPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_ForecastWeather");
}

table DF_ForecastRainPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_ForecastRain");
}

table DF_SignalSchemePacket {
    packet: [uint8] (nested_flatbuffer: "MSG_SignalScheme");
}

table DF_TrafficFlowPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_TrafficFlow");
}

table DF_TrajectoryPointsPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_TrajectoryPoints");
}

table DF_QueueLengthPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_QueueLength");
}

table DF_VariableMessageSignPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_VariableMessageSign");
}

table DF_CarbonEmissionPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_CarbonEmission");
}

table DF_SignalExecutionPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_SignalExecution");
}

table DF_SignalRequestPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_SignalRequest");
}

table DF_CollisionWarningPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_CollisionWarning");
}

table DF_SpeedGuidePacket {
    packet: [uint8] (nested_flatbuffer: "MSG_SpeedGuide");
}

table DF_RoutePlanPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_RoutePlan");
}

table DF_PlatooningManagementMessagenPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_PlatooningManagementMessage");
}

table DF_TicketPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_Ticket");
}

table DF_WildCardPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_WildCard");
}

table DF_ZBRSFeaturesPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_ZBRSFeatures");
}

table DF_MECSysResPacket {
    packet: [uint8] (nested_flatbuffer: "MSG_MECSysRes");
}

union DF_PacketContent {
    DF_AppInfoPacket,
    DF_ErrorImmediateReportPacket,
    DF_RootOTAPacket,
    DF_AppOTAPacket,
    DF_VolumeLanePacket,
    DF_DensityLanePacket,
    DF_SpeedLaneAreaPacket,
    DF_SpeedLanePointPacket,
    DF_TrafficSignPacket,
    DF_TrafficEventPacket,
    DF_SafetyMessagePacket,
    DF_SignalPhaseAndTimingPacket,
    DF_MapPacket,
    DF_RoadSideCoordinationPacket,
    DF_RoadSideInformationPacket,
    DF_RoadsideSafetyMessagePacket,
    DF_VehIntentionAndRequestPacket,
    DF_DedicatedLaneControlPacket,
    DF_VariableSpeedLimitPacket,
    DF_RampMeteringPacket,
    DF_NowWeatherPacket,
    DF_ForecastWeatherPacket,
    DF_ForecastRainPacket,
    DF_SignalSchemePacket,
    DF_TrafficFlowPacket,
    DF_TrajectoryPointsPacket,
    DF_QueueLengthPacket,
    DF_VariableMessageSignPacket,
    DF_CarbonEmissionPacket,
    DF_SignalExecutionPacket,
    DF_SignalRequestPacket,
    DF_CollisionWarningPacket,
    DF_SpeedGuidePacket,
    DF_RoutePlanPacket,
    DF_PlatooningManagementMessagenPacket,
    DF_TicketPacket,
    DF_WildCardPacket,
    DF_ZBRSFeaturesPacket,
    DF_MECSysResPacket
}

table MSG_InterceptedPacket {
    source: uint16;
    destinations: [uint16];
    time: int64;                    // Intercepted time, unix timestamp in ms
    packet: DF_PacketContent;
}

root_type MSG_InterceptedPacket;
