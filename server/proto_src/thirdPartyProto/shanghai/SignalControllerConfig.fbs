include "Month.fbs";
include "Weekday.fbs";
include "LocalTimePoint.fbs";
include "SignalControlMode.fbs";
include "PhaseCharacteristics.fbs";
include "PhasicConstraint.fbs";
include "NodeReferenceID.fbs";

namespace MECData;

// @brief 绿冲突配置
table DF_GreenConflicts {
    phase_id: uint8;            // 信号灯组ID
    conflict_phase_ids: [uint8];    // 和信号灯组ID冲突的其他信号灯组ID
}

// @brief 绿间隔配置
table DF_GreenInterval {
    phase_id_end: uint8;        // 失去路权的信号灯组ID
    phase_id_start: uint8;      // 获得路权的信号灯组ID
    interval: int16;            // 绿间隔时间，单位：s
}

// @brief 调度表
table DF_SCSchedule {
    schedule_id: uint8;                     // 调度表id
    month_filter: [DE_Month];               // 月份筛选条件
    day_filter: [uint8];                    // 日筛选条件
    weekday_filter: [DE_Weekday];           // 周日筛选条件
    priority: uint8;                        // 调度表优先级
    day_plan_id: uint8;                     // 对应的日计划id
}

// @brief 日计划时段动作
table DF_DayPlanAction {
    start_time: DF_LocalTimePoint (required);   // 开始时间
    scheme_id: int32;                       // 信号方案id，对应MSG_SignalScheme的id字段
                                            // 信号控制模式由MSG_SignalScheme消息的mode字段控制
    mode: DE_SignalControlMode;             // 时段信号控制模式
}

// @brief 日计划
table DF_DayPlan {
    dayplan_id: uint8;                      // 日计划id
    actions: [DF_DayPlanAction];            // 日计划时段动作
}

// @brief 信号控制机配置
table DF_SignalControllerConfig {
    phases: [DF_PhaseCharacteristics];                      // 信号灯组特征
    green_conflicts: [DF_GreenConflicts];                   // 绿冲突配置，下发配置参数时不填写视为清空绿冲突配置
    green_intervals: [DF_GreenInterval];                    // 绿间隔配置，下发配置参数时不填写视为清空绿间隔配置
    schedules: [DF_SCSchedule];                             // 调度表，下发配置参数时不填写视为清空调度表配置
    day_plans: [DF_DayPlan];                                // 日计划，下发配置参数时不填写视为清空日计划配置
}

// @brief 信号方案约束
table DF_SignalSchemeConstraint {
    scheme_id: int32;                                       // 信号控制方案ID
    node_id: DF_NodeReferenceID;                            // 所属交叉口
    min_cycle: uint16;                                      // 最小周期长度，单位：s
    max_cycle: uint16;                                      // 最大周期长度，单位：s
    offset: int16 = 0;                                      // 用于协调控制的相位差，单位：s
    phasic_constraints: [DF_PhasicConstraint];              // 相位阶段约束，按相位阶段出现顺序排列
}
