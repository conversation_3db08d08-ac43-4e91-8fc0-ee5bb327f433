namespace MECData;

// @brief 应用模块类型

enum DE_ModuleCategory: uint8 {
    DEVICE_INTERFACE = 0,                   // 外设协议接口
    APPLICATION_ALGORITHM = 1               // 一般应用算法
}

// @brief 崩溃重启策略

enum DE_CrashRestartPolicy: uint8 {
    NO_RESTART = 0,                         // 崩溃后不重启
    INFINITE_RESTART = 1,                   // 崩溃后无限重启
    LIMITED_RESTART = 2,                    // 崩溃后有限次重启
}

// @brief 崩溃重启配置

table DF_CrashRestartConfig {
    policy: DE_CrashRestartPolicy;          // 崩溃重启策略
    max_retry: uint16 = 5;                  // 最大重启尝试次数
}

// @brief 应用模块状态

enum DE_ModuleStatus: uint8 {
    UNINITIALIZED = 0,                      // 未初始化
    INITIALIZED = 1,                        // 初始化
    RUNNING = 2,                            // 运行中
    PAUSED = 3,                             // 暂停
    STOPPED = 4,                            // 停止
}

// @brief 应用模块部署类型

enum DE_DeploymentType: uint8 {
    UNKNOWN = 0,                            // 无效类型
    NATIVE_CLASS = 1,                       // 内建
    SHARED_OBJECT = 2,                      // SO型
    DOCKER_IMAGE = 3,                       // Docker型
    STAND_ALONE = 4                         // 独立型
}

// @brief 应用模块Web UI信息

table DF_ModuleWebUI {
    supported: bool;                        // 是否支持Web UI
    name:string;                            // UI入口展示名称
    relative_path: string;                  // Web页面相对URL
}

// @brief 应用模块IO记录

table DF_IORecorder {
    record: bool;                           // 开启IO记录
    record_type_codes: [uint16];            // 所有需要记录的MECData标准消息类型的代号
}
