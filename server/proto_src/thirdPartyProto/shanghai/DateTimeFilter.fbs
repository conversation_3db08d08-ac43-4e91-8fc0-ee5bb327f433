include "Month.fbs";
include "Weekday.fbs";
include "LocalTimePoint.fbs";

namespace MECData;

// @brief 日期与时间筛选条件

table DF_DateTimeFilter {
    month_filter: [DE_Month];               // 月份筛选条件
    day_filter: [uint8];                    // 日筛选条件
    weekday_filter: [DE_Weekday];           // 周日筛选条件
    from_time_point: DF_LocalTimePoint;     // 时段起点时分秒
    to_time_point: DF_LocalTimePoint;       // 时段终点时分秒
}
