namespace MECData;

// @brief 人行横道的属性定义
table DF_LaneAttributesCrosswalk {
                                                             // 存在下列情况时，字段对应二进制位置1
                                                             // crosswalkRevocableLane(0)，动态启用人行横道
                                                             // bicyleUseAllowed(1)，允许自行车使用
                                                             // isXwalkFlyOverLane(2)，是否与其他车道在同一平面
                                                             // fixedCycleTi(3)，被固定周期的信号灯组控制，无需使用按钮
                                                             // biDirectionalCycleTimes(4)，正反方向使用不同的信号灯组
                                                             // hasPushToWalkButton(5)，有行人过街按钮
                                                             // audioSupport(6)，有声音提示
                                                             // audio crossing cues present
                                                             // rfSignalRequestPresent(7)，支持射频检测
                                                             // unsignalizedSegmentsPresent(8)，部分区段不受信号灯控制
    crosswalk:short;
}
