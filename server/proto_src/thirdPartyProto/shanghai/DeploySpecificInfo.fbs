namespace MECData;

// @brief 环境变量

table DE_EnvironmentVariable {
    env: string;                    // 环境变量名称
    value: string;                  // 环境变量值
}

// @brief Docker容器状态

enum DE_ContainerStatus: byte {
    CREATED = 0,                    // 已创建
    RUNNING = 1,                    // 运行中
    PAUSED = 2,                     // 已暂停
    STOPPED = 3,                    // 已停止
    REMOVED = 4,                    // 已删除
}

// @brief Docker网络模型

enum DE_NetworkMode: byte {
    BRIDGE = 0,                     // 桥接
    HOST = 1,                       // 主机
    NONE = 2,                       // 不使用网络
    OVERLAY = 3,                    // Overlay虚拟网络
    MACVLAN = 4,                    // MACVlan虚拟网络
}

// @brief Docker容器端口映射

table DE_ContainerPortMapping {
    host_port: uint16;              // 主机端口号
    container_port: uint16;         // 容器端口号
}

// @brief Docker目录挂载

table DE_ContainerVolumeMapping {
    path_on_host: string;           // 主机目录
    path_in_container: string;      // 容器对应目录
}

// @brief Docker型应用模块特异性信息

table DF_DockerModuleInfo {
    image_name: string;             // 镜像名
    tag: string;                    // 镜像Tag
    image_id: string;               // 镜像ID
    container_id: string;           // 容器ID，由Docker Engine提供，不可配置
    state: DE_ContainerStatus;      // 容器状态，由Docker Engine提供，不可配置
    status: string;                 // 容器状态，由Docker Engine提供，不可配置
    port_mapping: [DE_ContainerPortMapping];        // 容器端口映射关系合集
    volume_mapping: [DE_ContainerVolumeMapping];    // 容器目录映射关系合集
    network_mode: DE_NetworkMode;   // 容器网络模型
    cmd: [string];                  // 容器启动命令
    working_dir: string;            // 容器工作目录
    image_sha256sum: string;        // 容器SHA256摘要
                                    // 格式示例：2b7412e6465c3c7fc5bb21d3e6f1917c167358449fecac8176c6e496e5c1f05f
}

// @brief SO型应用模块特异性信息

table DF_SharedObjectModuleInfo {
    service_host_pid: uint64;           // 服务宿主进程PID，由ZBRS提供，不可配置
    so_filename: string;                // SO文件名
    so_file_sha256sum: string;          // SO文件SHA256摘要
    so_dependency_list: [string];       // 依赖的外部SO列表
}

// @brief 独立型应用模块特异性信息

table DF_StandaloneProgramModuleInfo {
    working_directory: string;          // 工作目录
    venv_activate: string;              // 虚拟环境启动命令
    entrypoint: string;                 // 应用启动命令
    environment_variables: [DE_EnvironmentVariable];        // 应用依赖的环境变量
}

// @brief 内建型应用模块特异性信息

table DF_NativeClassModuleInfo {
    class_name: string;                 // 内建模块名
}

// @brief 应用模块部署特异性信息

union DF_DeploySpecificInfo {
    DF_NativeClassModuleInfo,           // 内建型
    DF_SharedObjectModuleInfo,          // SO型
    DF_DockerModuleInfo,                // Docker型
    DF_StandaloneProgramModuleInfo      // 独立型
}
