namespace MECData;

// @brief SSL证书用途
table DE_CertificateUsage {
    for_client: bool;                   // 客户端用
    for_server: bool;                   // 服务端用
}

// @brief SSL证书校验策略
enum DE_VerificationSetting: byte {
    CLIENT_VERIFY_NONE = 0,             // 不校验
    CLIENT_VERIFY_REQUIRED = 1,         // 校验，要求校验
    CLIENT_VERIFY_OPTIONAL = 2,         // 校验，允许校验不通过
}

// @brief SSL证书
table DF_SSLCertificate {
    name: string;                                       // 证书名称
    usage: DE_CertificateUsage (required);              // 证书用途
    certificate: string (required);                     // 证书，PEM格式
    private_key: string (required);                     // 私钥，PEM格式
    ca_certificate: string;                             // CA证书，PEM格式
    verification_setting: DE_VerificationSetting = CLIENT_VERIFY_REQUIRED;      // SSL证书校验策略
}
