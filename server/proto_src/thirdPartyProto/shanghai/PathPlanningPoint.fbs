include "ReferenceLink.fbs";
include "PositionOffsetLLV.fbs";
include "PositionConfidenceSet.fbs";
include "SpeedConfidence.fbs";
include "HeadingConfidence.fbs";
include "AccelerationSet4Way.fbs";
include "AccSet4WayConfidence.fbs";

namespace MECData;

// @brief 路径规划中的位置点信息

table DF_PathPlanningPoint {
    posInMap:DF_ReferenceLink;                          // 地图中的位置
    pos:DF_PositionOffsetLLV(required);                 // 规划路径中的目标点
    posAccuracy:DF_PositionConfidenceSet;               // 规划路径中的目标点位置置信度
    speed:ushort;                                       // 通过目标点时的建议车速，单位：0.02m/s
    speedCfd:DE_SpeedConfidence;                        // 通过目标点时的建议车速置信度
    heading:ushort;                                     // 通过目标点时的航向角，正北偏转角，单位：0.0125°
    headingCfd:DE_HeadingConfidence;                    // 通过目标点时的航向角置信度
    accelSet:DF_AccelerationSet4Way;                    // 通过目标点时的四轴加速度
    acc4WayConfidence:DF_AccSet4WayConfidence;          // 通过目标点时的四轴加速度置信度
    estimatedTime:ushort;                               // 到达此目标点的预计时刻相比上层VIR消息时间戳所示时刻的偏移，单位：10ms，65535为无效值
    timeConfidence:ubyte;                               // 到达预计时刻置信度，单位：0.5%
}
