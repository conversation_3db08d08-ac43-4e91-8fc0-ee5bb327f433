namespace MECData;

// @brief 停车车道的属性定义

table DF_LaneAttributesParking {
    parking:byte;                           // 存在下列情况时，字段对应二进制位置1
                                            //parkingRevocableLane (0)，动态启用
                                            //parallelParkinginUse(1)，平行式
                                            //headlnParkinglnUse(2)，斜列式或垂直式
                                            //doNotParkZone(3)，禁止停车位置
                                            //parkingForBusUse(4)，公交车停车位
                                            //parkingForTaxiUse(5)，出租车停车位
                                            //noPublicParkingUse(6)，无公共停车位
}
