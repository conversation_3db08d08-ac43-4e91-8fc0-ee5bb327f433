include "Position3D.fbs";
include "RTEData.fbs";
include "RTSData.fbs";
include "DebugTimeRecords.fbs";

namespace MECData;

// @brief 路侧交通事件及交通标志标线信息
table MSG_RoadSideInformation {
    moy:uint;                                       // 今年已经过去的分钟数
    id:uint16;                                      // RSU ID
    refPos:DF_Position3D(required);                 // 消息参考经纬度点
    rtes:[DF_RTEData];                              // 交通事件集合
    rtss:[DF_RTSData];                              // 交通标志标线集合
    time_records: DF_DebugTimeRecords;              // 消息模块间流转记录
    msg_id: int64;                                  // unique id for this message
    rsu_id: [uint8];                                // RSU ID
}

root_type MSG_RoadSideInformation;
