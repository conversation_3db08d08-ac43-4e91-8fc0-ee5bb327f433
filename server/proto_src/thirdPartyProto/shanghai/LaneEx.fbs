include "LaneAttributes.fbs";
include "AllowedManeuvers.fbs";
include "RegulatorySpeedLimit.fbs";
include "ConnectionEx.fbs";
include "STPoint.fbs";
include "RoadMark.fbs";
include "RoadPoint.fbs";

namespace MECData;

// @brief 车道
table DF_LaneEx {
    laneRefID: int8;                // 车道参考位置ID
                                    // 参考线所属车道ID为0
                                    // 其左侧车道依次编号1、2、3...
                                    // 其右侧车道依次编号-1、-2、-3...
    laneWidth: ushort;              // 车道宽度，单位：cm
    laneAttributes: DF_LaneAttributes;          // 车道属性
    maneuvers: DE_AllowedManeuvers;             // 车道转向
    connectsTo_ex: [DF_ConnectionEx];           // 车道下游连接关系
    speedLimits: [DF_RegulatorySpeedLimit];     // 车道限速
    stpoints: [DF_STPoint];         // 以ST坐标系定义的车道参考线
    ext_id: string;                 // 车道扩展ID
    road_marks: [DF_RoadMark];      // 车道标线，s_offset升序排列
                                    // 车道参考位置ID < 0时，描述车道右侧边缘标线
                                    // 车道参考位置ID > 0时，描述车道左侧边缘标线
                                    // 车道参考位置ID = 0时，描述车道中心标线
    offset_points: [DF_RoadPoint];  // 基于经纬度的车道参考线，为相对Map消息参考点的坐标偏移
}
