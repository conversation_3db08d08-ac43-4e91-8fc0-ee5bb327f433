namespace MECData;

// @brief 车道级地点平均速度
table MSG_SpeedLanePoint {
    deviceId: uint16;                                       // 检测传感器对应协议接口模块实例ID
    detectAeraId: int32;                                    // 检测框ID
    moy: uint = 4294967295;                                 // 今年已经过去的分钟数
    secMark: ushort = 65535;                                // 当前分钟已过去的毫秒数
    cycle: ushort = 65535;                                  // 检测周期，单位：s
    speed: ushort = 65535;                                  // 地点平均速度，单位：0.02m/s
}

root_type MSG_SpeedLanePoint;
