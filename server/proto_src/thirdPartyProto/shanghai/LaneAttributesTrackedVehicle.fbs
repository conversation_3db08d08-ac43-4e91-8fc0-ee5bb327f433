namespace MECData;

// @brief 轨道车辆车道的属性定义
table DF_LaneAttributesTrackedVehicle {
    tracked:byte;                   // 存在下列情况时，字段对应二进制位置1
                                    // spec-RevocableLane(0)，动态启用
                                    // spec-commuterRailRoadTrack(1)，通勤铁路
                                    // spec-lightRailRoadTrack(2)，轻轨
                                    // spec-heavyRailRoadTrack(3)，重轨
                                    // spec-otherRailType(4)，其他轨道
}
