namespace MECData;

// @brief 可变情报板预先录制图文
table DF_VMSPredefined {
    predefined_id: uint8;               // 预先录制图文编号
}

// @brief 可变情报板文字
table DF_VMSText {
    text: string;                       // 文字内容
}

// @brief 可变情报板显示内容
union DF_VMSContent {
    DF_VMSPredefined,                   // 预先录制图文
    DF_VMSText                          // 文字
}

// @brief 可变情报板
table MSG_VariableMessageSign {
    dev_id: uint16;                     // 可变情报板对应协议接口模块id
    content: DF_VMSContent (required);  // 显示内容
    ext_id: string;                     // 额外拓展ID
    msg_id: int64;                      // 雪花ID
}

root_type MSG_VariableMessageSign;
