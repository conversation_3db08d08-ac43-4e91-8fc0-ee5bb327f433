include "Maneuver.fbs";
include "Direction8.fbs";

namespace MECData;

// @brief 信号灯组路网元素特征
table DF_PhaseMapElement {
    movement_ext_id: string;                                // 对应转向ID或路网元素ID
    maneuver: DE_Maneuver;                                  // 对应转向
    direction: DE_Direction8;                               // 对应交叉口进口道方位
}

// @brief 信号灯组类型
enum DE_PhaseType: uint8 {
    UNKNOWN = 0,                                            // 未知
    MOTOR = 1,                                              // 机动车信号灯
    NON_MOTOR = 2,                                          // 非机动车信号灯
    PEDESTRIAN = 3,                                         // 行人信号灯
    LANE = 4                                                // 车道信号灯
}

// @brief 信号灯组特征
table DF_PhaseCharacteristics {
    phase_id: uint8;                                        // 信号灯组ID
    phase_type: DE_PhaseType = UNKNOWN;                     // 信号灯组类型
    map_elements: [DF_PhaseMapElement];                     // 控制的路网元素
}
