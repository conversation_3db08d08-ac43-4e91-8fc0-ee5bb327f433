include "TrafficFlow.fbs";

namespace MECData;

// @brief 排队长度单位
enum DE_QLengthUnit: uint8 {
    CENTIMETERS = 0,        // cm
    VEHICLES = 1            // 车辆数
}

// @brief 排队长度
table MSG_QueueLength {
    detector_area_id: uint32;   // 检测框ID
    detector_id: uint16;        // 检测器ID
    timestamp: int64;           // 毫秒级UNIX时间戳
    cycle: ushort = 65535;      // 统计周期，单位：s
    unit: DE_QLengthUnit;       // 排队长度单位
    value: uint32;              // 排队长度值
    map_element: DE_TrafficFlowStatMapElement;      // 排队长度绑定路网元素
    msg_id: int64;              // 雪花ID
}

root_type MSG_QueueLength;
