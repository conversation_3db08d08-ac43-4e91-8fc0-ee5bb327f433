namespace MECData;

// @brief 图片URL
table DF_ImageURL {
    url: string;                // 图片URL
}

// @brief 视频片段URL
table DF_VideoClipURL {
    url: string;                // 视频片段URL
}

// @brief 图片格式
enum DE_ImageFormat: uint8 {
    JPG = 0,                    // JPEG
    PNG = 1,                    // PNG
    TIFF = 2,                   // TIFF
    BMP = 3,                    // BMP
    GIF = 4                     // GIF
}

// @brief 图片二进制数据
table DF_ImageBytes {
    format: DE_ImageFormat;     // 图片格式
    bytes: [uint8];             // 图片二进制数据
}

// @brief 图片内容
union DF_ImageContent {
    DF_ImageURL,                // 图片URL
    DF_VideoClipURL,            // 视频片段URL
    DF_ImageBytes               // 图片二进制数据
}

// @brief 图片
table DF_Image {
    id: uint16;                 // 图片信息ID   
    timestamp: uint64;          // 图片信息毫秒级时间戳
    description: string;        // 描述
    content: DF_ImageContent;   // 图片内容
}
