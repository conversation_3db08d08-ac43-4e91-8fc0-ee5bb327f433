include "NodeReferenceID.fbs";
include "IntersectionStatusObject.fbs";
include "Phase.fbs";
include "TimeConfidence.fbs";

namespace MECData;

// @brief 一个路口信号灯的属性和当前状态
table DF_IntersectionState {
    intersectionId:DF_NodeReferenceID(required);                // 交叉口ID
    status:DE_IntersectionStatusObject(required);               // 信号控制机工作模式
    moy:uint;                                                   // 今年已经过去的分钟数
    timeStamp:ushort;                                           // 当前分钟已经过去的毫秒数
    timeConfidence:DE_TimeConfidence;                           // 时间置信度
    phases:[DF_Phase](required);                                // 信号灯组
}
