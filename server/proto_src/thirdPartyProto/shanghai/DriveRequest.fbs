include "ReqStatus.fbs";
include "ReqInfo.fbs";

namespace MECData;

// @brief 车辆发出的请求信息

table DF_DriveRequest {
    reqID:ubyte= 255;                       // 请求ID
                                            // 同一请求在多个VIR消息中应使用同一请求ID
    status:DE_ReqStatus;                    // 请求操作状态
    reqPriority:string;                     // 请求优先级
                                            // 低5位保留，设置为0
                                            // 二进制值*********至*********表示从低到高的优先级
    targetVeh:string;                       // 目标车辆临时ID
    targetRSU:string;                       // 目标RSU临时标识
    info:DF_ReqInfo;                        // 请求内容
    lifeTime:ushort;                        // 请求有效期相比上层VIR消息时间戳所示时刻的偏移，单位：10ms，65535为无效值
}
