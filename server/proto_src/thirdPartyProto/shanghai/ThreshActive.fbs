namespace MECData;

// @brief 阈值判断与地图元素绑定关系
enum DE_MapElementType: uint8 {
    Node = 0,           // 节点关系级别绑定
    Link = 1,           // 路段级别元素绑定
    Movement = 2,       // 转向级别元素绑定
    Lane = 3,           // 车道级别元素绑定
    Connection = 4      // 车道与下游道路连接关系级别元素绑定           
}

// @brief 阈值判断枚举类型
enum DE_ThreshActiveType: uint8 {
    QueueOverflow = 0,            // 排队溢出
    QueueImbalance = 1,           // 排队不均衡
    LowGreenUtilization = 2,      // 绿灯利用率低
    MainstreamOvercapacity = 3    // 主流向过饱和
}

// @brief 阈值判断结果
table DF_ThreshActive {
    map_element_type: DE_MapElementType;     // 阈值判断结果所属地图元素类型
    map_element_ext_id: string (required);   // 地图元素唯一扩展ID
    thresh_active_type: DE_ThreshActiveType; // 阈值判断类型
}