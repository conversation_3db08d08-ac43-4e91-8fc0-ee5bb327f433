namespace MECData;

// @brief 自行车道属性

table DF_LaneAttributesBike {
    bike:byte;                                              // 存在下列情况时，字段对应二进制位置1
                                                            // bikeRevocableLane (0)，动态自行车道
                                                            // pedestrianUseAllowed (1)，允许行人使用
                                                            // isBikeFlyOverLane (2)，和其他车道不在一个平面上
                                                            // fixedCycleTime (3)，被固定周期的信号灯组控制，无需使用按钮
                                                            // biDirectionalCycleTimes (4)，正反方向使用不同的信号灯组
                                                            // isolatedByBarrier (5)，有实体分割
                                                            // unsignalizedSegmentsPresent (6)，无信号灯控制
}
