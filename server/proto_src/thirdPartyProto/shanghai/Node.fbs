include "NodeReferenceID.fbs";
include "Position3D.fbs";
include "Link.fbs";

// Day II Extensions
include "LinkEx.fbs";
include "ProhibitedZone.fbs";

include "PedCrossing.fbs";

namespace MECData;

// @brief 交叉口或节点
table DF_Node {
    name:string;                                    // 交叉口名称
    id: DF_NodeReferenceID(required);               // 交叉口参考ID
    refPos: DF_Position3D(required);                // 交叉口参考经纬度
    inLinks: [DF_Link];                             // 入交叉口道路
    inLinks_ex: [DF_LinkEx];                        // 入交叉口道路（Day II）
    prohibitedzone: [DF_ProhibitedZone];            // 禁止区域
    signal_controlled: bool;                        // 是否为信号控制路口
    ped_crossings: [DF_PedCrossing];                // 人行横道
}
