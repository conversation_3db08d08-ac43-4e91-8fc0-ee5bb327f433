include "Position3D.fbs";

namespace MECData;

// @brief 当前天气
table MSG_NowWeather {
    divisionCode:uint= 4294967295;              // 行政区代码，见http://www.mca.gov.cn/article/sj/xzqh/2020/
    timezone:short = 32767;                     // 时区，取值范围：-1200~1200, 例：800=GMT +8:00
    app_id: uint16;                             // 协议接口应用实例ID
    subdevice_id: uint8;                        // 子设备ID
    pos:DF_Position3D(required);                // 参考位置
    updateTime:uint = 4294967295;               // 更新时间，今年已经过去的分钟数
    description:string;                         // 描述
    weatherCode:ubyte = 255;                    // 天气代码，见https://docs.seniverse.com/api/start/code.html
    temperature:short;                          // 气温，单位：0.1摄氏度
    apparentTemperature:short;                  // 体感温度，单位：0.1摄氏度
    pressure:ushort;                            // 气压，单位：Pa
    humidity:ubyte;                             // 湿度，单位：%
    visibility:ushort;                          // 能见度，单位：m
    windDirection:ushort;                       // 风向，单位：°，正北偏转角
    windSpeed:ushort;                           // 风速，单位：0.01km/h
    windScale:ubyte;                            // 蒲福风级
    clouds:ubyte;                               // 多云，单位：%
    dewPoint:short;                             // 露点，单位：0.1摄氏度
    roadsurtemp:short;                          // 道路表面温度，单位：0.1摄氏度
    freezingtemp:short;                         // 冰点温度，单位：0.1摄氏度
    waterfilmthickness:ubyte;                   // 水膜厚度，单位：mm
    snowthickness:ubyte;                        // 雪厚度，单位：mm
    icethickness:ubyte;                         // 冰厚度，单位：mm
    salinity:ubyte;                             // 盐度，单位：%
    surfacewetslipcoefficient:ubyte;            // 表面湿滑系数
    roadconditions:ubyte;                       // 路面条件，0:干燥 ,1:潮湿, 2:有水,4:有雪/冰,6:危险湿滑
    msg_id: int64;                              // 消息雪花ID
}

root_type MSG_NowWeather;
