namespace MECData;

// @brief 95%置信水平的车辆位置精度

enum DE_PositionConfidence: byte {
    unavailable=0,              // 不可用
    a500m=1,                    // 500m或5e-3°
    a200m=2,                    // 200m或2e-3°
    a100m=3,                    // 100m或1e-3°
    a50m=4,                     // 50m或5e-4°
    a20m=5,                     // 20m或2e-4°
    a10m=6,                     // 10m或1e-4°
    a5m=7,                      // 5m或5e-5°
    a2m=8,                      // 2m或2e-5°
    a1m=9,                      // 1m或1e-5°
    a50cm=10,                   // 0.50m或5e-6°
    a20cm=11,                   // 0.20或2e-6°
    a10cm=12,                   // 0.10m或1e-6°
    a5cm=13,                    // 0.05m或5e-7°
    a2cm=14,                    // 0.02m或2e-7°
    alcm=15                     // 0.01m或1e-7°
}
