namespace MECData;

// @brief 车辆行驶车道的属性定义
table DF_LaneAttributesVehicle {
    vehicle:short;              // 存在下列情况时，字段对应二进制位置1
                                // isVehicleRevocableLane(0)，动态启用
                                // isVehicleFlyOverLane(1)，是否与其他车道在同一平面
                                // path of lane is not at grade
                                // hovLaneUseOnly(2)，是否为多乘员车道（HOV）
                                // restrictedToBusUse(3)，公交车专用
                                // restrictedToTaxiUse(4)，出租车专用
                                // restrictedfromPublicUse(5)，专用
                                // hasIRbeaconCoverage(6)，射频信标副高
                                // permissionOnRequest(7)，需要请求路权
}
