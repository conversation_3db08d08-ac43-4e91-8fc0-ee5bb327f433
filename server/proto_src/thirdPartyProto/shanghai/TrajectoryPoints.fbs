include "VehicleClassification.fbs";
include "NodeReferenceID.fbs";
include "VehicleCharEx.fbs";

namespace MECData;

// @brief 集计上报交通参与者轨迹点
table DF_SingleTrajectoryPoint {
    timestamp: int64;               // 毫秒级UNIX时间戳
    lat:int;                        // 纬度，单位：1e-7°
    lon:int;                        // 经度，单位：1e-7°
    speed: int32;                   // 瞬时速度，单位：0.1 m/s
    direction_angle: uint8;         // 航向角，正北偏转角，单位：1.5°
    section_id: uint8;              // 路段ID
    lane_ref_id: int8;              // 车道参考ID
    dis_to_stopline: uint32 = 0;    // 距离停止线距离，单位：0.1m
}

// @brief 交叉口轨迹数据特征
table DF_TrajectoryNodeExtension {
    node: DF_NodeReferenceID (required);            // 所属交叉口
    movement_ext_id: string (required);             // 在交叉口内转向的扩展ID
    link_ext_ids: [string] (required);              // 经过道路的扩展ID，按经过先后顺序排列
}

// @brief 轨迹集计上报
table MSG_TrajectoryPoints {
    mec_id: string;                 // MEC ID
    ptc_type: ubyte = 255;          // 交通参与者类型
                                    // 0 = 未知类型
                                    // 1 = 机动车
                                    // 2 = 非机动车
                                    // 3 = 行人
                                    // 4 = RSU接收BSM获得的V2X交通参与者
    ptc_id: ushort = 65535;         // 交通参与者ID
    obu_id: [ubyte];                // 交通参与者OBU ID
    points: [DF_SingleTrajectoryPoint];     // 轨迹点，按时间先后排列
    ce_token: string;                       // 碳排放令牌
    veh_type: DF_VehicleClassification;     // 车型
    veh_char: DF_VehicleCharEx;             // 车辆额外特征
    node_ext: DF_TrajectoryNodeExtension;   // 交叉口轨迹数据特征
    msg_id: int64;                  // 消息雪花ID
}

root_type MSG_TrajectoryPoints;
