include "Position3D.fbs";
include "DeviceAbnormal.fbs";
include "NodeReferenceID.fbs";
include "Mileage.fbs";
include "DeviceSpecificInfo.fbs";
include "PacketFlowRecord.fbs";
include "ModulePerformance.fbs";

namespace MECData;

// @brief 外接设备描述

table DF_DeviceDescription {
    model: string;                              // 型号
    manufacturer: string;                       // 厂家
    raw_serial_number: string;                  // 出厂序列号
}

// @brief 实际子设备即时信息

table DF_SubDeviceInfo {
    id: uint8;                                  // 外接设备编号
    pos: DF_Position3D;                         // 安装经纬度
    mileage: DF_Mileage;                        // 安装桩号
    description: DF_DeviceDescription;          // 外接设备描述
    specifics: DF_DeviceSpecificInfo;           // 外接设备特异性信息
    status: DF_DeviceAbnormal;                  // 外接设备状态
    packet_stats: DF_PacketFlowRecord;          // 收发标准数据数量统计
    performance: [DF_ModulePerformance];        // 外接设备性能指标
}

// @brief 外接设备即时信息

table DF_DeviceInfo {
    sub_devices: [DF_SubDeviceInfo];            // 对接实际子设备即时信息
}
