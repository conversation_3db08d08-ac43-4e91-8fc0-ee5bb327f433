namespace MECData;

// @brief 停车位类型

table DE_ParkingType {                                     
    parkingType:short;  //存在下列情况时，字段对应二进制位置1
                        //unknown=0，未知
                        //ordinary=1，普通
                        //disabled=2，残疾人
                        //mini=3，小型
                        //attached=4，附属
                        //charging=5，充电桩
                        //stereo=6，立体
                        //lady=7，女士专用
                        //extended=8，扩展
                        //private=9，私人
}
