include "LaneAttributes.fbs";
include "Connection.fbs";
include "RegulatorySpeedLimit.fbs";
include "RoadPoint.fbs";
include "AllowedManeuvers.fbs";

namespace MECData;

// @brief 车道

table DF_Lane {
    laneId:ubyte= 255;                              // 车道ID
    laneWidth:ushort;                               // 车道宽度，单位：cm
    laneAttributes:DF_LaneAttributes;               // 车道属性
    maneuvers:DE_AllowedManeuvers;                  // 车道允许转向
    connectsTo:[DF_Connection];                     // 车道下游连接关系
    speedLimits:[DF_RegulatorySpeedLimit];          // 车道限速
    points:[DF_RoadPoint];                          // 车道参考点序列，自上游到下游
}
