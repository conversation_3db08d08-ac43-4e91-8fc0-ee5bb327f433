include "PositionLL24B.fbs";
include "PositionLL28B.fbs";
include "PositionLL32B.fbs";
include "PositionLL36B.fbs";
include "PositionLL44B.fbs";
include "PositionLL48B.fbs";
include "PositionLLmD64b.fbs";

namespace MECData;

// @brief 经纬度偏差，来描述一个坐标点的相对位置。约定偏差值等于真实值减去参考值。
union DF_PositionOffsetLL {
    DF_PositionLL24B,                                  // 距离参考点偏差距离小于±22.634554m
    DF_PositionLL28B,                                  // 距离参考点偏差距离小于±90.571389m
    DF_PositionLL32B,                                  // 距离参考点偏差距离小于±362.31873m
    DF_PositionLL36B,                                  // 距离参考点偏差距离小于±01.449308km
    DF_PositionLL44B,                                  // 距离参考点偏差距离小于±23.189096km
    DF_PositionLL48B,                                  // 距离参考点偏差距离小于±92.756481km
    DF_PositionLLmD64b                                 // 经纬度坐标绝对值
}
