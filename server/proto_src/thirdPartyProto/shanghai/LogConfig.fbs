include "ErrorLevel.fbs";

namespace MECData;

// @brief 日志服务器连接参数

table DE_LogServerConfig {
    server_host: string;                        // 主机名或IP
    server_port: uint16;                        // 监听端口
}

// @brief 日志策略

table DF_LogConfig {
    log_level: DE_ErrorLevel = DEBUG;           // 写入日志的错误等级
    log_path: string;                           // 日志储存目录
    max_file_size: uint64;                      // 最大单个日志文件尺寸，单位：字节
    max_file_duration: uint64;                  // 最大单个日志文件覆盖时长，单位：ms
    log_server_config: DE_LogServerConfig;      // 日志服务器设置
    max_days_to_keep: uint16;                   // 最大保留天数
}
