#pragma once

#include "moduleBase.h"
#include "dataPusherBase.h"
#include "udpDataPusher.h"
#include "mqttDataPusher.h"
#include <memory>
#include <functional>
#include <thread>
#include <atomic>
#include <map>
extern std::string gTestCaseFilePath;
extern std::string gCompressFilePath;
extern std::string gTestCaseResultFilePath;
volatile extern int gSoftwareVersion ;

// 数据推送器状态回调类型
typedef std::function<void(EPusherStatus status, EPusherError error, const std::string& errorMsg)> TPusherStatusCallback;

// UDP推送器配置
typedef struct tagUdpPusherManagerConfig
{
    bool bEnable = false;                    // 是否启用UDP推送
    std::string strAddr = "127.0.0.1";      // UDP目标地址
    u32 dwPort = 8080;                      // UDP目标端口
    u32 dwTimeoutMs = 5000;                 // 超时时间(毫秒)
    u32 dwRetryCount = 3;                   // 重试次数
    u32 dwRetryIntervalMs = 1000;           // 重试间隔(毫秒)
    bool bAutoReconnect = true;             // 是否自动重连
    bool bBroadcast = false;                // 是否广播模式
    u32 dwMaxPacketSize = 8192;             // 最大包大小
} TUdpPusherManagerConfig;

// MQTT推送器配置
typedef struct tagMqttPusherManagerConfig
{
    bool bEnable = false;                    // 是否启用MQTT推送
    std::string strAddr = "127.0.0.1";      // MQTT服务器地址
    u32 dwPort = 1883;                      // MQTT服务器端口
    u32 dwTimeoutMs = 5000;                 // 超时时间(毫秒)
    u32 dwRetryCount = 3;                   // 重试次数
    u32 dwRetryIntervalMs = 1000;           // 重试间隔(毫秒)
    bool bAutoReconnect = true;             // 是否自动重连
    std::string strClientId = "";           // 客户端ID
    std::string strTopic = "data/raw";      // 发布主题
    std::string strUsername = "";           // 用户名
    std::string strPassword = "";           // 密码
    u32 dwQos = 0;                          // QoS等级
    bool bRetain = false;                   // 是否保留消息
    bool bCleanSession = true;              // 是否清理会话
    u32 dwKeepAliveSeconds = 60;            // 心跳间隔(秒)
    bool bUseTls = false;                   // 是否使用TLS
} TMqttPusherManagerConfig;

namespace TestCase{
    struct FileInfo {
        std::string name;      // 原始文件名，如 "0819_C-20250819005_RSM.json"
        std::string base_name; // 基础文件名，如 "RSM"
        std::string middle_part; // 中间部分，如 "C-20250819005"
        int freq;              // 频率值

        FileInfo(const std::string& n, int f)
            : name(n), freq(f) {
            parseFileName();
        }

    private:
        void parseFileName() {
            // 去掉文件扩展名
            size_t dot_pos = name.find_last_of('.');
            std::string name_without_ext = (dot_pos != std::string::npos) ? name.substr(0, dot_pos) : name;

            // 按 '_' 分割文件名
            std::vector<std::string> parts;
            size_t start = 0;
            size_t pos = 0;
            while ((pos = name_without_ext.find('_', start)) != std::string::npos) {
                parts.push_back(name_without_ext.substr(start, pos - start));
                start = pos + 1;
            }
            parts.push_back(name_without_ext.substr(start)); // 添加最后一部分

            if (parts.size() == 3) {
                // 新格式：0819_C-20250819005_RSM
                middle_part = parts[1];  // C-20250819005
                base_name = parts[2];    // RSM
            } else {
                // 旧格式或其他格式，直接使用去掉扩展名的文件名作为base_name
                base_name = name_without_ext;
                middle_part = "";
            }

            // 统一转换为大写方便比较
            std::transform(base_name.begin(), base_name.end(), base_name.begin(), ::toupper);
        }
    };

    // 单个测试任务信息
    struct TestTask {
        std::string case_uid;
        std::string folder_path; // 文件夹完整路径
        std::string test_name;   // 测试名称
        std::string data_uid;
        std::string file_type;
        unsigned long long create_timestamp;
        std::vector<TestCase::FileInfo> files; // 文件列表
        
        // 添加文件信息
        void addFile(const std::string& name, int freq) {
            files.emplace_back(name, freq);
        }
    };
    using TaskList = std::vector<TestTask>;

    // 文件推送线程信息
    struct FilePushThread {
        std::string baseName;           // 除去后缀名
        std::string fileName;           // 文件名
        std::string fileContent;        // 文件内容
        int frequency;                  // 推送频率(Hz)
        std::thread thread;             // 推送线程
        std::atomic<bool> shouldStop;   // 停止标志

        FilePushThread(const std::string& base_name,const std::string& name, const std::string& content, int freq)
            : baseName(base_name),fileName(name), fileContent(content), frequency(freq), shouldStop(false) {}
    };
};


class CTestCaseManager : public CModuleBase
{
public:
    CTestCaseManager(CMediator* pMediator);
    virtual ~CTestCaseManager();

    // 模块基类接口实现
    virtual void Init() override;
    virtual void Start() override;
    virtual void Stop() override;
    virtual void Pause() override;
    
    bool MsgFilter(u32 msgType);
    void HandleMsg(u32 msgLevel,u32 deviceId,u32 msgType,std::shared_ptr<void> spData);

    // 恢复推送器
    bool Resume();

    // 数据推送相关方法
    bool InitDataPushers();
    bool StartDataPushers();
    bool StopDataPushers();
    bool PushData(const std::string& data);

    // 配置管理
    void SetUdpPusherConfig(const TUdpPusherManagerConfig& config);
    void SetMqttPusherConfig(const TMqttPusherManagerConfig& config);
    TUdpPusherManagerConfig GetUdpPusherConfig() const;
    TMqttPusherManagerConfig GetMqttPusherConfig() const;

    // 状态回调设置
    void SetUdpPusherStatusCallback(const TPusherStatusCallback& callback);
    void SetMqttPusherStatusCallback(const TPusherStatusCallback& callback);

    // 统计信息获取
    CDataPusherBase::Statistics GetUdpPusherStatistics() const;
    CDataPusherBase::Statistics GetMqttPusherStatistics() const;

    // 推送器状态查询
    EPusherStatus GetUdpPusherStatus() const;
    EPusherStatus GetMqttPusherStatus() const;

    // 文件推送线程管理
    void CreateFilePushThread(const std::string& baseName,const std::string& fileName, const std::string& fileContent, int frequency);
    void StopFilePushThreads();
    void FilePushWorker(std::shared_ptr<TestCase::FilePushThread> threadInfo);

private:
    void OnUdpPusherStatus(EPusherStatus status, EPusherError error, const std::string& errorMsg);
    void OnMqttPusherStatus(EPusherStatus status, EPusherError error, const std::string& errorMsg);
    // 配置转换方法
    TUdpPusherConfig ConvertUdpConfig(const TUdpPusherManagerConfig& config) const;
    TMqttPusherConfig ConvertMqttConfig(const TMqttPusherManagerConfig& config) const;
    // 用例管理
    bool loadLocalTestCase();
    // 压缩文件读取
    bool loadCompressFile();
    // 数据处理
    bool handleAddTestCaseReq(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);
    bool handleControlDataPushReq(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);
    bool hendleGetAllInfo(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);
    bool handleGetUploadFileListReq(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);
    bool handleTestCaseDeleteOne(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);
    bool handleClientConnected(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);

    // 辅助函数：从文件名中提取数据类型
    std::string extractDataTypeFromFileName(const std::string& fileName);
    // 辅助函数：从文件名中提取case_uid
    std::string extractCaseUidFromFileName(const std::string& fileName);

private:
    // UDP推送器
    std::shared_ptr<CUdpDataPusher> m_pUdpPusher;
    TUdpPusherManagerConfig m_udpConfig;
    TPusherStatusCallback m_udpStatusCallback;
    // MQTT推送器
    std::shared_ptr<CMqttDataPusher> m_pMqttPusher;
    TMqttPusherManagerConfig m_mqttConfig;
    TPusherStatusCallback m_mqttStatusCallback;
    // 线程安全
    mutable std::mutex m_configMutex;
    mutable std::mutex m_pusherMutex;
  
    std::vector<TestCase::TestTask> m_vecTestCaseInfoList;//测试用例文件队列
    std::vector<TTestCaseCompressFileInfo> m_vecCompressFileList;  //压缩包文件队列
    std::string m_strCompressFilePath;  // 压缩包文件路径
    std::string m_strTestCaseFilePath;  // 测试用例文件路径
    std::string m_strTestCaseResultFilePath;    // 测试用例发送及读写路径
    bool m_bIsPusherInited=false;

    // 文件推送线程管理
    std::vector<std::shared_ptr<TestCase::FilePushThread>> m_filePushThreads;
    mutable std::mutex m_filePushMutex;

    TTestCaseStatus::enumStatus m_status;
    u64 m_llStartTime;
    TTestCasePushConf m_stConf;
};
