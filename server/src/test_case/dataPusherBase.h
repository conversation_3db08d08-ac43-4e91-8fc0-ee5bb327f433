#ifndef _DATA_PUSHER_BASE_H_
#define _DATA_PUSHER_BASE_H_

#include "struct.h"
#include <string>
#include <memory>
#include <functional>
#include <thread>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <queue>

// 推送状态枚举
enum class EPusherStatus
{
    DISCONNECTED,   // 未连接
    CONNECTING,     // 连接中
    CONNECTED,      // 已连接
    SENDING,        // 发送中
    ERROR           // 错误状态
};

// 推送错误类型枚举
enum class EPusherError
{
    NONE,               // 无错误
    CONNECTION_FAILED,  // 连接失败
    SEND_FAILED,        // 发送失败
    NETWORK_ERROR,      // 网络错误
    TIMEOUT,            // 超时
    INVALID_CONFIG,     // 配置无效
    UNKNOWN             // 未知错误
};

// 推送结果结构
typedef struct tagPushResult
{
    bool bSuccess = false;          // 是否成功
    EPusherError errorType = EPusherError::NONE;  // 错误类型
    std::string strErrorMsg;        // 错误信息
    u64 llTimestamp = 0;           // 时间戳
} TPushResult;

// 推送配置基类
typedef struct tagPusherConfig
{
    bool bEnable = false;           // 是否启用
    std::string strAddr;            // 地址
    u32 dwPort = 0;                // 端口
    u32 dwTimeoutMs = 5000;        // 超时时间(毫秒)
    u32 dwRetryCount = 3;          // 重试次数
    u32 dwRetryIntervalMs = 1000;  // 重试间隔(毫秒)
    bool bAutoReconnect = true;     // 是否自动重连
} TPusherConfig;

// 数据推送基类
class CDataPusherBase
{
public:
    CDataPusherBase();
    virtual ~CDataPusherBase();

    // 初始化配置
    virtual bool Init(const TPusherConfig& config);

    // 连接到目标
    virtual bool Connect() = 0;

    // 断开连接
    virtual bool Disconnect() = 0;

    // 发送数据
    virtual bool SendData(const std::string& data) = 0;

    // 批量发送数据
    virtual bool SendDataBatch(const std::vector<std::string>& dataList);

    // 开始推送服务
    virtual bool Start();

    // 停止推送服务
    virtual bool Stop();

    // 暂停推送
    virtual bool Pause();

    // 恢复推送
    virtual bool Resume();

    // 推送数据到队列
    virtual bool PushData(const std::string& data);

    // 获取当前状态
    EPusherStatus GetStatus() const { return m_status; }

    // 获取最后错误
    EPusherError GetLastError() const { return m_lastError; }

    // 获取错误信息
    std::string GetErrorMessage() const { return m_strLastErrorMsg; }

    // 设置状态回调
    void SetStatusCallback(const std::function<void(EPusherStatus, EPusherError, const std::string&)>& callback);

    // 获取统计信息
    struct Statistics
    {
        u64 llTotalSent = 0;        // 总发送数量
        u64 llSuccessSent = 0;      // 成功发送数量
        u64 llFailedSent = 0;       // 失败发送数量
        u64 llLastSendTime = 0;     // 最后发送时间
        u64 llConnectTime = 0;      // 连接时间
        u32 dwReconnectCount = 0;   // 重连次数
    };

    Statistics GetStatistics() const { return m_statistics; }

    // 重置统计信息
    void ResetStatistics();

protected:
    // 设置状态
    void SetStatus(EPusherStatus status);

    // 设置无锁状态
    void SetStatusUnsafe(EPusherStatus status);

    // 设置错误
    void SetError(EPusherError error, const std::string& errorMsg);

    // 检查是否需要重连
    bool ShouldReconnect() const;

    // 执行重连
    virtual bool DoReconnect();

    // 工作线程函数
    virtual void WorkerThread();

    // 处理发送队列
    virtual void ProcessSendQueue();

    // 添加数据到发送队列
    bool AddToSendQueue(const std::string& data);

    // 清空发送队列
    void ClearSendQueue();

    // 获取队列大小
    size_t GetQueueSize() const;

protected:
    // 配置信息
    TPusherConfig m_config;

    // 当前状态
    std::atomic<EPusherStatus> m_status;

    // 最后错误
    std::atomic<EPusherError> m_lastError;

    // 错误信息
    std::string m_strLastErrorMsg;

    // 是否运行中
    std::atomic<bool> m_bRunning;

    // 是否暂停
    std::atomic<bool> m_bPaused;

    // 工作线程
    std::thread m_workerThread;

    // 发送队列
    std::queue<std::string> m_sendQueue;

    // 队列互斥锁
    mutable std::mutex m_queueMutex;

    // 队列条件变量
    std::condition_variable m_queueCondition;

    // 状态互斥锁
    mutable std::mutex m_statusMutex;

    // 状态回调函数
    std::function<void(EPusherStatus, EPusherError, const std::string&)> m_statusCallback;

    // 统计信息
    Statistics m_statistics;

    // 统计信息互斥锁
    mutable std::mutex m_statisticsMutex;
};

#endif // _DATA_PUSHER_BASE_H_