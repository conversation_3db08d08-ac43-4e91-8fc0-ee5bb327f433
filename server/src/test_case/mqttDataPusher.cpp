#include "mqttDataPusher.h"
#include <iostream>
#include <chrono>
#include <random>
#include <thread>

CMqttDataPusher::CMqttDataPusher()
    : CDataPusherBase()
    , m_bMqttConnected(false)
    , m_packetIdCounter(1)
    , m_bStopFlag(false)
{
}

CMqttDataPusher::~CMqttDataPusher()
{
    Disconnect();
}

bool CMqttDataPusher::Init(const TMqttPusherConfig& config)
{
    m_mqttConfig = config;

    // 初始化基类配置
    TPusherConfig baseConfig;
    baseConfig.bEnable = config.bEnable;
    baseConfig.strAddr = config.strAddr;
    baseConfig.dwPort = config.dwPort;
    baseConfig.dwTimeoutMs = config.dwTimeoutMs;
    baseConfig.dwRetryCount = config.dwRetryCount;
    baseConfig.dwRetryIntervalMs = config.dwRetryIntervalMs;
    baseConfig.bAutoReconnect = config.bAutoReconnect;

    return CDataPusherBase::Init(baseConfig);
}

bool CMqttDataPusher::Init(const TPusherConfig& config)
{
    // 转换为MQTT配置
    TMqttPusherConfig mqttConfig;
    mqttConfig.bEnable = config.bEnable;
    mqttConfig.strAddr = config.strAddr;
    mqttConfig.dwPort = config.dwPort;
    mqttConfig.dwTimeoutMs = config.dwTimeoutMs;
    mqttConfig.dwRetryCount = config.dwRetryCount;
    mqttConfig.dwRetryIntervalMs = config.dwRetryIntervalMs;
    mqttConfig.bAutoReconnect = config.bAutoReconnect;

    // 设置默认MQTT参数
    mqttConfig.strClientId = "DataPusher_" + std::to_string(std::chrono::system_clock::now().time_since_epoch().count());
    mqttConfig.strTopic = "data/raw";

    return Init(mqttConfig);
}



bool CMqttDataPusher::Connect()
{
    std::lock_guard<std::mutex> lock(m_connectionMutex);

    if (m_bMqttConnected)
    {
        return true;
    }

    SetStatusUnsafe(EPusherStatus::CONNECTING);

    // 创建MQTT客户端
    if (!CreateMqttClient())
    {
        SetError(EPusherError::CONNECTION_FAILED, "创建MQTT客户端失败");
        return false;
    }

    try
    {
        // 异步连接
        m_mqttClient->async_connect(
            [this](MQTT_NS::error_code ec) {
                if (!ec) {
                    OnConnected();
                } else {
                    OnError(ec);
                }
            }
        );

        // 启动IO线程
        m_bStopFlag = false;
        m_workerThread = std::thread([this]() {
            try {
                m_ioContext.run();
            } catch (const std::exception& e) {
                std::cout << "MQTT IO线程异常: " << e.what() << std::endl;
            }
        });

        // 等待连接完成
        auto startTime = std::chrono::steady_clock::now();
        while (!m_bMqttConnected &&
               std::chrono::duration_cast<std::chrono::milliseconds>(
                   std::chrono::steady_clock::now() - startTime).count() < m_mqttConfig.dwTimeoutMs)
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }

        if (!m_bMqttConnected)
        {
            SetError(EPusherError::TIMEOUT, "MQTT连接超时");
            return false;
        }

        return true;
    }
    catch (const std::exception& e)
    {
        SetError(EPusherError::CONNECTION_FAILED, std::string("MQTT连接异常: ") + e.what());
        return false;
    }
}

bool CMqttDataPusher::Disconnect()
{
    std::lock_guard<std::mutex> lock(m_connectionMutex);

    if (!m_bMqttConnected)
    {
        return true;
    }

    try
    {
        // 断开MQTT连接
        if (m_mqttClient)
        {
            m_mqttClient->async_disconnect(
                [this](MQTT_NS::error_code ec) {
                    if (ec) {
                        std::cout << "MQTT断开连接错误: " << ec.message() << std::endl;
                    }
                    OnDisconnected();
                }
            );
        }

        // 停止IO上下文
        m_ioContext.stop();

        // 停止工作线程
        m_bStopFlag = true;
        if (m_workerThread.joinable())
        {
            m_workerThread.join();
        }

        m_mqttClient.reset();
        m_bMqttConnected = false;
        SetStatusUnsafe(EPusherStatus::DISCONNECTED);

        std::cout << "MQTT连接已断开" << std::endl;

        return true;
    }
    catch (const std::exception& e)
    {
        std::cout << "MQTT断开连接异常: " << e.what() << std::endl;
        return false;
    }
}

bool CMqttDataPusher::SendData(const std::string& data)
{
    if (!m_bMqttConnected)
    {
        SetError(EPusherError::SEND_FAILED, "MQTT未连接");
        return false;
    }

    SetStatusUnsafe(EPusherStatus::SENDING);

    try
    {
        // 生成包ID
        std::uint16_t packetId = 0;
        if (m_mqttConfig.dwQos > 0) {
            packetId = m_packetIdCounter++;
        }

        // 构造发布选项
        MQTT_NS::publish_options pubopts = static_cast<MQTT_NS::qos>(m_mqttConfig.dwQos);
        if (m_mqttConfig.bRetain) {
            pubopts |= MQTT_NS::retain::yes;
        }

        // 发布消息
        m_mqttClient->publish(
            packetId,
            m_mqttConfig.strTopic,
            data,
            pubopts
        );

        SetStatusUnsafe(EPusherStatus::CONNECTED);

        std::cout << "MQTT消息发布成功: 主题=" << m_mqttConfig.strTopic
                  << ", 负载长度=" << data.length() << ", 包ID=" << packetId << std::endl;
        return true;
    }
    catch (const std::exception& e)
    {
        SetError(EPusherError::SEND_FAILED, std::string("MQTT发送异常: ") + e.what());
        SetStatusUnsafe(EPusherStatus::CONNECTED);
        return false;
    }
}

bool CMqttDataPusher::CreateMqttClient()
{
    try
    {
        if (m_mqttConfig.bUseTls)
        {
            // TLS连接 (暂时不实现，需要额外的TLS配置)
            std::cout << "TLS连接暂不支持" << std::endl;
            return false;
        }
        else
        {
            // 创建TCP MQTT客户端
            m_mqttClient = mqtt::make_client(m_ioContext, m_mqttConfig.strAddr, m_mqttConfig.dwPort);
        }

        if (!m_mqttClient)
        {
            std::cout << "创建MQTT客户端失败" << std::endl;
            return false;
        }

        // 设置客户端参数
        m_mqttClient->set_client_id(m_mqttConfig.strClientId);
        m_mqttClient->set_clean_session(m_mqttConfig.bCleanSession);
        m_mqttClient->set_keep_alive_sec(m_mqttConfig.dwKeepAliveSeconds);

        if (!m_mqttConfig.strUsername.empty())
        {
            m_mqttClient->set_user_name(m_mqttConfig.strUsername);
            if (!m_mqttConfig.strPassword.empty())
            {
                m_mqttClient->set_password(m_mqttConfig.strPassword);
            }
        }

        // 设置回调
        SetMqttCallbacks();

        return true;
    }
    catch (const std::exception& e)
    {
        std::cout << "创建MQTT客户端异常: " << e.what() << std::endl;
        return false;
    }
}

void CMqttDataPusher::SetMqttCallbacks()
{
    // 简化版本，不需要设置回调
    // 实际项目中可以根据使用的MQTT库设置相应的回调
}

void CMqttDataPusher::OnConnected()
{
    m_bMqttConnected = true;
    SetStatusUnsafe(EPusherStatus::CONNECTED);

    // 更新连接时间统计
    {
        std::lock_guard<std::mutex> lock(m_statisticsMutex);
        m_statistics.llConnectTime = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
    }

    std::cout << "MQTT连接成功: " << m_mqttConfig.strAddr << ":" << m_mqttConfig.dwPort << std::endl;
}



void CMqttDataPusher::OnDisconnected()
{
    m_bMqttConnected = false;
    SetStatusUnsafe(EPusherStatus::DISCONNECTED);
    std::cout << "MQTT连接断开" << std::endl;
}

void CMqttDataPusher::OnPublished(std::uint16_t packet_id)
{
    // 从待确认列表中移除
    {
        std::lock_guard<std::mutex> lock(m_pendingMutex);
        m_pendingPublishes.erase(packet_id);
    }

    std::cout << "MQTT消息发布成功, 包ID: " << packet_id << std::endl;
}

void CMqttDataPusher::OnError(const mqtt::error_code& ec)
{
    std::string errorMsg = "MQTT错误: " + ec.message();
    SetError(EPusherError::NETWORK_ERROR, errorMsg);
    std::cout << errorMsg << std::endl;
}

void CMqttDataPusher::WorkerThreadFunc()
{
    std::cout << "MQTT工作线程启动" << std::endl;

    while (!m_bStopFlag)
    {
        try
        {
            // 检查连接状态（简化版本，实际项目中可以通过其他方式检查）
            if (!m_bMqttConnected)
            {
                std::cout << "检测到MQTT连接断开" << std::endl;

                // 如果启用自动重连，尝试重连
                if (m_mqttConfig.bAutoReconnect)
                {
                    ReconnectMqtt();
                }
            }

            // 清理超时的待确认包
            CheckMqttConnection();

            // 休眠一段时间
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        }
        catch (const std::exception& e)
        {
            std::cout << "MQTT工作线程异常: " << e.what() << std::endl;
        }
    }

    std::cout << "MQTT工作线程结束" << std::endl;
}

bool CMqttDataPusher::CheckMqttConnection()
{
    if (!m_mqttClient)
    {
        return false;
    }

    // 检查连接状态
    if (!m_bMqttConnected)
    {
        return false;
    }

    // 清理超时的待确认包
    {
        std::lock_guard<std::mutex> lock(m_pendingMutex);
        auto now = std::chrono::steady_clock::now();
        auto it = m_pendingPublishes.begin();

        while (it != m_pendingPublishes.end())
        {
            if (std::chrono::duration_cast<std::chrono::seconds>(now - it->second).count() > 30)
            {
                std::cout << "MQTT发布包超时, 包ID: " << it->first << std::endl;
                it = m_pendingPublishes.erase(it);
            }
            else
            {
                ++it;
            }
        }
    }

    return true;
}

bool CMqttDataPusher::ReconnectMqtt()
{
    std::cout << "尝试MQTT重连..." << std::endl;

    // 先断开现有连接
    Disconnect();

    // 等待一段时间后重连
    std::this_thread::sleep_for(std::chrono::milliseconds(m_mqttConfig.dwRetryIntervalMs));

    // 尝试重新连接
    if (Connect())
    {
        std::lock_guard<std::mutex> lock(m_statisticsMutex);
        m_statistics.dwReconnectCount++;
        std::cout << "MQTT重连成功" << std::endl;
        return true;
    }

    std::cout << "MQTT重连失败" << std::endl;
    return false;
}