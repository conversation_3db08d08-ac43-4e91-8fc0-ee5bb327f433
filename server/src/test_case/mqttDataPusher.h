#ifndef _MQTT_DATA_PUSHER_H_
#define _MQTT_DATA_PUSHER_H_

#include "dataPusherBase.h"
#include <memory>
#include <thread>
#include <map>
#include <chrono>
#include <mqtt_client_cpp.hpp>

// MQTT推送配置
typedef struct tagMqttPusherConfig : public TPusherConfig
{
    std::string strUsername;        // 用户名
    std::string strPassword;        // 密码
    std::string strClientId;        // 客户端ID
    std::string strTopic;           // 发布主题
    u32 dwQos = 1;                 // QoS等级 (0, 1, 2)
    bool bRetain = false;           // 是否保留消息
    bool bCleanSession = true;      // 是否清理会话
    u32 dwKeepAliveSeconds = 60;    // 心跳间隔(秒)
    bool bUseTls = false;           // 是否使用TLS
} TMqttPusherConfig;

// MQTT数据推送类
class CMqttDataPusher : public CDataPusherBase
{
public:
    CMqttDataPusher();
    virtual ~CMqttDataPusher();

    // 初始化配置
    bool Init(const TPusherConfig& config) override;

    // MQTT特定初始化
    bool Init(const TMqttPusherConfig& config);

    // 连接到目标
    bool Connect() override;

    // 断开连接
    bool Disconnect() override;

    // 发送数据
    bool SendData(const std::string& data) override;

protected:
    // 创建MQTT客户端
    bool CreateMqttClient();

    // 设置MQTT回调
    void SetMqttCallbacks();

    // 连接回调
    void OnConnected();

    // 断开连接回调
    void OnDisconnected();

    // 发布完成回调
    void OnPublished(std::uint16_t packet_id);

    // 错误回调
    void OnError(const boost::system::error_code& ec);

    // 检查MQTT连接状态
    bool CheckMqttConnection();

    // 重连MQTT
    bool ReconnectMqtt();

    // 工作线程函数
    void WorkerThreadFunc();

private:
    // MQTT配置
    TMqttPusherConfig m_mqttConfig;

    // MQTT客户端
    std::shared_ptr<mqtt::client<mqtt::tcp_endpoint<mqtt::as::ip::tcp::socket, mqtt::as::io_context::strand>>> m_mqttClient;

    // IO上下文
    mqtt::as::io_context m_ioContext;

    // 工作线程
    std::thread m_workerThread;

    // 是否已连接
    std::atomic<bool> m_bMqttConnected;

    // 连接互斥锁
    std::mutex m_connectionMutex;

    // 发布包ID计数器
    std::atomic<std::uint16_t> m_packetIdCounter;

    // 待确认的发布包
    std::map<std::uint16_t, std::chrono::steady_clock::time_point> m_pendingPublishes;

    // 待确认包互斥锁
    std::mutex m_pendingMutex;

    // 停止标志
    std::atomic<bool> m_bStopFlag;
};

#endif // _MQTT_DATA_PUSHER_H_