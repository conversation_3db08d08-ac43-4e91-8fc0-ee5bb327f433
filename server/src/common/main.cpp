#include "communication.h"
#include "data_capture_manager.h"
#include "database_manager.h"
#include "fileManager.h"
#include "interaction.h"
#include "logger.hpp"
#include "systemStatus.h"
#include "utils/inimanager/config_util.h"
#include "../test_case/testCaseManager.h"
// #include "utils/stringutil/stringutils.h"
#include "commonFunction.h"
#include "common_define.h"
#include "../heartbeat/heartbeat.h"


#include <iostream>
#include <unistd.h>
#include <stdlib.h>
#include <signal.h>
#include <memory>

fileutil::ConfigUtil config;
fileutil::ConfigUtil commongConfig;
volatile bool gIsExit = false;
volatile int gSystemStatus =  SYSTEM_STATUS_ONLINE;
volatile int gFactoryType = FACTORY_NONE;
volatile int gConnectType = CAPTURE_GUI;
volatile int gPort = 9999;
volatile int gIsStoreRaw = 0;
std::string gStrStorePath = "./";
std::string gCompressFilePath = "./";
std::string gTestCaseFilePath = "./";
std::string gTestCaseResultFilePath = "./";
volatile int gSoftwareVersion = 0;

// MQTT心跳配置全局变量
volatile int gMqttHeartbeatEnable = 0;
std::string gMqttServerHost = "localhost";
std::string gMqttServerPort = "1883";
std::string gMqttUsername = "";
std::string gMqttPassword = "";
std::string gMqttDeviceId = "data_capture_device_001";
std::string gMqttTopic = "heartbeat/data_capture";
std::string gSerialNo = "";
volatile int gMqttHeartbeatInterval = 10;
volatile int gMqttEnableTls = 0;

void handle_signal(int sig)
{
    printf("recv sigterm!\n");
    gIsExit = true;
}


int main(int argc,char *argv[])
{
   signal(SIGINT, handle_signal);
   signal(SIGTERM, handle_signal);

   // 加载配置
   int dwLogFileMaxSize = 0,dwLogMaxCnt = 0;
   std::string strLogName,strLogFileName;
   if(config.OpenConfig("../conf/log_config.ini"))
   {
      if (config.Move2Section("log"))
      {
         strLogName = config.GetValueByKey("loggername", "baseapp");
         strLogFileName = config.GetValueByKey("filename", "baseapp");
         dwLogFileMaxSize = atoi(config.GetValueByKey("filemaxsize", "0").c_str());
         dwLogMaxCnt = atoi(config.GetValueByKey("filemaxcnt", "0").c_str());
      }
   }

   if(commongConfig.OpenConfig("../conf/config.ini"))
   {
      if (commongConfig.Move2Section("data_factory"))
      {
         gFactoryType = atoi(commongConfig.GetValueByKey("factory","0").c_str());
      }

      if (commongConfig.Move2Section("client_connect"))
      {
         gConnectType = atoi(commongConfig.GetValueByKey("type","0").c_str());
         gPort = atoi(commongConfig.GetValueByKey("port","9999").c_str());
      }
      if (commongConfig.Move2Section("data_store"))
      {
         gStrStorePath = commongConfig.GetValueByKey("path", "./");
         if(gStrStorePath.back() != '/')
         {
            gStrStorePath += "/";
         }
         gIsStoreRaw = atoi(commongConfig.GetValueByKey("isStoreRaw","0").c_str());
         gTestCaseFilePath = commongConfig.GetValueByKey("testCaseFilePath", "./");
         if(gTestCaseFilePath.back() != '/')
         {
            gTestCaseFilePath += "/";
         }
         gCompressFilePath = commongConfig.GetValueByKey("compressFilePath", "./");
         if(gCompressFilePath.back() != '/')
         {
            gCompressFilePath += "/";
         }
         gTestCaseResultFilePath = commongConfig.GetValueByKey("testCaseResultFilePath", "./");
         if(gTestCaseResultFilePath.back() != '/')
         {
            gTestCaseResultFilePath += "/";
         }
      }
      if (commongConfig.Move2Section("software"))
      {
         gSoftwareVersion = atoi(commongConfig.GetValueByKey("softwareVersion","0").c_str());
      }

      // 读取MQTT心跳配置
      if (commongConfig.Move2Section("mqtt_heartbeat"))
      {
         gMqttHeartbeatEnable = atoi(commongConfig.GetValueByKey("enable","0").c_str());
         gMqttServerHost = commongConfig.GetValueByKey("server_host", "localhost");
         gMqttServerPort = commongConfig.GetValueByKey("server_port", "1883");
         gMqttUsername = commongConfig.GetValueByKey("username", "");
         gMqttPassword = commongConfig.GetValueByKey("password", "");
         gMqttDeviceId = commongConfig.GetValueByKey("device_id", "data_capture_device_001");
         gMqttTopic = commongConfig.GetValueByKey("topic", "heartbeat/data_capture");
         gMqttHeartbeatInterval = atoi(commongConfig.GetValueByKey("heartbeat_interval_sec","10").c_str());
         gMqttEnableTls = atoi(commongConfig.GetValueByKey("enable_tls","0").c_str());
         gSerialNo = commongConfig.GetValueByKey("serialNo", "");
      }
   }

   // 初始化日志
   Logger::SetLogLevel(argc, argv);
   Logger::Instance().InitLogger(strLogName, strLogFileName, dwLogFileMaxSize, dwLogMaxCnt);

   INFO("____________________SERVER START____________________");


   //初始化中介者
   CInteraction *pInteraction = new CInteraction();

   //实例化各模块
   CFileManager *pFileManager = new CFileManager(pInteraction);
   CCommunication *pCommunication = new CCommunication(pInteraction);
   CDataCaptureManager *pDataCaptureManager = new CDataCaptureManager(pInteraction);
   CDatabaseManager *pDatabaseManager = new CDatabaseManager(pInteraction);
   CSystemStatus *pSystemStatus = new CSystemStatus(pInteraction);
   CTestCaseManager *pTestCaseManager = new CTestCaseManager(pInteraction);

   //注册各模块
   pInteraction->AddModule(pFileManager);
   pInteraction->AddModule(pCommunication);
   pInteraction->AddModule(pDataCaptureManager);
   pInteraction->AddModule(pDatabaseManager);
   pInteraction->AddModule(pSystemStatus);
   pInteraction->AddModule(pTestCaseManager);
   //初始化
   pInteraction->Notify(NULL,MSG_LEVEL_ALL_DEVICE,NO_DEVICE,SYSTEM_INIT, nullptr);

   sleep(1);

   // 启动MQTT心跳服务
   std::unique_ptr<HeartbeatSender> pHeartbeatSender = nullptr;
   if (gMqttHeartbeatEnable) {
      try {
         HeartbeatSender::Config heartbeat_config;
         heartbeat_config.server_host = gMqttServerHost;
         heartbeat_config.server_port = gMqttServerPort;
         heartbeat_config.username = gMqttUsername;
         heartbeat_config.password = gMqttPassword;
         heartbeat_config.device_id = gMqttDeviceId;
         heartbeat_config.serialNo = gSerialNo;
         heartbeat_config.topic = gMqttTopic;
         heartbeat_config.heartbeat_interval_sec = gMqttHeartbeatInterval;
         heartbeat_config.enable_tls = (gMqttEnableTls != 0);

         pHeartbeatSender = std::make_unique<HeartbeatSender>(heartbeat_config);

         if (pHeartbeatSender->start()) {
            INFO("MQTT heartbeat service started successfully");
         } else {
            ERROR("Failed to start MQTT heartbeat service");
            pHeartbeatSender.reset();
         }
      } catch (const std::exception& e) {
         ERROR("Exception while starting MQTT heartbeat service: {}", e.what());
         pHeartbeatSender.reset();
      }
   } else {
      INFO("MQTT heartbeat service is disabled in configuration");
   }

   pCommunication->Start();

   while(!gIsExit)
   {
      pInteraction->Notify(NULL,MSG_LEVEL_ALL_DEVICE,NO_DEVICE,SYSTEM_INFO_REQ,nullptr);
      std::this_thread::sleep_for(std::chrono::seconds(1)); // 使用现代的休眠函数
   }

   sleep(1);
   pInteraction->Notify(NULL, MSG_LEVEL_ALL_DEVICE,NO_DEVICE,SYSTEM_STOP, nullptr);
   if(pTestCaseManager)
   {
      pTestCaseManager->Stop();
   }
   // 停止MQTT心跳服务
   if (pHeartbeatSender) {
      INFO("Stopping MQTT heartbeat service...");
      pHeartbeatSender->stop();
      pHeartbeatSender.reset();
      INFO("MQTT heartbeat service stopped");
   }

   pCommunication->Stop();
   sleep(1);

   if(commongConfig.OpenConfig("../conf/config.ini"))
   {
      if (commongConfig.Move2Section("data_store"))
      {
         commongConfig.WriteKey("path",gStrStorePath);
      }
   }
   commongConfig.Save2File();

   Logger::Instance().CleanLogger();

   return 0;
}