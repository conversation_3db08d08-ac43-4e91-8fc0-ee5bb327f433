#include "commonFunction.h"
#include "struct.h"
#include <string.h>
#include <ctime>
#include <chrono>
#include <random>
#include <fstream>
#include <sys/stat.h>
#include <iostream>
#include "logger.hpp"
#include <sstream>
#include <vector>
#include "zip.h"
#include <dirent.h>

namespace commonFunc
{
    u64 GetUsTime()
    {
        auto duration_since_epoch = std::chrono::system_clock::now().time_since_epoch();                                     // 从1970-01-01 00:00:00到当前时间点的时长
        auto microseconds_since_epoch = std::chrono::duration_cast<std::chrono::microseconds>(duration_since_epoch).count(); // 将时长转换为微秒数
        time_t seconds_since_epoch = static_cast<time_t>(microseconds_since_epoch / 1000000);                                // 将时长转换为秒数
        std::tm current_time;
        localtime_r(&seconds_since_epoch, &current_time);          // 获取当前时间（精确到秒）
        auto tm_microsec = microseconds_since_epoch % 1000;        // 当前时间的微妙数
        auto tm_millisec = microseconds_since_epoch / 1000 % 1000; // 当前时间的毫秒数
        unsigned long long lltime = (unsigned long long)seconds_since_epoch * 1000 * 1000 + tm_millisec * 1000 + tm_microsec;
        return lltime;
    }

    u64 GetMsTime()
    {
        auto now = std::chrono::system_clock::now();
        auto duration = now.time_since_epoch();

        // 获取时间戳，精确到毫秒
        long long timestamp_ms = std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();
        // return timestamp_ms + 28800 * 1000;
        return timestamp_ms;
    }

    u32 GetBJTimeHour(u64 llMsTimestamp)
    {

        // return  ((llMsTimestamp/(1000*60*60)) %24)+8;
        return ((llMsTimestamp / (1000 * 60 * 60)));
    }

    // 查找str结尾是否为suffix true:是 false:否
    bool endsWith(const char *str, const char *suffix)
    {
        size_t str_len = strlen(str);
        size_t suffix_len = strlen(suffix);
        return (str_len >= suffix_len) && (strcmp(str + str_len - suffix_len, suffix) == 0);
    }

    // 获取路口id 格式要求 ID_XXXX
    std::string extractRoadId(const std::string &input)
    {
        size_t lastUnderscorePos = input.find_last_of('_');
        if (lastUnderscorePos != std::string::npos)
        {
            // 提取路口id
            std::string roadId = input.substr(0, lastUnderscorePos);
            return roadId;
        }
        else
        {
            ERROR("extractRoadId error");
        }
        return "";
    }

    u64 extractTimestamp(const std::string &input)
    {
        do
        {
            size_t lastUnderscorePos = input.find_last_of('_');
            if (lastUnderscorePos == std::string::npos)
            {
                break;
            }
            size_t dotPos = input.find_last_of('.');
            if (dotPos == std::string::npos)
            {
                break;
            }
            std::string timestamp = input.substr(lastUnderscorePos + 1, dotPos - lastUnderscorePos - 1); // 提取从最后一个下划线后面到倒数第二个点的部分
            return std::stoull(timestamp);

        } while (0);
        ERROR("extractTimestamp error");
        return 0;
    }

    static std::unordered_set<std::string> generatedStrings; // 存储已经生成过的字符串
    std::string generateUniqueRandomString(int length)
    {
        std::string characters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        std::string randomString;

        std::random_device rd;
        std::mt19937 generator(rd());
        std::uniform_int_distribution<int> distribution(0, characters.length() - 1);

        do
        {
            randomString.clear(); // 清空之前的字符串
            for (int i = 0; i < length; ++i)
            {
                randomString += characters[distribution(generator)];
            }
        } while (generatedStrings.find(randomString) != generatedStrings.end());

        // generatedStrings.insert(randomString); // 将新生成的字符串加入集合

        return randomString;
    }

    // 检查文件是否存在
    bool isFileExist(const std::string &path)
    {
        struct stat info;
        if (stat(path.c_str(), &info) == 0)
        {
            // 文件存在
            return true;
        }
        else
        {
            // 文件不存在
            return false;
        }
    }

    bool createFile(const std::string &directory, const std::string &filename)
    {
        struct stat info;
        if (stat(directory.c_str(), &info) == 0)
        {
            // 文件存在
            return false;
        }

        int status = mkdir(directory.c_str(), S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH);
        if (status == 0)
        {
            return true;
        }
        else
        {
            perror("Failed to create directory");
            return false;
        }
        // std::ofstream ofs(directory + "/" + filename);
        // if (!ofs)
        // {
        //     return false;
        // }
        // ofs.close();
        // return true;
    };
bool createDirectory(const std::string& path) {
    size_t pos = 0;
    std::string dir;
    int ret = 0;
    
    while ((pos = path.find_first_of('/', pos + 1)) != std::string::npos) {
        dir = path.substr(0, pos);
        ret = mkdir(dir.c_str(), 0755);
        if (ret != 0 && errno != EEXIST) {
            return false;
        }
    }
    
    // Create the last directory
    ret = mkdir(path.c_str(), 0755);
    if (ret != 0 && errno != EEXIST) {
        return false;
    }
    
    return true;
}

bool unzipFile(const std::string& srcZipFile, const std::string& dstDirectory) {
    // 1. 检查并创建目标目录
    struct stat st;
    if (stat(dstDirectory.c_str(), &st) != 0) {
        if (!createDirectory(dstDirectory)) {
            ERROR("Failed to create destination directory: {}", dstDirectory);
            return false;
        }
    }

    // 2. 打开zip文件
    int err = 0;
    zip_t* zipArchive = zip_open(srcZipFile.c_str(), ZIP_RDONLY, &err);
    if (zipArchive == nullptr) {
        zip_error_t error;
        zip_error_init_with_code(&error, err);
        ERROR("Failed to open zip file {}: {}", srcZipFile, zip_error_strerror(&error));
        zip_error_fini(&error);
        return false;
    }

    // 3. 遍历zip条目
    bool success = true;
    zip_int64_t numEntries = zip_get_num_entries(zipArchive, 0);
    for (zip_int64_t i = 0; i < numEntries; ++i) {
        const char* entryName = zip_get_name(zipArchive, i, 0);
        if (!entryName) {
            ERROR("Failed to get entry name at index {}", i);
            success = false;
            continue;
        }

        // 4. 构建完整路径
        std::string fullPath = dstDirectory + "/" + entryName;

        // 5. 处理目录项
        if (entryName[strlen(entryName)-1] == '/') {
            if (!createDirectory(fullPath)) {
                ERROR("Failed to create directory: {}", fullPath);
                success = false;
            }
            continue;
        }

        // 6. 确保父目录存在
        std::string parentDir = fullPath.substr(0, fullPath.find_last_of('/'));
        if (!parentDir.empty() && stat(parentDir.c_str(), &st) != 0) {
            if (!createDirectory(parentDir)) {
                ERROR("Failed to create parent directory: {}", parentDir);
                success = false;
                continue;
            }
        }

        // 7. 解压文件
        zip_file_t* file = zip_fopen_index(zipArchive, i, 0);
        if (!file) {
            ERROR("Failed to open zip entry {}: {}", entryName, zip_strerror(zipArchive));
            success = false;
            continue;
        }

        std::ofstream ofs(fullPath, std::ios::binary);
        if (!ofs) {
            ERROR("Failed to create file: {}", fullPath);
            zip_fclose(file);
            success = false;
            continue;
        }

        std::vector<char> buffer(1024 * 16); // 16KB buffer
        zip_int64_t bytesRead;
        while ((bytesRead = zip_fread(file, buffer.data(), buffer.size())) > 0) {
            ofs.write(buffer.data(), bytesRead);
            if (!ofs) {
                ERROR("Failed to write to file: {}", fullPath);
                success = false;
                break;
            }
        }

        if (bytesRead < 0) {
            ERROR("Failed to read zip entry {}: {}", entryName, zip_strerror(zipArchive));
            success = false;
        }

        ofs.close();
        zip_fclose(file);
    }

    zip_close(zipArchive);
    return success;
}

    std::string convertUtcTimestampToDateTimeString(u64 timestamp)
    {
        timestamp += 60 * 60 * 8 * 1000;                   // 调整时差
        time_t ts = static_cast<time_t>(timestamp / 1000); // 将毫秒级的时间戳转换为秒级
        struct tm *timeinfo;
        char buffer[80];

        timeinfo = gmtime(&ts);                              // 使用gmtime将时间戳转换为UTC时间
        strftime(buffer, 80, "%Y-%m-%d_%H:%M:%S", timeinfo); // 格式化时间字符串
        return buffer;
    }

    std::vector<std::string> splitString(const std::string& s, char delim) 
    {
    std::vector<std::string> tokens;
    std::string token;
    std::istringstream tokenStream(s);
    while (std::getline(tokenStream, token, delim)) {
        tokens.push_back(token);
    }
    return tokens;
}

// 获取 CPU 核心数
int getCPUCores() { return sysconf(_SC_NPROCESSORS_ONLN); }

// 获取 CPU 使用率
float getCPUUsage() {
  std::ifstream file("/proc/stat");
  std::string line;
  std::getline(file, line); // 读取第一行
  // 提取每个核心的使用时间
  long user, nice, system, idle, iowait, irq, softirq;
  sscanf(line.c_str(), "cpu %ld %ld %ld %ld %ld %ld %ld", &user, &nice, &system,
         &idle, &iowait, &irq, &softirq);
  long totalIdle = idle + iowait;
  long totalNonIdle = user + nice + system + irq + softirq;
  long total = totalIdle + totalNonIdle;
  return 100.0 * totalNonIdle / total;
}

// 获取 CPU 温度（需要根据实际情况修改）
float getTemperature() {
  // 这里假设你有一个可以读取 CPU 温度的函数或文件
  // 你需要根据你的系统和 CPU 来确定如何获取温度
  // 这里只是一个示例，实际情况可能会有所不同
  return 0.0; // 假设温度为 0
}

// 获取内存总容量
long getMemoryTotal() {
  struct sysinfo memInfo;
  sysinfo(&memInfo);
  return memInfo.totalram / (1024 * 1024); // 转换为 MB
}

// 获取内存使用率
float getMemoryUsage() {
  struct sysinfo memInfo;
  sysinfo(&memInfo);
  long totalMemory = memInfo.totalram;
  long freeMemory = memInfo.freeram;
  long usedMemory = totalMemory - freeMemory;
  return (float)usedMemory / totalMemory * 100.0;
}

// 获取磁盘总容量
long getDiskTotal(const char *path) {
  struct statvfs stat;
  if (statvfs(path, &stat) != 0) {
    return -1; // 获取失败
  }
  return (stat.f_frsize * stat.f_blocks) / (1024 * 1024); // 转换为 MB
}

// 获取磁盘使用率
float getDiskUsage(const char *path) {
  struct statvfs stat;
  if (statvfs(path, &stat) != 0) {
    return -1.0; // 获取失败
  }
  long totalSpace = stat.f_frsize * stat.f_blocks;
  long freeSpace = stat.f_frsize * stat.f_bfree;
  long usedSpace = totalSpace - freeSpace;
  return (float)usedSpace / totalSpace * 100.0;
}

// 检查是否是目录
bool isDirectory(const std::string& path) {
    struct stat statbuf;
    if (stat(path.c_str(), &statbuf) != 0) return false;
    return S_ISDIR(statbuf.st_mode);
}

// 检查是否是普通文件
bool isRegularFile(const std::string& path) {
    struct stat statbuf;
    if (stat(path.c_str(), &statbuf) != 0) return false;
    return S_ISREG(statbuf.st_mode);
}

// 获取目录下所有条目
std::vector<std::string> listDirectory(const std::string& path) {
    std::vector<std::string> result;
    DIR* dir = opendir(path.c_str());
    if (dir == nullptr) return result;

    struct dirent* entry;
    while ((entry = readdir(dir)) != nullptr) {
        // 跳过 "." 和 ".."
        if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0) {
            continue;
        }
        result.push_back(entry->d_name);
    }
    closedir(dir);
    return result;
}
int64_t getCreationTimestamp(const std::string& path) {
    struct stat statbuf;
    if (stat(path.c_str(), &statbuf) != 0) {
        return 0; // 获取失败返回0
    }
    
    // 获取秒级时间
    time_t sec_time;
    #if defined(__APPLE__)
        sec_time = statbuf.st_birthtime; // MacOS创建时间
    #elif defined(_WIN32)
        sec_time = statbuf.st_ctime;     // Windows创建时间
    #else
        sec_time = statbuf.st_ctime;     // Linux最后状态更改时间
    #endif
    
    // 转换为毫秒级时间戳
    auto millisec = std::chrono::seconds(sec_time);
    return std::chrono::duration_cast<std::chrono::milliseconds>(millisec).count();
}

bool deleteFolder(const std::string& folderPath) {
    // 打开目录
    DIR* dir = opendir(folderPath.c_str());
    if (dir == nullptr) {
        return false; // 无法打开目录
    }

    // 遍历目录内容
    struct dirent* entry;
    bool success = true;
    
    while ((entry = readdir(dir)) != nullptr) {
        // 跳过 "." 和 ".."
        if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0) {
            continue;
        }

        // 构建完整路径
        std::string fullPath = folderPath + "/" + entry->d_name;

        // 检查文件类型
        struct stat statbuf;
        if (lstat(fullPath.c_str(), &statbuf) != 0) {
            success = false;
            continue;
        }

        if (S_ISDIR(statbuf.st_mode)) {
            // 递归删除子目录
            if (!deleteFolder(fullPath)) {
                success = false;
            }
        } else {
            // 删除文件
            if (unlink(fullPath.c_str()) != 0) {
                success = false;
            }
        }
    }
    closedir(dir);

    // 删除空目录
    if (rmdir(folderPath.c_str()) != 0) {
        success = false;
    }

    return success;
}
}


