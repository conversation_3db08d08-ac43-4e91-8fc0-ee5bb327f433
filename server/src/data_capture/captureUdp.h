#ifndef _CAPTURE_UDP_H_
#define _CAPTURE_UDP_H_


#include <functional>
#include <memory>
#include <mutex>
#include <thread>
#include <chrono>
#include <string>
#include <atomic>
#include <system_error>
#include <netinet/in.h>
#include "captureBase.h"

extern std::string gTestCaseResultFilePath;
class CCaptureUdp : public CCaptureBase{
public:
    // 构造函数和析构函数
    CCaptureUdp(CDataCaptureManager &manager);
    ~CCaptureUdp();

    void Start() override;
    void Stop() override;
    void SetConfig(TConnectArgs stArgs) override;
    void SetCallBack(const std::function<void(TRawData stCapData)> &pCb) override;
    void statusSendThread() override;


    // // 控制接口
    // void Start();
    // void Stop();
    // bool IsRunning() const;

private:
    void ReceiverThread();

    std::string m_strBindIp;
    int m_dwBindPort = 0;
    int m_dwbBufferSize = 8192;
    std::function<void(TRawData stCapData)> m_pCb;
    // DataCallback callback;
    std::mutex m_mutex;
    std::thread m_receiver_thread;
    int m_dwSockfd = -1;
    std::string m_strCrossId;
};

#endif
