#ifndef _DATA_CPATURE_MANAGER_H_
#define _DATA_CPATURE_MANAGER_H_

#include "captureMqttCpp.h"
#include "moduleBase.h"
// #include "captureMqtt.h"
#include "captureTcpServer.h"
#include "captureKafka.h"
#include "struct.h"
#include "captureUdp.h"


#include "utils/inimanager/config_util.h"
// #include "utils/stringutil/stringutils.h"
#include <mutex>


#define CONNECT_TYPE_MQTT 1
#define CONNECT_TYPE_HTTP 2
#define CONNECT_TYPE_KAFKA 3

extern volatile int gSystemStatus;

class CDataCaptureManager :public CModuleBase
{
    public:
    CDataCaptureManager(CMediator *pMediator);
    ~CDataCaptureManager();

    void Init() override;
    void Start() override;
    void Stop() override;
    void Pause() override;

    bool MsgFilter(u32 msgType);
    void HandleMsg(u32 msgLevel,u32 deviceId,u32 msgType,std::shared_ptr<void> spData);

    void dataRecv(TRawData stCapData);
    void updateStatus(std::string strId,bool isCap,float fFreq,u32 recvDataCnt);

    protected:
    bool updateConfig();
    bool parseConfigFromMsg(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);
    bool configReq(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);
    bool readConfig();
    void sendStatus(u64 startTime);

    private:
    int m_dwConnectType;
    int m_dwMaxConnect;
    fileutil::ConfigUtil m_config;
    std::vector<std::string> m_vecRoadId;
    std::vector<TConnectArgs> m_vecConnectArgs;
    std::vector<CCaptureBase*> m_vecHandle;
    std::mutex m_vecStatusMutex;
    std::vector<TCapStatusUpdate> m_vecStatus;

    std::mutex m_mutex;
    std::thread m_sendThread;

};





#endif