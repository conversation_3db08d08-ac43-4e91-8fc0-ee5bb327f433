#include "captureUdp.h"
#include "logger.hpp"
#include <cstring>
#include <unistd.h>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <stdexcept>
#include "commonFunction.h"
#include "data_capture_manager.h"
#include "json/json.h"

CCaptureUdp::CCaptureUdp(CDataCaptureManager &manager)
    : CCaptureBase(manager)
{
    m_dwbBufferSize = 8192;
}

CCaptureUdp::~CCaptureUdp()
{
    Stop();
}

void CCaptureUdp::Start()
{
       
    if (m_bIsRunning){
        ERROR("Receiver is already running");
        return;
    }
    CCaptureBase::Start();
    // 创建socket
    m_dwSockfd = socket(AF_INET, SOCK_DGRAM, 0);
    if (m_dwSockfd < 0) {
        ERROR("Failed to create socket");
        return;
    }
    int flags = fcntl(m_dwSockfd, F_GETFL, 0);
    fcntl(m_dwSockfd, F_SETFL, flags | O_NONBLOCK); // 设置为非阻塞

    // 设置接收超时为5秒
    struct timeval tv;
    tv.tv_sec = 5; // 5秒超时
    tv.tv_usec = 0;
    setsockopt(m_dwSockfd, SOL_SOCKET, SO_RCVTIMEO, &tv, sizeof(tv));

    int optval = 1;
    if (setsockopt(m_dwSockfd, SOL_SOCKET, SO_REUSEADDR,
                   &optval, sizeof(optval)) < 0)
    {
        close(m_dwSockfd);
        ERROR("Failed to set SO_REUSEADDR");
        return;
    }
    // 设置socket选项
    int buf_size = static_cast<int>(m_dwbBufferSize);
    if (setsockopt(m_dwSockfd, SOL_SOCKET, SO_RCVBUF, &buf_size, sizeof(buf_size)) < 0) {
        close(m_dwSockfd);
        ERROR("Failed to set receive buffer size");
        return;
    }

    // 绑定地址
    sockaddr_in addr{};
    addr.sin_family = AF_INET;
    addr.sin_port = htons(m_dwBindPort);
    
    if (m_strBindIp.empty() || m_strBindIp == "0.0.0.0") {
        addr.sin_addr.s_addr = INADDR_ANY;
    } else {
        if (inet_pton(AF_INET, m_strBindIp.c_str(), &addr.sin_addr) <= 0) {
            close(m_dwSockfd);
            ERROR("Invalid IP address: " + m_strBindIp);
            return;
        }
    }

    if (bind(m_dwSockfd, reinterpret_cast<const sockaddr*>(&addr), sizeof(addr)) < 0) {
        close(m_dwSockfd);
        ERROR("Failed to bind socket");
        return;
    }

    // 启动接收线程
    m_bIsRunning= true;
    m_receiver_thread= std::thread(&CCaptureUdp::ReceiverThread, this);
}

void CCaptureUdp::SetConfig(TConnectArgs stArgs) {
    size_t colonPos = stArgs.strAddr.find(':');
    if (colonPos == std::string::npos)
    {
        ERROR("this is no ip and port");
        return;
    }

    m_strBindIp = stArgs.strAddr.substr(0, colonPos);
    m_dwBindPort = atoi(stArgs.strAddr.substr(colonPos + 1).c_str());
    m_strCrossId = stArgs.strRoadId;
};
void CCaptureUdp::SetCallBack(const std::function<void(TRawData stCapData)> &pCb)
{
    if (pCb != NULL)
    {
        m_pCb = pCb;
    }
}
void CCaptureUdp::statusSendThread()
{
    u32 recvDataCnt = 0;
    u64 llLastTime = commonFunc::GetMsTime();
    while (m_bIsRunning)
    {   
        u64 llNowTime = commonFunc::GetMsTime();
        if (llNowTime - llLastTime >= 60 * 1000)
        {
            m_fFreq = static_cast<float>(m_dwMsgCnt)  / 60;
            m_mutex.lock();
            recvDataCnt = m_dwMsgCnt;
            m_dwMsgCnt = 0;
            m_mutex.unlock();
            llLastTime = llNowTime;
        }
        m_manager.updateStatus(m_strCrossId, m_bIsCaping, m_fFreq,recvDataCnt);
        m_bIsCaping = false;
        sleep(1);
    }
}

void CCaptureUdp::Stop() {
    if (!m_bIsRunning) return;

    m_bIsRunning = false;
    
    // 通过发送空包唤醒接收线程
    if (m_dwSockfd!= -1) {
        sockaddr_in addr{};
        addr.sin_family = AF_INET;
        addr.sin_port = htons(m_dwBindPort);
        inet_pton(AF_INET, "127.0.0.1", &addr.sin_addr);
        sendto(m_dwSockfd, "", 0, 0, 
              reinterpret_cast<const sockaddr*>(&addr), sizeof(addr));
    }

    if (m_receiver_thread.joinable()) {
        m_receiver_thread.join();
    }

    if (m_dwSockfd != -1) {
        close(m_dwSockfd);
        m_dwSockfd = -1;
    }
    CCaptureBase::Stop();
}

void CCaptureUdp::ReceiverThread() {
    std::unique_ptr<char[]> buffer(new char[m_dwbBufferSize]);

    // 记录最后接收时间（可保留用于日志或调试）
    auto lastRecvTime = std::chrono::steady_clock::now();

    // 记录日志的文件和互斥锁
    // std::ofstream logFile;
    // std::mutex logMutex;

    // // 构建文件名（基于时间）
    // {
    //     auto t = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
    //     std::tm tm;
    //     localtime_r(&t, &tm);
    //     std::ostringstream oss;
    //     oss << gTestCaseResultFilePath+"capture_" << std::put_time(&tm, "%Y%m%d_%H%M%S") << ".txt";
    //     logFile.open(oss.str(), std::ios::out);
    //     if (!logFile.is_open()) {
    //         ERROR("无法打开日志文件");
    //         return;
    //     }
    // }

    // JSON 写入器初始化（紧凑格式 + 控制浮点精度）
    Json::StreamWriterBuilder writerBuilder;
    writerBuilder["indentation"] = "";
    writerBuilder["precision"] = 3;
    std::unique_ptr<Json::StreamWriter> jsonWriter(writerBuilder.newStreamWriter());

    while (m_bIsRunning) {
        sockaddr_in from_addr{};
        socklen_t from_len = sizeof(from_addr);

        ssize_t n = recvfrom(m_dwSockfd, buffer.get(), m_dwbBufferSize, 0,
                            reinterpret_cast<sockaddr*>(&from_addr), &from_len);
        if (n > 0) {
            m_bIsCaping = true;
            lastRecvTime = std::chrono::steady_clock::now();

            {
                std::lock_guard<std::mutex> lock(m_mutex);
                m_dwMsgCnt++;
            }

            auto timestamp = std::chrono::system_clock::now();
            std::string recvdMsg(buffer.get(), n);

            // 尝试解析收到的数据为 JSON
            Json::Reader reader;
            Json::Value valueJson;
            if (!reader.parse(recvdMsg, valueJson) || !valueJson.isObject()) {
                ERROR("接收到的数据不是合法的 JSON 对象，跳过处理");
                continue;
            }
            // 去掉头
            Json::Value valueBody;
            if(valueJson.isMember("value")&&valueJson["value"].isObject())
            {
                valueBody = valueJson["value"];
            }
            // 从 value 中提取 msgCnt
            int msgCnt = 0;
            if (valueBody.isMember("msgCnt") && valueBody["msgCnt"].isInt()) {
                msgCnt = valueBody["msgCnt"].asInt();
            }

            // 构造完整封装 JSON
            Json::Value fullJson;
            fullJson["timeStamp"] = static_cast<Json::UInt64>(
                std::chrono::duration_cast<std::chrono::milliseconds>(timestamp.time_since_epoch()).count()
            );
            fullJson["msgCnt"] = msgCnt;
            fullJson["value"] = valueBody;
            if(valueBody.isMember("participants"))
            {
                fullJson["type"] = "rsm"; 
            }else if(valueBody.isMember("nodes"))
            {
                fullJson["type"] = "map"; 
            }else if(valueBody.isMember("intersections"))
            {
                fullJson["type"] = "spat"; 
            }else if(valueBody.isMember("rtes")||valueBody.isMember("rtss"))
            {
                fullJson["type"] = "rsi"; 
            }else if(valueBody.isMember("secMark"))
            {
                fullJson["type"] = "bsm"; 
            }else
            {
                fullJson["type"] = "unkown"; 
            }
            std::ostringstream oss;
            jsonWriter->write(fullJson, &oss);
            // 回调原始接收到的数据（不影响原逻辑）
            if (m_pCb) {
                TRawData stCapData;
                stCapData.llRecvTime = commonFunc::GetMsTime();
                stCapData.strCrossroadId = m_strCrossId;
                stCapData.wDataLength =  oss.str().length();
                stCapData.strData = oss.str();
                m_pCb(stCapData);
            }

        } else if (n == 0) {
            // 收到唤醒包
            continue;
        } else {
            if (errno == EAGAIN || errno == EWOULDBLOCK) {
                // 不再中断接收，仅等待重试（保持连接）
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                continue;
            } else if (errno == EINTR) {
                continue; // 被信号中断，继续
            } else {
                // 其他严重错误，退出接收
                ERROR("recvfrom 发生错误，退出线程，错误码: " + std::to_string(errno));
                break;
            }
        }
    }

    // // 关闭日志文件
    // {
    //     std::lock_guard<std::mutex> lock(logMutex);
    //     if (logFile.is_open()) {
    //         logFile.close();
    //     }
    // }
}