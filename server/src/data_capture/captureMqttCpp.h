#ifndef _CAPTURE_MQTT_CPP_H_
#define _CAPTURE_MQTT_CPP_H_

#define MQTT_USE_WS
#include "captureBase.h"

#include "mqtt_cpp/mqtt_client_cpp.hpp"
#include "logger.hpp"
#include "json/json.h"
#include <cstring>
#include <stdexcept>
#include <unistd.h>
#include <fstream>

#define MQTT_UNKNOW 0
#define MQTT_TCP 1
#define MQTT_WEBSOCKET 2
#define MQTT_TLS 3
#define MQTT_TLS_WEBSOCKET 4
extern std::string gTestCaseResultFilePath;
volatile extern int gSoftwareVersion;
class CCaptureMqttCpp : public CCaptureBase 
{
public:
    CCaptureMqttCpp(CDataCaptureManager &manager);
    ~CCaptureMqttCpp();

    void Start() override;
    void Stop() override;
    void SetConfig(TConnectArgs stArgs) override;
    void SetCallBack(const std::function<void(TRawData stCapData)> &pCb) override;

    void statusSendThread() override;
    void handleMsg(const std::string msg);
private:
    bool openLogFile();
    bool writeLogFile(const std::string &msg,u64 llNowTime);
    bool closeLogFile();

private:
    boost::asio::io_context m_ioc;
    std::uint16_t m_pid_sub1;
    std::uint16_t m_pid_sub2;

    std::string m_strUserName;
    std::string m_strPassword;
    std::string m_straddr;
    std::string m_strClientId;
    std::string m_strTopic;
    std::string m_strCrossId;
    std::string m_strPort;
    std::string m_strUrl;
    int m_connectType;
    bool m_bIsConnectedSucceed;

    std::function<void()> m_disconnectFunc;
    std::mutex m_mutex;
    std::function<void(TRawData stCapData)> m_pCb;
    std::thread m_runThread;

    Json::StreamWriterBuilder m_writerBuilder;
    std::unique_ptr<Json::StreamWriter> m_jsonWriter;
    std::ofstream m_logFile;
    std::mutex m_logMutex;
    bool m_bIsFileOpend = false;

};

#endif