cmake_minimum_required(VERSION 3.0.2)

aux_source_directory(. DIR_SRC)

add_library(datacapture SHARED ${DIR_SRC})

# target_link_libraries(datacapture rdkafka cppkafka paho-mqttpp3 paho-mqtt3c paho-mqtt3a jsoncpp pthread)
target_link_libraries(datacapture cppkafka  rdkafka pthread jsoncpp)

add_custom_command(TARGET datacapture POST_BUILD
 COMMAND
 mv libdatacapture.so libdatacapture.so.${PROJECT_VERSION}
 COMMAND
 ln -s libdatacapture.so.${PROJECT_VERSION} libdatacapture.so
 WORKING_DIRECTORY ${LIBRARY_OUTPUT_PATH})
