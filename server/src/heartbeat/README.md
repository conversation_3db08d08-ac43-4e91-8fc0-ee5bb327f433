# MQTT心跳发送器 (HeartbeatSender)

## 概述

HeartbeatSender是一个基于MQTT协议的心跳发送器，用于定期向MQTT服务器发送设备心跳数据。该类具有高鲁棒性、低资源消耗的特点，支持自动重连和错误恢复。

## 主要特性

1. **自动连接管理**：自动连接MQTT服务器，连接失败时每10秒重连一次
2. **定期心跳发送**：按配置的间隔定期发送心跳数据
3. **高鲁棒性**：除手动停止外，服务会持续运行并自动恢复
4. **低资源消耗**：使用异步I/O和高效的线程管理
5. **TLS支持**：预留TLS加密连接接口
6. **灵活配置**：支持自定义服务器地址、认证信息、心跳间隔等

## 心跳数据格式

心跳数据采用JSON格式：

```json
{
  "timestamp": 1704614400000,
  "device_id": "device_001"
}
```

- `timestamp`: 13位UTC时间戳（毫秒）
- `device_id`: 设备唯一标识符

## 使用方法

### 1. 基本使用

```cpp
#include "heartbeat.h"

// 配置参数
HeartbeatSender::Config config;
config.server_host = "*************";
config.server_port = "1883";
config.username = "mqtt_user";
config.password = "mqtt_pass";
config.device_id = "device_001";
config.topic = "heartbeat/device_001";
config.heartbeat_interval_sec = 10;

// 创建并启动心跳发送器
HeartbeatSender sender(config);
if (sender.start()) {
    // 心跳服务已启动
    // ... 其他业务逻辑 ...
    
    // 停止服务
    sender.stop();
}
```

### 2. 配置参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| server_host | string | 是 | MQTT服务器地址 |
| server_port | string | 是 | MQTT服务器端口 |
| username | string | 否 | MQTT用户名 |
| password | string | 否 | MQTT密码 |
| device_id | string | 是 | 设备唯一标识 |
| topic | string | 是 | 心跳发布主题 |
| heartbeat_interval_sec | int | 是 | 心跳间隔（秒） |
| enable_tls | bool | 否 | 是否启用TLS（预留） |

### 3. 错误处理

类会自动处理以下错误情况：
- 网络连接中断：自动重连
- MQTT服务器不可用：定期重试连接
- 消息发送失败：记录错误日志并继续尝试

## 依赖项

- **mqtt_cpp**: MQTT客户端库
- **jsoncpp**: JSON处理库
- **spdlog**: 日志库（通过logger.hpp）
- **boost::asio**: 异步I/O库
- **pthread**: 线程库
- **OpenSSL**: TLS支持（ssl, crypto）

## 编译

确保CMakeLists.txt包含必要的链接库：

```cmake
target_link_libraries(heartbeat ssl crypto jsoncpp pthread)
```

## 日志输出

类使用项目统一的日志系统，会输出以下类型的日志：
- **INFO**: 连接状态、心跳发送成功等信息
- **ERROR**: 连接失败、发送失败等错误信息

## 性能特点

- **内存占用**: 约几KB（主要是线程栈空间）
- **CPU占用**: 极低，大部分时间处于休眠状态
- **网络流量**: 每次心跳约50-100字节
- **线程数**: 2个（工作线程 + IOC线程）

## 注意事项

1. **线程安全**: 类的所有公共方法都是线程安全的
2. **资源清理**: 析构函数会自动清理所有资源
3. **异常处理**: 所有异常都会被捕获并记录到日志
4. **配置验证**: 构造函数会验证配置参数的有效性

## 故障排除

### 连接失败
- 检查MQTT服务器地址和端口
- 验证网络连接
- 确认用户名密码正确

### 心跳发送失败
- 检查主题权限
- 验证网络稳定性
- 查看服务器日志

### 高CPU占用
- 检查心跳间隔设置是否过小
- 确认没有频繁的连接断开重连
