/**
 * @file example_usage.cpp
 * @brief HeartbeatSender使用示例
 * 
 * 本文件展示了如何使用HeartbeatSender类进行MQTT心跳发送
 */

#include "heartbeat.h"
#include <iostream>
#include <thread>
#include <chrono>

int main() {
    try {
        // 配置心跳参数
        HeartbeatSender::Config config;
        config.server_host = "localhost";          // MQTT服务器地址
        config.server_port = "1883";               // MQTT服务器端口
        config.username = "test_user";             // MQTT用户名（可选）
        config.password = "test_password";         // MQTT密码（可选）
        config.device_id = "device_001";           // 设备ID
        config.topic = "heartbeat/device_001";     // 心跳主题
        config.heartbeat_interval_sec = 10;        // 心跳间隔10秒
        config.enable_tls = false;                 // 不启用TLS
        
        // 创建心跳发送器
        HeartbeatSender heartbeat_sender(config);
        
        // 启动心跳服务
        if (heartbeat_sender.start()) {
            std::cout << "心跳服务启动成功" << std::endl;
            
            // 运行60秒
            std::this_thread::sleep_for(std::chrono::seconds(60));
            
            // 停止心跳服务
            heartbeat_sender.stop();
            std::cout << "心跳服务已停止" << std::endl;
        } else {
            std::cerr << "心跳服务启动失败" << std::endl;
            return -1;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "异常: " << e.what() << std::endl;
        return -1;
    }
    
    return 0;
}

/**
 * 编译命令示例：
 * g++ -std=c++14 -I../../include -I../../include/common/logger -I../../include/mqtt_cpp -I../../include/json \
 *     example_usage.cpp heartbeat.cpp ../../src/common/commonFunction.cpp \
 *     -ljsoncpp -lssl -lcrypto -lpthread -o heartbeat_example
 * 
 * 运行前需要：
 * 1. 启动MQTT服务器（如mosquitto）
 * 2. 确保网络连接正常
 * 3. 根据实际情况修改配置参数
 */
