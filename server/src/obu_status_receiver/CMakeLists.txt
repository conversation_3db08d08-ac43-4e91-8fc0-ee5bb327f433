# OBU Status Receiver Module CMakeLists.txt
cmake_minimum_required(VERSION 3.0.2)

# 设置源文件
aux_source_directory(. DIR_SRC)

# 创建共享库
add_library(obu_status_receiver SHARED ${DIR_SRC})

# 设置包含目录
target_include_directories(obu_status_receiver PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/src
)

# 链接依赖库
target_link_libraries(obu_status_receiver
    utils
    jsoncpp
    pthread
)
