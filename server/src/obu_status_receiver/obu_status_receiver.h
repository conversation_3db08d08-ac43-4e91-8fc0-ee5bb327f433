#ifndef _OBU_STATUS_RECEIVER_H_
#define _OBU_STATUS_RECEIVER_H_

#include "../common/moduleBase.h"
#include "../../include/struct.h"
#include "../../include/message.h"
#include <string>
#include <thread>
#include <atomic>
#include <mutex>
#include <memory>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>

/**
 * @brief OBU状态接收器类
 * 继承自CModuleBase，用于接收UDP数据并解析OBU状态信息
 */
class CObuStatusReceiver : public CModuleBase
{
public:
    /**
     * @brief 构造函数
     * @param pMediator 中介者指针
     * @param port UDP监听端口
     * @param bindIp 绑定IP地址
     * @param bufferSize 接收缓冲区大小
     */
    CObuStatusReceiver(CMediator *pMediator, int port, const std::string bindIp, int bufferSize = 8192);
    
    /**
     * @brief 析构函数
     */
    virtual ~CObuStatusReceiver();

    /**
     * @brief 初始化模块
     */
    virtual void Init() override;
    
    /**
     * @brief 启动模块
     */
    virtual void Start() override;
    
    /**
     * @brief 停止模块
     */
    virtual void Stop() override;
    
    /**
     * @brief 暂停模块
     */
    virtual void Pause() override;

    /**
     * @brief 处理接收到的消息
     * @param msgLevel 消息级别
     * @param deviceId 设备ID
     * @param msgType 消息类型
     * @param spData 数据指针
     */
    virtual void HandleMsg(u32 msgLevel, u32 deviceId, u32 msgType, std::shared_ptr<void> spData) override;

private:
    
    /**
     * @brief 创建UDP socket
     * @return 成功返回true，失败返回false
     */
    bool CreateUdpSocket();
    
    /**
     * @brief UDP接收线程函数
     */
    void UdpReceiveThread();
    
    /**
     * @brief 解析接收到的数据
     * @param data 接收到的数据
     * @param length 数据长度
     * @param fromAddr 发送方地址
     */
    void ParseReceivedData(const char* data, size_t length, const sockaddr_in& fromAddr);
    
    /**
     * @brief 处理OBU状态数据
     * @param statusData 状态数据
     */
    void ProcessObuStatus(const std::string& statusData);
    
    /**
     * @brief 关闭socket
     */
    void CloseSocket();

private:
    // 配置相关
    int m_nPort;                             ///< 监听端口
    int m_nBufferSize;                       ///< 接收缓冲区大小
    std::string m_strBindIp;                 ///< 绑定IP地址
    
    // 网络相关
    int m_nSocketFd;                         ///< UDP socket文件描述符
    sockaddr_in m_serverAddr;                ///< 服务器地址结构
    
    // 线程相关
    std::thread m_receiveThread;             ///< 接收线程
    std::atomic<bool> m_bThreadRunning;      ///< 线程运行标志
    std::mutex m_dataMutex;                  ///< 数据处理互斥锁
    
    // 统计相关
    std::atomic<uint64_t> m_ullReceivedCount;    ///< 接收数据包计数
    std::atomic<uint64_t> m_ullProcessedCount;   ///< 处理数据包计数
    std::atomic<uint64_t> m_ullErrorCount;       ///< 错误计数
};

#endif // _OBU_STATUS_RECEIVER_H_
