# OBU状态接收器模块

## 概述

OBU状态接收器模块（CObuStatusReceiver）是一个基于UDP协议的数据接收模块，用于接收和解析OBU（On-Board Unit）设备发送的状态信息。该模块继承自CModuleBase基类，集成到系统的模块化架构中。

## 功能特性

- **UDP数据接收**：监听指定端口，接收UDP数据包
- **数据解析**：支持JSON格式和自定义格式的数据解析
- **线程安全**：使用多线程处理，确保数据接收的实时性
- **鲁棒性设计**：具备异常处理和错误恢复机制，确保线程不会意外退出
- **配置化管理**：通过配置文件管理端口、缓冲区大小等参数
- **统计功能**：提供接收、处理、错误计数等统计信息

## 配置说明

在 `conf/config.ini` 文件中添加以下配置段：

```ini
[obu_status_receiver]
# 是否启用OBU状态接收器 (0=禁用, 1=启用)
enable = 1
# UDP监听端口
port = 8080
# 接收缓冲区大小（字节）
buffer_size = 8192
# 绑定IP地址 (0.0.0.0表示绑定所有网卡)
bind_ip = 0.0.0.0
```

## 数据格式支持

### JSON格式示例
```json
{
    "obu_id": "OBU001",
    "status": "active",
    "latitude": 39.9042,
    "longitude": 116.4074,
    "speed": 60,
    "heading": 90,
    "timestamp": 1640995200000
}
```

### 自定义格式示例
```
OBU001:active:39.9042:116.4074:60:90
```
格式说明：`OBU_ID:STATUS:LAT:LON:SPEED:HEADING`

## 使用方法

1. **配置文件设置**：在config.ini中配置相关参数
2. **编译项目**：模块会自动集成到主程序中
3. **启动服务**：主程序启动时会自动初始化和启动该模块
4. **发送测试数据**：可以使用UDP客户端向配置的端口发送测试数据

## 测试示例

使用netcat工具发送测试数据：

```bash
# 发送JSON格式数据
echo '{"obu_id":"OBU001","status":"active","latitude":39.9042,"longitude":116.4074,"speed":60,"heading":90}' | nc -u localhost 8080

# 发送自定义格式数据
echo 'OBU001:active:39.9042:116.4074:60:90' | nc -u localhost 8080
```

## 日志输出

模块运行时会输出详细的日志信息，包括：
- 模块启动/停止状态
- 数据接收情况
- 数据解析结果
- 错误信息和统计数据

## 扩展说明

该模块提供了数据处理的基础框架，您可以根据实际需求：

1. **修改数据格式**：在`ParseReceivedData`和`ProcessObuStatus`函数中修改解析逻辑
2. **添加数据验证**：在`ProcessObuStatus`函数中添加数据有效性检查
3. **集成其他模块**：通过中介者模式与其他模块进行数据交互
4. **数据存储**：添加数据库存储或文件存储功能

## 注意事项

- 确保配置的端口未被其他程序占用
- 建议根据实际网络环境调整缓冲区大小
- 在生产环境中建议启用数据验证和错误处理机制
- 模块支持优雅关闭，停止时会等待接收线程正常退出
