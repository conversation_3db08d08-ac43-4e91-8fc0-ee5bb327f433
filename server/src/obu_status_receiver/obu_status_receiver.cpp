#include "obu_status_receiver.h"
#include "../../include/common/logger/logger.hpp"
#include "../common/commonFunction.h"
#include <unistd.h>
#include <fcntl.h>
#include <errno.h>
#include <cstring>
#include <chrono>
#include <sstream>
#include <json/json.h>

CObuStatusReceiver::CObuStatusReceiver(CMediator *pMediator, int port, const std::string bindIp, int bufferSize)
    : CModuleBase(pMediator)
    , m_nPort(port)
    , m_nBufferSize(bufferSize)
    , m_strBindIp(bindIp)
    , m_nSocketFd(-1)
    , m_bThreadRunning(false)
    , m_ullReceivedCount(0)
    , m_ullProcessedCount(0)
    , m_ullErrorCount(0)
{
    memset(&m_serverAddr, 0, sizeof(m_serverAddr));
}

CObuStatusReceiver::~CObuStatusReceiver()
{
    Stop();
}

void CObuStatusReceiver::Init()
{

    // 调用基类初始化
    CModuleBase::Init();

    INFO("OBU Status Receiver initialized successfully");
    INFO("Port: %d, Buffer Size: %d, Bind IP: %s",
         m_nPort, m_nBufferSize, m_strBindIp.c_str());
}

void CObuStatusReceiver::Start()
{

    if (m_bIsRunning)
    {
        INFO("OBU Status Receiver is already running");
        return;
    }
    
    // 调用基类启动
    CModuleBase::Start();
    
    // 创建UDP socket
    if (!CreateUdpSocket())
    {
        ERROR("Failed to create UDP socket");
        return;
    }
    
    // 启动接收线程
    m_bThreadRunning = true;
    m_receiveThread = std::thread(&CObuStatusReceiver::UdpReceiveThread, this);
    
    INFO("OBU Status Receiver started successfully on port %d", m_nPort);
}

void CObuStatusReceiver::Stop()
{
    INFO("CObuStatusReceiver::Stop() called");
    
    if (!m_bIsRunning)
    {
        INFO("OBU Status Receiver is not running");
        return;
    }
    
    // 停止接收线程
    m_bThreadRunning = false;
    
    // 关闭socket以中断阻塞的接收操作
    CloseSocket();
    
    // 等待线程结束
    if (m_receiveThread.joinable())
    {
        m_receiveThread.join();
        INFO("UDP receive thread stopped");
    }
    
    // 调用基类停止
    CModuleBase::Stop();
    
    INFO("OBU Status Receiver stopped successfully");
    INFO("Statistics - Received: %llu, Processed: %llu, Errors: %llu", 
         m_ullReceivedCount.load(), m_ullProcessedCount.load(), m_ullErrorCount.load());
}

void CObuStatusReceiver::Pause()
{
    INFO("CObuStatusReceiver::Pause() called");
    CModuleBase::Pause();
}

void CObuStatusReceiver::HandleMsg(u32 msgLevel, u32 deviceId, u32 msgType, std::shared_ptr<void> spData)
{
    // 处理系统消息
    CModuleBase::HandleMsg(msgLevel, deviceId, msgType, spData);
    
    // 可以在这里添加自定义消息处理逻辑
    switch (msgType)
    {
        // 可以添加自定义消息类型处理
        default:
            break;
    }
}



bool CObuStatusReceiver::CreateUdpSocket()
{
    INFO("Creating UDP socket...");
    
    // 创建UDP socket
    m_nSocketFd = socket(AF_INET, SOCK_DGRAM, 0);
    if (m_nSocketFd < 0)
    {
        ERROR("Failed to create UDP socket: %s", strerror(errno));
        return false;
    }
    
    // 设置socket选项 - 地址重用
    int optval = 1;
    if (setsockopt(m_nSocketFd, SOL_SOCKET, SO_REUSEADDR, &optval, sizeof(optval)) < 0)
    {
        ERROR("Failed to set SO_REUSEADDR: %s", strerror(errno));
        CloseSocket();
        return false;
    }
    
    // 设置接收缓冲区大小
    if (setsockopt(m_nSocketFd, SOL_SOCKET, SO_RCVBUF, &m_nBufferSize, sizeof(m_nBufferSize)) < 0)
    {
        ERROR("Failed to set receive buffer size: %s", strerror(errno));
        CloseSocket();
        return false;
    }
    
    // 设置接收超时
    struct timeval tv;
    tv.tv_sec = 5;  // 5秒超时
    tv.tv_usec = 0;
    if (setsockopt(m_nSocketFd, SOL_SOCKET, SO_RCVTIMEO, &tv, sizeof(tv)) < 0)
    {
        ERROR("Failed to set receive timeout: %s", strerror(errno));
        CloseSocket();
        return false;
    }
    
    // 设置服务器地址
    memset(&m_serverAddr, 0, sizeof(m_serverAddr));
    m_serverAddr.sin_family = AF_INET;
    m_serverAddr.sin_port = htons(m_nPort);
    
    if (m_strBindIp == "0.0.0.0" || m_strBindIp.empty())
    {
        m_serverAddr.sin_addr.s_addr = INADDR_ANY;
    }
    else
    {
        if (inet_pton(AF_INET, m_strBindIp.c_str(), &m_serverAddr.sin_addr) <= 0)
        {
            ERROR("Invalid bind IP address: %s", m_strBindIp.c_str());
            CloseSocket();
            return false;
        }
    }
    
    // 绑定socket
    if (bind(m_nSocketFd, (struct sockaddr*)&m_serverAddr, sizeof(m_serverAddr)) < 0)
    {
        ERROR("Failed to bind socket to port %d: %s", m_nPort, strerror(errno));
        CloseSocket();
        return false;
    }
    
    INFO("UDP socket created and bound to %s:%d successfully", m_strBindIp.c_str(), m_nPort);
    return true;
}

void CObuStatusReceiver::UdpReceiveThread()
{
    INFO("UDP receive thread started");

    std::unique_ptr<char[]> buffer(new char[m_nBufferSize]);
    sockaddr_in clientAddr;
    socklen_t clientAddrLen = sizeof(clientAddr);

    while (m_bThreadRunning)
    {
        try
        {
            memset(&clientAddr, 0, sizeof(clientAddr));
            clientAddrLen = sizeof(clientAddr);

            // 接收数据
            ssize_t receivedBytes = recvfrom(m_nSocketFd, buffer.get(), m_nBufferSize - 1, 0,
                                           (struct sockaddr*)&clientAddr, &clientAddrLen);

            if (receivedBytes < 0)
            {
                if (errno == EAGAIN || errno == EWOULDBLOCK)
                {
                    // 超时，继续循环
                    continue;
                }
                else if (errno == EINTR)
                {
                    // 被信号中断，继续循环
                    continue;
                }
                else
                {
                    if (m_bThreadRunning) // 只有在线程应该运行时才记录错误
                    {
                        ERROR("recvfrom failed: %s", strerror(errno));
                        m_ullErrorCount++;
                    }
                    break;
                }
            }
            else if (receivedBytes == 0)
            {
                // 对于UDP，这种情况很少见，但仍需处理
                continue;
            }
            else
            {
                // 成功接收数据
                m_ullReceivedCount++;
                buffer[receivedBytes] = '\0'; // 确保字符串结束

                // 获取客户端IP地址用于日志
                char clientIp[INET_ADDRSTRLEN];
                inet_ntop(AF_INET, &clientAddr.sin_addr, clientIp, INET_ADDRSTRLEN);

                INFO("Received %zd bytes from %s:%d", receivedBytes, clientIp, ntohs(clientAddr.sin_port));

                // 解析接收到的数据
                ParseReceivedData(buffer.get(), receivedBytes, clientAddr);
            }
        }
        catch (const std::exception& e)
        {
            ERROR("Exception in UDP receive thread: %s", e.what());
            m_ullErrorCount++;

            // 短暂休眠后继续，避免快速循环
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        catch (...)
        {
            ERROR("Unknown exception in UDP receive thread");
            m_ullErrorCount++;

            // 短暂休眠后继续，避免快速循环
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }

    INFO("UDP receive thread exited");
}

void CObuStatusReceiver::ParseReceivedData(const char* data, size_t length, const sockaddr_in& fromAddr)
{
    if (!data || length == 0)
    {
        ERROR("Invalid data received");
        m_ullErrorCount++;
        return;
    }

    try
    {
        std::lock_guard<std::mutex> lock(m_dataMutex);

        // 将接收到的数据转换为字符串
        std::string receivedData(data, length);

        // 获取发送方信息
        char senderIp[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &fromAddr.sin_addr, senderIp, INET_ADDRSTRLEN);
        int senderPort = ntohs(fromAddr.sin_port);

        INFO("Parsing data from %s:%d, length: %zu", senderIp, senderPort, length);

        // 尝试解析为JSON格式（这是一个简单的模板，您可以根据实际数据格式修改）
        Json::Reader reader;
        Json::Value root;

        if (reader.parse(receivedData, root))
        {
            INFO("Successfully parsed JSON data");

            // 检查是否包含OBU状态相关字段（示例字段，您可以根据实际需求修改）
            if (root.isMember("obu_id") && root.isMember("status"))
            {
                ProcessObuStatus(receivedData);
            }
            else
            {
                INFO("Received JSON data but no OBU status fields found");
                // 可以在这里添加其他类型数据的处理逻辑
            }
        }
        else
        {
            INFO("Received non-JSON data, treating as raw OBU status");
            // 如果不是JSON格式，按原始数据处理
            ProcessObuStatus(receivedData);
        }

        m_ullProcessedCount++;
    }
    catch (const std::exception& e)
    {
        ERROR("Exception in ParseReceivedData: %s", e.what());
        m_ullErrorCount++;
    }
    catch (...)
    {
        ERROR("Unknown exception in ParseReceivedData");
        m_ullErrorCount++;
    }
}

void CObuStatusReceiver::ProcessObuStatus(const std::string& statusData)
{
    try
    {
        INFO("Processing OBU status data, length: %zu", statusData.length());

        // 获取当前时间戳
        auto now = std::chrono::system_clock::now();
        auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()).count();

        // 这里是OBU状态数据处理的模板，您可以根据实际数据格式进行修改
        Json::Reader reader;
        Json::Value root;

        if (reader.parse(statusData, root))
        {
            // JSON格式数据处理示例
            std::string obuId = root.get("obu_id", "unknown").asString();
            std::string status = root.get("status", "unknown").asString();
            double latitude = root.get("latitude", 0.0).asDouble();
            double longitude = root.get("longitude", 0.0).asDouble();
            int speed = root.get("speed", 0).asInt();
            int heading = root.get("heading", 0).asInt();

            INFO("OBU Status - ID: %s, Status: %s, Lat: %.6f, Lon: %.6f, Speed: %d, Heading: %d",
                 obuId.c_str(), status.c_str(), latitude, longitude, speed, heading);

            // 创建处理后的数据结构（示例）
            Json::Value processedData;
            processedData["obu_id"] = obuId;
            processedData["status"] = status;
            processedData["position"]["latitude"] = latitude;
            processedData["position"]["longitude"] = longitude;
            processedData["motion"]["speed"] = speed;
            processedData["motion"]["heading"] = heading;
            processedData["timestamp"] = static_cast<Json::Int64>(timestamp);
            processedData["processed_by"] = "obu_status_receiver";

            // 这里可以通过中介者模式通知其他模块
            // 例如：通知数据库模块存储数据，通知通信模块转发数据等
            // Notify(MSG_LEVEL_ALL_DEVICE, NO_DEVICE, OBU_STATUS_DATA, processedDataPtr);

            INFO("OBU status processed successfully for ID: %s", obuId.c_str());
        }
        else
        {
            // 非JSON格式数据处理示例
            INFO("Processing raw OBU status data: %s", statusData.substr(0, 100).c_str()); // 只显示前100个字符

            // 这里可以添加自定义的数据解析逻辑
            // 例如：按固定格式解析、按分隔符解析等

            // 示例：假设数据格式为 "OBU_ID:STATUS:LAT:LON:SPEED:HEADING"
            std::vector<std::string> parts;
            std::stringstream ss(statusData);
            std::string item;

            while (std::getline(ss, item, ':'))
            {
                parts.push_back(item);
            }

            if (parts.size() >= 6)
            {
                std::string obuId = parts[0];
                std::string status = parts[1];
                double latitude = std::stod(parts[2]);
                double longitude = std::stod(parts[3]);
                int speed = std::stoi(parts[4]);
                int heading = std::stoi(parts[5]);

                INFO("Parsed OBU Status - ID: %s, Status: %s, Lat: %.6f, Lon: %.6f, Speed: %d, Heading: %d",
                     obuId.c_str(), status.c_str(), latitude, longitude, speed, heading);
            }
            else
            {
                INFO("Raw data format not recognized, storing as-is");
            }
        }

        // 可以在这里添加数据验证逻辑
        // 例如：检查数据完整性、范围验证等

        // 可以在这里添加数据存储逻辑
        // 例如：存储到数据库、写入文件等

    }
    catch (const std::exception& e)
    {
        ERROR("Exception in ProcessObuStatus: %s", e.what());
        m_ullErrorCount++;
    }
    catch (...)
    {
        ERROR("Unknown exception in ProcessObuStatus");
        m_ullErrorCount++;
    }
}

void CObuStatusReceiver::CloseSocket()
{
    if (m_nSocketFd >= 0)
    {
        INFO("Closing UDP socket");
        close(m_nSocketFd);
        m_nSocketFd = -1;
    }
}
