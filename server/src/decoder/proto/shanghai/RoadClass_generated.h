// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_ROADCLASS_MECDATA_H_
#define FLATBUFFERS_GENERATED_ROADCLASS_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_RoadClass : uint8_t {
  DE_RoadClass_UNCLASSIFIED = 0,
  DE_RoadClass_EXPRESSWAY = 1,
  DE_RoadClass_ARTERIAL = 2,
  DE_RoadClass_FEEDER = 3,
  DE_RoadClass_BRANCH = 4,
  DE_RoadClass_PRIVATE = 5,
  DE_RoadClass_MIN = DE_RoadClass_UNCLASSIFIED,
  DE_RoadClass_MAX = DE_RoadClass_PRIVATE
};

inline const DE_RoadClass (&EnumValuesDE_RoadClass())[6] {
  static const DE_RoadClass values[] = {
    DE_RoadClass_UNCLASSIFIED,
    DE_RoadClass_EXPRESSWAY,
    DE_RoadClass_ARTERIAL,
    DE_RoadClass_FEEDER,
    DE_RoadClass_BRANCH,
    DE_RoadClass_PRIVATE
  };
  return values;
}

inline const char * const *EnumNamesDE_RoadClass() {
  static const char * const names[7] = {
    "UNCLASSIFIED",
    "EXPRESSWAY",
    "ARTERIAL",
    "FEEDER",
    "BRANCH",
    "PRIVATE",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_RoadClass(DE_RoadClass e) {
  if (::flatbuffers::IsOutRange(e, DE_RoadClass_UNCLASSIFIED, DE_RoadClass_PRIVATE)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_RoadClass()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_ROADCLASS_MECDATA_H_
