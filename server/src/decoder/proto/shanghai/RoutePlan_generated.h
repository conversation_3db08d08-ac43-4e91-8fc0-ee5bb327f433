// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_ROUTEPLAN_MECDATA_H_
#define FLATBUFFERS_GENERATED_ROUTEPLAN_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "NodeReferenceID_generated.h"

namespace MECData {

struct MSG_RoutePlan;
struct MSG_RoutePlanBuilder;

struct MSG_RoutePlan FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_RoutePlanBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PTC_ID = 4,
    VT_TRIP_REQUEST_ID = 6,
    VT_TRAVEL_DISTANCE = 8,
    VT_TRAVEL_TIME = 10,
    VT_LINKS = 12,
    VT_NODES = 14,
    VT_MSG_ID = 16
  };
  uint16_t ptc_id() const {
    return GetField<uint16_t>(VT_PTC_ID, 0);
  }
  int32_t trip_request_id() const {
    return GetField<int32_t>(VT_TRIP_REQUEST_ID, 0);
  }
  int16_t travel_distance() const {
    return GetField<int16_t>(VT_TRAVEL_DISTANCE, 0);
  }
  int16_t travel_time() const {
    return GetField<int16_t>(VT_TRAVEL_TIME, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *links() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *>(VT_LINKS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_NodeReferenceID>> *nodes() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_NodeReferenceID>> *>(VT_NODES);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_PTC_ID, 2) &&
           VerifyField<int32_t>(verifier, VT_TRIP_REQUEST_ID, 4) &&
           VerifyField<int16_t>(verifier, VT_TRAVEL_DISTANCE, 2) &&
           VerifyField<int16_t>(verifier, VT_TRAVEL_TIME, 2) &&
           VerifyOffset(verifier, VT_LINKS) &&
           verifier.VerifyVector(links()) &&
           verifier.VerifyVectorOfStrings(links()) &&
           VerifyOffset(verifier, VT_NODES) &&
           verifier.VerifyVector(nodes()) &&
           verifier.VerifyVectorOfTables(nodes()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct MSG_RoutePlanBuilder {
  typedef MSG_RoutePlan Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_ptc_id(uint16_t ptc_id) {
    fbb_.AddElement<uint16_t>(MSG_RoutePlan::VT_PTC_ID, ptc_id, 0);
  }
  void add_trip_request_id(int32_t trip_request_id) {
    fbb_.AddElement<int32_t>(MSG_RoutePlan::VT_TRIP_REQUEST_ID, trip_request_id, 0);
  }
  void add_travel_distance(int16_t travel_distance) {
    fbb_.AddElement<int16_t>(MSG_RoutePlan::VT_TRAVEL_DISTANCE, travel_distance, 0);
  }
  void add_travel_time(int16_t travel_time) {
    fbb_.AddElement<int16_t>(MSG_RoutePlan::VT_TRAVEL_TIME, travel_time, 0);
  }
  void add_links(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> links) {
    fbb_.AddOffset(MSG_RoutePlan::VT_LINKS, links);
  }
  void add_nodes(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_NodeReferenceID>>> nodes) {
    fbb_.AddOffset(MSG_RoutePlan::VT_NODES, nodes);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_RoutePlan::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_RoutePlanBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_RoutePlan> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_RoutePlan>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_RoutePlan> CreateMSG_RoutePlan(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t ptc_id = 0,
    int32_t trip_request_id = 0,
    int16_t travel_distance = 0,
    int16_t travel_time = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> links = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_NodeReferenceID>>> nodes = 0,
    int64_t msg_id = 0) {
  MSG_RoutePlanBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_nodes(nodes);
  builder_.add_links(links);
  builder_.add_trip_request_id(trip_request_id);
  builder_.add_travel_time(travel_time);
  builder_.add_travel_distance(travel_distance);
  builder_.add_ptc_id(ptc_id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_RoutePlan> CreateMSG_RoutePlanDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t ptc_id = 0,
    int32_t trip_request_id = 0,
    int16_t travel_distance = 0,
    int16_t travel_time = 0,
    const std::vector<::flatbuffers::Offset<::flatbuffers::String>> *links = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_NodeReferenceID>> *nodes = nullptr,
    int64_t msg_id = 0) {
  auto links__ = links ? _fbb.CreateVector<::flatbuffers::Offset<::flatbuffers::String>>(*links) : 0;
  auto nodes__ = nodes ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_NodeReferenceID>>(*nodes) : 0;
  return MECData::CreateMSG_RoutePlan(
      _fbb,
      ptc_id,
      trip_request_id,
      travel_distance,
      travel_time,
      links__,
      nodes__,
      msg_id);
}

inline const MECData::MSG_RoutePlan *GetMSG_RoutePlan(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_RoutePlan>(buf);
}

inline const MECData::MSG_RoutePlan *GetSizePrefixedMSG_RoutePlan(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_RoutePlan>(buf);
}

inline bool VerifyMSG_RoutePlanBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_RoutePlan>(nullptr);
}

inline bool VerifySizePrefixedMSG_RoutePlanBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_RoutePlan>(nullptr);
}

inline void FinishMSG_RoutePlanBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_RoutePlan> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_RoutePlanBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_RoutePlan> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_ROUTEPLAN_MECDATA_H_
