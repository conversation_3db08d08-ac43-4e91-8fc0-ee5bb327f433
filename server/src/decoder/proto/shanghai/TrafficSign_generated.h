// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_TRAFFICSIGN_MECDATA_H_
#define FLATBUFFERS_GENERATED_TRAFFICSIGN_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "DebugTimeRecords_generated.h"
#include "EventOperationType_generated.h"
#include "Position3D_generated.h"
#include "ReferenceLink_generated.h"
#include "ReferencePath_generated.h"

namespace MECData {

struct MSG_TrafficSign;
struct MSG_TrafficSignBuilder;

struct MSG_TrafficSign FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_TrafficSignBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SIGNID = 4,
    VT_SIGNTYPE = 6,
    VT_SIGNPOS = 8,
    VT_DESCRIPTION = 10,
    VT_TIMESTART = 12,
    VT_TIMEEND = 14,
    VT_PRIORITY = 16,
    VT_REFERENCEPATHS = 18,
    VT_REFERENCELINKS = 20,
    VT_PATHRADIUS = 22,
    VT_REFERENCEUPSTREAMNODESID = 24,
    VT_REFERENCEDOWNSTREAMNODESID = 26,
    VT_REFERENCELANES = 28,
    VT_REGION = 30,
    VT_TIME_RECORDS = 32,
    VT_MSG_ID = 34,
    VT_SESSION_ID = 36,
    VT_OPERATION_TYPE = 38
  };
  uint32_t signId() const {
    return GetField<uint32_t>(VT_SIGNID, 4294967295);
  }
  uint16_t signType() const {
    return GetField<uint16_t>(VT_SIGNTYPE, 65535);
  }
  const MECData::DF_Position3D *signPos() const {
    return GetPointer<const MECData::DF_Position3D *>(VT_SIGNPOS);
  }
  const ::flatbuffers::String *description() const {
    return GetPointer<const ::flatbuffers::String *>(VT_DESCRIPTION);
  }
  int32_t timeStart() const {
    return GetField<int32_t>(VT_TIMESTART, 0);
  }
  int32_t timeEnd() const {
    return GetField<int32_t>(VT_TIMEEND, 0);
  }
  int32_t priority() const {
    return GetField<int32_t>(VT_PRIORITY, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferencePath>> *referencePaths() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferencePath>> *>(VT_REFERENCEPATHS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferenceLink>> *referenceLinks() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferenceLink>> *>(VT_REFERENCELINKS);
  }
  uint16_t pathRadius() const {
    return GetField<uint16_t>(VT_PATHRADIUS, 0);
  }
  const ::flatbuffers::Vector<uint16_t> *referenceUpstreamNodesId() const {
    return GetPointer<const ::flatbuffers::Vector<uint16_t> *>(VT_REFERENCEUPSTREAMNODESID);
  }
  const ::flatbuffers::Vector<uint16_t> *referenceDownstreamNodesId() const {
    return GetPointer<const ::flatbuffers::Vector<uint16_t> *>(VT_REFERENCEDOWNSTREAMNODESID);
  }
  const ::flatbuffers::Vector<int16_t> *referenceLanes() const {
    return GetPointer<const ::flatbuffers::Vector<int16_t> *>(VT_REFERENCELANES);
  }
  uint16_t region() const {
    return GetField<uint16_t>(VT_REGION, 0);
  }
  const MECData::DF_DebugTimeRecords *time_records() const {
    return GetPointer<const MECData::DF_DebugTimeRecords *>(VT_TIME_RECORDS);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  int64_t session_id() const {
    return GetField<int64_t>(VT_SESSION_ID, 0);
  }
  MECData::DE_EventOperationType operation_type() const {
    return static_cast<MECData::DE_EventOperationType>(GetField<uint8_t>(VT_OPERATION_TYPE, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_SIGNID, 4) &&
           VerifyField<uint16_t>(verifier, VT_SIGNTYPE, 2) &&
           VerifyOffsetRequired(verifier, VT_SIGNPOS) &&
           verifier.VerifyTable(signPos()) &&
           VerifyOffset(verifier, VT_DESCRIPTION) &&
           verifier.VerifyString(description()) &&
           VerifyField<int32_t>(verifier, VT_TIMESTART, 4) &&
           VerifyField<int32_t>(verifier, VT_TIMEEND, 4) &&
           VerifyField<int32_t>(verifier, VT_PRIORITY, 4) &&
           VerifyOffset(verifier, VT_REFERENCEPATHS) &&
           verifier.VerifyVector(referencePaths()) &&
           verifier.VerifyVectorOfTables(referencePaths()) &&
           VerifyOffset(verifier, VT_REFERENCELINKS) &&
           verifier.VerifyVector(referenceLinks()) &&
           verifier.VerifyVectorOfTables(referenceLinks()) &&
           VerifyField<uint16_t>(verifier, VT_PATHRADIUS, 2) &&
           VerifyOffset(verifier, VT_REFERENCEUPSTREAMNODESID) &&
           verifier.VerifyVector(referenceUpstreamNodesId()) &&
           VerifyOffset(verifier, VT_REFERENCEDOWNSTREAMNODESID) &&
           verifier.VerifyVector(referenceDownstreamNodesId()) &&
           VerifyOffset(verifier, VT_REFERENCELANES) &&
           verifier.VerifyVector(referenceLanes()) &&
           VerifyField<uint16_t>(verifier, VT_REGION, 2) &&
           VerifyOffset(verifier, VT_TIME_RECORDS) &&
           verifier.VerifyTable(time_records()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           VerifyField<int64_t>(verifier, VT_SESSION_ID, 8) &&
           VerifyField<uint8_t>(verifier, VT_OPERATION_TYPE, 1) &&
           verifier.EndTable();
  }
};

struct MSG_TrafficSignBuilder {
  typedef MSG_TrafficSign Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_signId(uint32_t signId) {
    fbb_.AddElement<uint32_t>(MSG_TrafficSign::VT_SIGNID, signId, 4294967295);
  }
  void add_signType(uint16_t signType) {
    fbb_.AddElement<uint16_t>(MSG_TrafficSign::VT_SIGNTYPE, signType, 65535);
  }
  void add_signPos(::flatbuffers::Offset<MECData::DF_Position3D> signPos) {
    fbb_.AddOffset(MSG_TrafficSign::VT_SIGNPOS, signPos);
  }
  void add_description(::flatbuffers::Offset<::flatbuffers::String> description) {
    fbb_.AddOffset(MSG_TrafficSign::VT_DESCRIPTION, description);
  }
  void add_timeStart(int32_t timeStart) {
    fbb_.AddElement<int32_t>(MSG_TrafficSign::VT_TIMESTART, timeStart, 0);
  }
  void add_timeEnd(int32_t timeEnd) {
    fbb_.AddElement<int32_t>(MSG_TrafficSign::VT_TIMEEND, timeEnd, 0);
  }
  void add_priority(int32_t priority) {
    fbb_.AddElement<int32_t>(MSG_TrafficSign::VT_PRIORITY, priority, 0);
  }
  void add_referencePaths(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferencePath>>> referencePaths) {
    fbb_.AddOffset(MSG_TrafficSign::VT_REFERENCEPATHS, referencePaths);
  }
  void add_referenceLinks(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferenceLink>>> referenceLinks) {
    fbb_.AddOffset(MSG_TrafficSign::VT_REFERENCELINKS, referenceLinks);
  }
  void add_pathRadius(uint16_t pathRadius) {
    fbb_.AddElement<uint16_t>(MSG_TrafficSign::VT_PATHRADIUS, pathRadius, 0);
  }
  void add_referenceUpstreamNodesId(::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> referenceUpstreamNodesId) {
    fbb_.AddOffset(MSG_TrafficSign::VT_REFERENCEUPSTREAMNODESID, referenceUpstreamNodesId);
  }
  void add_referenceDownstreamNodesId(::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> referenceDownstreamNodesId) {
    fbb_.AddOffset(MSG_TrafficSign::VT_REFERENCEDOWNSTREAMNODESID, referenceDownstreamNodesId);
  }
  void add_referenceLanes(::flatbuffers::Offset<::flatbuffers::Vector<int16_t>> referenceLanes) {
    fbb_.AddOffset(MSG_TrafficSign::VT_REFERENCELANES, referenceLanes);
  }
  void add_region(uint16_t region) {
    fbb_.AddElement<uint16_t>(MSG_TrafficSign::VT_REGION, region, 0);
  }
  void add_time_records(::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records) {
    fbb_.AddOffset(MSG_TrafficSign::VT_TIME_RECORDS, time_records);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_TrafficSign::VT_MSG_ID, msg_id, 0);
  }
  void add_session_id(int64_t session_id) {
    fbb_.AddElement<int64_t>(MSG_TrafficSign::VT_SESSION_ID, session_id, 0);
  }
  void add_operation_type(MECData::DE_EventOperationType operation_type) {
    fbb_.AddElement<uint8_t>(MSG_TrafficSign::VT_OPERATION_TYPE, static_cast<uint8_t>(operation_type), 0);
  }
  explicit MSG_TrafficSignBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_TrafficSign> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_TrafficSign>(end);
    fbb_.Required(o, MSG_TrafficSign::VT_SIGNPOS);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_TrafficSign> CreateMSG_TrafficSign(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t signId = 4294967295,
    uint16_t signType = 65535,
    ::flatbuffers::Offset<MECData::DF_Position3D> signPos = 0,
    ::flatbuffers::Offset<::flatbuffers::String> description = 0,
    int32_t timeStart = 0,
    int32_t timeEnd = 0,
    int32_t priority = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferencePath>>> referencePaths = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferenceLink>>> referenceLinks = 0,
    uint16_t pathRadius = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> referenceUpstreamNodesId = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> referenceDownstreamNodesId = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<int16_t>> referenceLanes = 0,
    uint16_t region = 0,
    ::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records = 0,
    int64_t msg_id = 0,
    int64_t session_id = 0,
    MECData::DE_EventOperationType operation_type = MECData::DE_EventOperationType_AUTO_FORWARD) {
  MSG_TrafficSignBuilder builder_(_fbb);
  builder_.add_session_id(session_id);
  builder_.add_msg_id(msg_id);
  builder_.add_time_records(time_records);
  builder_.add_referenceLanes(referenceLanes);
  builder_.add_referenceDownstreamNodesId(referenceDownstreamNodesId);
  builder_.add_referenceUpstreamNodesId(referenceUpstreamNodesId);
  builder_.add_referenceLinks(referenceLinks);
  builder_.add_referencePaths(referencePaths);
  builder_.add_priority(priority);
  builder_.add_timeEnd(timeEnd);
  builder_.add_timeStart(timeStart);
  builder_.add_description(description);
  builder_.add_signPos(signPos);
  builder_.add_signId(signId);
  builder_.add_region(region);
  builder_.add_pathRadius(pathRadius);
  builder_.add_signType(signType);
  builder_.add_operation_type(operation_type);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_TrafficSign> CreateMSG_TrafficSignDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t signId = 4294967295,
    uint16_t signType = 65535,
    ::flatbuffers::Offset<MECData::DF_Position3D> signPos = 0,
    const char *description = nullptr,
    int32_t timeStart = 0,
    int32_t timeEnd = 0,
    int32_t priority = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_ReferencePath>> *referencePaths = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_ReferenceLink>> *referenceLinks = nullptr,
    uint16_t pathRadius = 0,
    const std::vector<uint16_t> *referenceUpstreamNodesId = nullptr,
    const std::vector<uint16_t> *referenceDownstreamNodesId = nullptr,
    const std::vector<int16_t> *referenceLanes = nullptr,
    uint16_t region = 0,
    ::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records = 0,
    int64_t msg_id = 0,
    int64_t session_id = 0,
    MECData::DE_EventOperationType operation_type = MECData::DE_EventOperationType_AUTO_FORWARD) {
  auto description__ = description ? _fbb.CreateString(description) : 0;
  auto referencePaths__ = referencePaths ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_ReferencePath>>(*referencePaths) : 0;
  auto referenceLinks__ = referenceLinks ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_ReferenceLink>>(*referenceLinks) : 0;
  auto referenceUpstreamNodesId__ = referenceUpstreamNodesId ? _fbb.CreateVector<uint16_t>(*referenceUpstreamNodesId) : 0;
  auto referenceDownstreamNodesId__ = referenceDownstreamNodesId ? _fbb.CreateVector<uint16_t>(*referenceDownstreamNodesId) : 0;
  auto referenceLanes__ = referenceLanes ? _fbb.CreateVector<int16_t>(*referenceLanes) : 0;
  return MECData::CreateMSG_TrafficSign(
      _fbb,
      signId,
      signType,
      signPos,
      description__,
      timeStart,
      timeEnd,
      priority,
      referencePaths__,
      referenceLinks__,
      pathRadius,
      referenceUpstreamNodesId__,
      referenceDownstreamNodesId__,
      referenceLanes__,
      region,
      time_records,
      msg_id,
      session_id,
      operation_type);
}

inline const MECData::MSG_TrafficSign *GetMSG_TrafficSign(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_TrafficSign>(buf);
}

inline const MECData::MSG_TrafficSign *GetSizePrefixedMSG_TrafficSign(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_TrafficSign>(buf);
}

inline bool VerifyMSG_TrafficSignBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_TrafficSign>(nullptr);
}

inline bool VerifySizePrefixedMSG_TrafficSignBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_TrafficSign>(nullptr);
}

inline void FinishMSG_TrafficSignBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_TrafficSign> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_TrafficSignBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_TrafficSign> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_TRAFFICSIGN_MECDATA_H_
