// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_SIGNALSCHEME_MECDATA_H_
#define FLATBUFFERS_GENERATED_SIGNALSCHEME_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "DebugTimeRecords_generated.h"
#include "NodeReferenceID_generated.h"
#include "Phasic_generated.h"
#include "SignalControlMode_generated.h"

namespace MECData {

struct MSG_SignalScheme;
struct MSG_SignalSchemeBuilder;

enum DE_SignalSchemeSource : uint8_t {
  DE_SignalSchemeSource_SIGNAL_CONTROLLER = 0,
  DE_SignalSchemeSource_ROADSIDE_ALGO = 1,
  DE_SignalSchemeSource_ROADSIDE_MANUAL = 2,
  DE_SignalSchemeSource_CLOUD_ALGO = 3,
  DE_SignalSchemeSource_CLOUD_MANUAL = 4,
  DE_SignalSchemeSource_MIN = DE_SignalSchemeSource_SIGNAL_CONTROLLER,
  DE_SignalSchemeSource_MAX = DE_SignalSchemeSource_CLOUD_MANUAL
};

inline const DE_SignalSchemeSource (&EnumValuesDE_SignalSchemeSource())[5] {
  static const DE_SignalSchemeSource values[] = {
    DE_SignalSchemeSource_SIGNAL_CONTROLLER,
    DE_SignalSchemeSource_ROADSIDE_ALGO,
    DE_SignalSchemeSource_ROADSIDE_MANUAL,
    DE_SignalSchemeSource_CLOUD_ALGO,
    DE_SignalSchemeSource_CLOUD_MANUAL
  };
  return values;
}

inline const char * const *EnumNamesDE_SignalSchemeSource() {
  static const char * const names[6] = {
    "SIGNAL_CONTROLLER",
    "ROADSIDE_ALGO",
    "ROADSIDE_MANUAL",
    "CLOUD_ALGO",
    "CLOUD_MANUAL",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_SignalSchemeSource(DE_SignalSchemeSource e) {
  if (::flatbuffers::IsOutRange(e, DE_SignalSchemeSource_SIGNAL_CONTROLLER, DE_SignalSchemeSource_CLOUD_MANUAL)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_SignalSchemeSource()[index];
}

struct MSG_SignalScheme FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_SignalSchemeBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SCHEME_ID = 4,
    VT_NODE_ID = 6,
    VT_CYCLE = 8,
    VT_CONTROL_MODE = 10,
    VT_MIN_CYCLE = 12,
    VT_MAX_CYCLE = 14,
    VT_BASE_SIGNAL_SCHEME_ID = 16,
    VT_OFFSET = 18,
    VT_PHASES = 20,
    VT_TIME_RECORDS = 22,
    VT_MSG_ID = 24,
    VT_EXTRA = 26,
    VT_SOURCE = 28
  };
  int32_t scheme_id() const {
    return GetField<int32_t>(VT_SCHEME_ID, 0);
  }
  const MECData::DF_NodeReferenceID *node_id() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_NODE_ID);
  }
  uint16_t cycle() const {
    return GetField<uint16_t>(VT_CYCLE, 0);
  }
  MECData::DE_SignalControlMode control_mode() const {
    return static_cast<MECData::DE_SignalControlMode>(GetField<uint8_t>(VT_CONTROL_MODE, 0));
  }
  uint16_t min_cycle() const {
    return GetField<uint16_t>(VT_MIN_CYCLE, 0);
  }
  uint16_t max_cycle() const {
    return GetField<uint16_t>(VT_MAX_CYCLE, 0);
  }
  int32_t base_signal_scheme_id() const {
    return GetField<int32_t>(VT_BASE_SIGNAL_SCHEME_ID, 0);
  }
  int16_t offset() const {
    return GetField<int16_t>(VT_OFFSET, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Phasic>> *phases() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Phasic>> *>(VT_PHASES);
  }
  const MECData::DF_DebugTimeRecords *time_records() const {
    return GetPointer<const MECData::DF_DebugTimeRecords *>(VT_TIME_RECORDS);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  const ::flatbuffers::String *extra() const {
    return GetPointer<const ::flatbuffers::String *>(VT_EXTRA);
  }
  MECData::DE_SignalSchemeSource source() const {
    return static_cast<MECData::DE_SignalSchemeSource>(GetField<uint8_t>(VT_SOURCE, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_SCHEME_ID, 4) &&
           VerifyOffset(verifier, VT_NODE_ID) &&
           verifier.VerifyTable(node_id()) &&
           VerifyField<uint16_t>(verifier, VT_CYCLE, 2) &&
           VerifyField<uint8_t>(verifier, VT_CONTROL_MODE, 1) &&
           VerifyField<uint16_t>(verifier, VT_MIN_CYCLE, 2) &&
           VerifyField<uint16_t>(verifier, VT_MAX_CYCLE, 2) &&
           VerifyField<int32_t>(verifier, VT_BASE_SIGNAL_SCHEME_ID, 4) &&
           VerifyField<int16_t>(verifier, VT_OFFSET, 2) &&
           VerifyOffset(verifier, VT_PHASES) &&
           verifier.VerifyVector(phases()) &&
           verifier.VerifyVectorOfTables(phases()) &&
           VerifyOffset(verifier, VT_TIME_RECORDS) &&
           verifier.VerifyTable(time_records()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           VerifyOffset(verifier, VT_EXTRA) &&
           verifier.VerifyString(extra()) &&
           VerifyField<uint8_t>(verifier, VT_SOURCE, 1) &&
           verifier.EndTable();
  }
};

struct MSG_SignalSchemeBuilder {
  typedef MSG_SignalScheme Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_scheme_id(int32_t scheme_id) {
    fbb_.AddElement<int32_t>(MSG_SignalScheme::VT_SCHEME_ID, scheme_id, 0);
  }
  void add_node_id(::flatbuffers::Offset<MECData::DF_NodeReferenceID> node_id) {
    fbb_.AddOffset(MSG_SignalScheme::VT_NODE_ID, node_id);
  }
  void add_cycle(uint16_t cycle) {
    fbb_.AddElement<uint16_t>(MSG_SignalScheme::VT_CYCLE, cycle, 0);
  }
  void add_control_mode(MECData::DE_SignalControlMode control_mode) {
    fbb_.AddElement<uint8_t>(MSG_SignalScheme::VT_CONTROL_MODE, static_cast<uint8_t>(control_mode), 0);
  }
  void add_min_cycle(uint16_t min_cycle) {
    fbb_.AddElement<uint16_t>(MSG_SignalScheme::VT_MIN_CYCLE, min_cycle, 0);
  }
  void add_max_cycle(uint16_t max_cycle) {
    fbb_.AddElement<uint16_t>(MSG_SignalScheme::VT_MAX_CYCLE, max_cycle, 0);
  }
  void add_base_signal_scheme_id(int32_t base_signal_scheme_id) {
    fbb_.AddElement<int32_t>(MSG_SignalScheme::VT_BASE_SIGNAL_SCHEME_ID, base_signal_scheme_id, 0);
  }
  void add_offset(int16_t offset) {
    fbb_.AddElement<int16_t>(MSG_SignalScheme::VT_OFFSET, offset, 0);
  }
  void add_phases(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Phasic>>> phases) {
    fbb_.AddOffset(MSG_SignalScheme::VT_PHASES, phases);
  }
  void add_time_records(::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records) {
    fbb_.AddOffset(MSG_SignalScheme::VT_TIME_RECORDS, time_records);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_SignalScheme::VT_MSG_ID, msg_id, 0);
  }
  void add_extra(::flatbuffers::Offset<::flatbuffers::String> extra) {
    fbb_.AddOffset(MSG_SignalScheme::VT_EXTRA, extra);
  }
  void add_source(MECData::DE_SignalSchemeSource source) {
    fbb_.AddElement<uint8_t>(MSG_SignalScheme::VT_SOURCE, static_cast<uint8_t>(source), 0);
  }
  explicit MSG_SignalSchemeBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_SignalScheme> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_SignalScheme>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_SignalScheme> CreateMSG_SignalScheme(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t scheme_id = 0,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> node_id = 0,
    uint16_t cycle = 0,
    MECData::DE_SignalControlMode control_mode = MECData::DE_SignalControlMode_CYCLIC_FIXED,
    uint16_t min_cycle = 0,
    uint16_t max_cycle = 0,
    int32_t base_signal_scheme_id = 0,
    int16_t offset = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Phasic>>> phases = 0,
    ::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records = 0,
    int64_t msg_id = 0,
    ::flatbuffers::Offset<::flatbuffers::String> extra = 0,
    MECData::DE_SignalSchemeSource source = MECData::DE_SignalSchemeSource_SIGNAL_CONTROLLER) {
  MSG_SignalSchemeBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_extra(extra);
  builder_.add_time_records(time_records);
  builder_.add_phases(phases);
  builder_.add_base_signal_scheme_id(base_signal_scheme_id);
  builder_.add_node_id(node_id);
  builder_.add_scheme_id(scheme_id);
  builder_.add_offset(offset);
  builder_.add_max_cycle(max_cycle);
  builder_.add_min_cycle(min_cycle);
  builder_.add_cycle(cycle);
  builder_.add_source(source);
  builder_.add_control_mode(control_mode);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_SignalScheme> CreateMSG_SignalSchemeDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t scheme_id = 0,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> node_id = 0,
    uint16_t cycle = 0,
    MECData::DE_SignalControlMode control_mode = MECData::DE_SignalControlMode_CYCLIC_FIXED,
    uint16_t min_cycle = 0,
    uint16_t max_cycle = 0,
    int32_t base_signal_scheme_id = 0,
    int16_t offset = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_Phasic>> *phases = nullptr,
    ::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records = 0,
    int64_t msg_id = 0,
    const char *extra = nullptr,
    MECData::DE_SignalSchemeSource source = MECData::DE_SignalSchemeSource_SIGNAL_CONTROLLER) {
  auto phases__ = phases ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_Phasic>>(*phases) : 0;
  auto extra__ = extra ? _fbb.CreateString(extra) : 0;
  return MECData::CreateMSG_SignalScheme(
      _fbb,
      scheme_id,
      node_id,
      cycle,
      control_mode,
      min_cycle,
      max_cycle,
      base_signal_scheme_id,
      offset,
      phases__,
      time_records,
      msg_id,
      extra__,
      source);
}

inline const MECData::MSG_SignalScheme *GetMSG_SignalScheme(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_SignalScheme>(buf);
}

inline const MECData::MSG_SignalScheme *GetSizePrefixedMSG_SignalScheme(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_SignalScheme>(buf);
}

inline bool VerifyMSG_SignalSchemeBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_SignalScheme>(nullptr);
}

inline bool VerifySizePrefixedMSG_SignalSchemeBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_SignalScheme>(nullptr);
}

inline void FinishMSG_SignalSchemeBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_SignalScheme> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_SignalSchemeBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_SignalScheme> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_SIGNALSCHEME_MECDATA_H_
