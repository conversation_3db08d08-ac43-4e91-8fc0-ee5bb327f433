// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_VEHICLECOORDINATION_MECDATA_H_
#define FLATBUFFERS_GENERATED_VEHICLECOORDINATION_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "CoordinationInfo_generated.h"
#include "DriveSuggestion_generated.h"
#include "PathPlanningPoint_generated.h"

namespace MECData {

struct DF_VehicleCoordination;
struct DF_VehicleCoordinationBuilder;

struct DF_VehicleCoordination FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_VehicleCoordinationBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VEHID = 4,
    VT_DRIVESUGGESTION = 6,
    VT_PATHGUIDANCE = 8,
    VT_INFO = 10
  };
  const ::flatbuffers::Vector<uint8_t> *vehId() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_VEHID);
  }
  const MECData::DF_DriveSuggestion *driveSuggestion() const {
    return GetPointer<const MECData::DF_DriveSuggestion *>(VT_DRIVESUGGESTION);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PathPlanningPoint>> *pathGuidance() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PathPlanningPoint>> *>(VT_PATHGUIDANCE);
  }
  const MECData::DE_CoordinationInfo *info() const {
    return GetPointer<const MECData::DE_CoordinationInfo *>(VT_INFO);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_VEHID) &&
           verifier.VerifyVector(vehId()) &&
           VerifyOffset(verifier, VT_DRIVESUGGESTION) &&
           verifier.VerifyTable(driveSuggestion()) &&
           VerifyOffset(verifier, VT_PATHGUIDANCE) &&
           verifier.VerifyVector(pathGuidance()) &&
           verifier.VerifyVectorOfTables(pathGuidance()) &&
           VerifyOffset(verifier, VT_INFO) &&
           verifier.VerifyTable(info()) &&
           verifier.EndTable();
  }
};

struct DF_VehicleCoordinationBuilder {
  typedef DF_VehicleCoordination Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_vehId(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> vehId) {
    fbb_.AddOffset(DF_VehicleCoordination::VT_VEHID, vehId);
  }
  void add_driveSuggestion(::flatbuffers::Offset<MECData::DF_DriveSuggestion> driveSuggestion) {
    fbb_.AddOffset(DF_VehicleCoordination::VT_DRIVESUGGESTION, driveSuggestion);
  }
  void add_pathGuidance(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PathPlanningPoint>>> pathGuidance) {
    fbb_.AddOffset(DF_VehicleCoordination::VT_PATHGUIDANCE, pathGuidance);
  }
  void add_info(::flatbuffers::Offset<MECData::DE_CoordinationInfo> info) {
    fbb_.AddOffset(DF_VehicleCoordination::VT_INFO, info);
  }
  explicit DF_VehicleCoordinationBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_VehicleCoordination> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_VehicleCoordination>(end);
    fbb_.Required(o, DF_VehicleCoordination::VT_VEHID);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_VehicleCoordination> CreateDF_VehicleCoordination(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> vehId = 0,
    ::flatbuffers::Offset<MECData::DF_DriveSuggestion> driveSuggestion = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PathPlanningPoint>>> pathGuidance = 0,
    ::flatbuffers::Offset<MECData::DE_CoordinationInfo> info = 0) {
  DF_VehicleCoordinationBuilder builder_(_fbb);
  builder_.add_info(info);
  builder_.add_pathGuidance(pathGuidance);
  builder_.add_driveSuggestion(driveSuggestion);
  builder_.add_vehId(vehId);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_VehicleCoordination> CreateDF_VehicleCoordinationDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *vehId = nullptr,
    ::flatbuffers::Offset<MECData::DF_DriveSuggestion> driveSuggestion = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_PathPlanningPoint>> *pathGuidance = nullptr,
    ::flatbuffers::Offset<MECData::DE_CoordinationInfo> info = 0) {
  auto vehId__ = vehId ? _fbb.CreateVector<uint8_t>(*vehId) : 0;
  auto pathGuidance__ = pathGuidance ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_PathPlanningPoint>>(*pathGuidance) : 0;
  return MECData::CreateDF_VehicleCoordination(
      _fbb,
      vehId__,
      driveSuggestion,
      pathGuidance__,
      info);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_VEHICLECOORDINATION_MECDATA_H_
