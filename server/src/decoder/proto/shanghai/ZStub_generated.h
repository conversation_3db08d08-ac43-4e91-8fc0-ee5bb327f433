// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_ZSTUB_MECDATA_H_
#define FLATBUFFERS_GENERATED_ZSTUB_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "CommunicationProtocol_generated.h"
#include "ModuleIOConfig_generated.h"

namespace MECData {

struct DE_MQTTConfig;
struct DE_MQTTConfigBuilder;

struct DE_UDPConfig;
struct DE_UDPConfigBuilder;

struct DE_USOCKConfig;
struct DE_USOCKConfigBuilder;

struct DE_TCPConfig;
struct DE_TCPConfigBuilder;

struct DE_CacheConfig;
struct DE_CacheConfigBuilder;

struct DF_QueueCacheConfig;
struct DF_QueueCacheConfigBuilder;

struct DF_EndpointConfig;
struct DF_EndpointConfigBuilder;

struct DF_Endpoint;
struct DF_EndpointBuilder;

struct DE_MECServiceConfig;
struct DE_MECServiceConfigBuilder;

struct DF_ZStubSettings;
struct DF_ZStubSettingsBuilder;

struct DF_ZStubConfig;
struct DF_ZStubConfigBuilder;

enum DE_SerializationMethod : int8_t {
  DE_SerializationMethod_JSON = 0,
  DE_SerializationMethod_PROTOBUF = 1,
  DE_SerializationMethod_FLATBUFFERS = 2,
  DE_SerializationMethod_MIN = DE_SerializationMethod_JSON,
  DE_SerializationMethod_MAX = DE_SerializationMethod_FLATBUFFERS
};

inline const DE_SerializationMethod (&EnumValuesDE_SerializationMethod())[3] {
  static const DE_SerializationMethod values[] = {
    DE_SerializationMethod_JSON,
    DE_SerializationMethod_PROTOBUF,
    DE_SerializationMethod_FLATBUFFERS
  };
  return values;
}

inline const char * const *EnumNamesDE_SerializationMethod() {
  static const char * const names[4] = {
    "JSON",
    "PROTOBUF",
    "FLATBUFFERS",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_SerializationMethod(DE_SerializationMethod e) {
  if (::flatbuffers::IsOutRange(e, DE_SerializationMethod_JSON, DE_SerializationMethod_FLATBUFFERS)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_SerializationMethod()[index];
}

enum DF_CommunicationConfig : uint8_t {
  DF_CommunicationConfig_NONE = 0,
  DF_CommunicationConfig_DE_MQTTConfig = 1,
  DF_CommunicationConfig_DE_UDPConfig = 2,
  DF_CommunicationConfig_DE_USOCKConfig = 3,
  DF_CommunicationConfig_DE_TCPConfig = 4,
  DF_CommunicationConfig_MIN = DF_CommunicationConfig_NONE,
  DF_CommunicationConfig_MAX = DF_CommunicationConfig_DE_TCPConfig
};

inline const DF_CommunicationConfig (&EnumValuesDF_CommunicationConfig())[5] {
  static const DF_CommunicationConfig values[] = {
    DF_CommunicationConfig_NONE,
    DF_CommunicationConfig_DE_MQTTConfig,
    DF_CommunicationConfig_DE_UDPConfig,
    DF_CommunicationConfig_DE_USOCKConfig,
    DF_CommunicationConfig_DE_TCPConfig
  };
  return values;
}

inline const char * const *EnumNamesDF_CommunicationConfig() {
  static const char * const names[6] = {
    "NONE",
    "DE_MQTTConfig",
    "DE_UDPConfig",
    "DE_USOCKConfig",
    "DE_TCPConfig",
    nullptr
  };
  return names;
}

inline const char *EnumNameDF_CommunicationConfig(DF_CommunicationConfig e) {
  if (::flatbuffers::IsOutRange(e, DF_CommunicationConfig_NONE, DF_CommunicationConfig_DE_TCPConfig)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDF_CommunicationConfig()[index];
}

template<typename T> struct DF_CommunicationConfigTraits {
  static const DF_CommunicationConfig enum_value = DF_CommunicationConfig_NONE;
};

template<> struct DF_CommunicationConfigTraits<MECData::DE_MQTTConfig> {
  static const DF_CommunicationConfig enum_value = DF_CommunicationConfig_DE_MQTTConfig;
};

template<> struct DF_CommunicationConfigTraits<MECData::DE_UDPConfig> {
  static const DF_CommunicationConfig enum_value = DF_CommunicationConfig_DE_UDPConfig;
};

template<> struct DF_CommunicationConfigTraits<MECData::DE_USOCKConfig> {
  static const DF_CommunicationConfig enum_value = DF_CommunicationConfig_DE_USOCKConfig;
};

template<> struct DF_CommunicationConfigTraits<MECData::DE_TCPConfig> {
  static const DF_CommunicationConfig enum_value = DF_CommunicationConfig_DE_TCPConfig;
};

bool VerifyDF_CommunicationConfig(::flatbuffers::Verifier &verifier, const void *obj, DF_CommunicationConfig type);
bool VerifyDF_CommunicationConfigVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types);

enum DE_QueueingModel : int8_t {
  DE_QueueingModel_FIFO = 0,
  DE_QueueingModel_FILO = 1,
  DE_QueueingModel_MIN = DE_QueueingModel_FIFO,
  DE_QueueingModel_MAX = DE_QueueingModel_FILO
};

inline const DE_QueueingModel (&EnumValuesDE_QueueingModel())[2] {
  static const DE_QueueingModel values[] = {
    DE_QueueingModel_FIFO,
    DE_QueueingModel_FILO
  };
  return values;
}

inline const char * const *EnumNamesDE_QueueingModel() {
  static const char * const names[3] = {
    "FIFO",
    "FILO",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_QueueingModel(DE_QueueingModel e) {
  if (::flatbuffers::IsOutRange(e, DE_QueueingModel_FIFO, DE_QueueingModel_FILO)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_QueueingModel()[index];
}

struct DE_MQTTConfig FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_MQTTConfigBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_HOST = 4,
    VT_PORT = 6,
    VT_USERNAME = 8,
    VT_PASSWORD = 10,
    VT_CLIENT_ID = 12,
    VT_TLS_ENABLED = 14,
    VT_CERTIFICATE_PATH = 16
  };
  const ::flatbuffers::String *host() const {
    return GetPointer<const ::flatbuffers::String *>(VT_HOST);
  }
  uint16_t port() const {
    return GetField<uint16_t>(VT_PORT, 0);
  }
  const ::flatbuffers::String *username() const {
    return GetPointer<const ::flatbuffers::String *>(VT_USERNAME);
  }
  const ::flatbuffers::String *password() const {
    return GetPointer<const ::flatbuffers::String *>(VT_PASSWORD);
  }
  const ::flatbuffers::String *client_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_CLIENT_ID);
  }
  bool tls_enabled() const {
    return GetField<uint8_t>(VT_TLS_ENABLED, 0) != 0;
  }
  const ::flatbuffers::String *certificate_path() const {
    return GetPointer<const ::flatbuffers::String *>(VT_CERTIFICATE_PATH);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_HOST) &&
           verifier.VerifyString(host()) &&
           VerifyField<uint16_t>(verifier, VT_PORT, 2) &&
           VerifyOffset(verifier, VT_USERNAME) &&
           verifier.VerifyString(username()) &&
           VerifyOffset(verifier, VT_PASSWORD) &&
           verifier.VerifyString(password()) &&
           VerifyOffset(verifier, VT_CLIENT_ID) &&
           verifier.VerifyString(client_id()) &&
           VerifyField<uint8_t>(verifier, VT_TLS_ENABLED, 1) &&
           VerifyOffset(verifier, VT_CERTIFICATE_PATH) &&
           verifier.VerifyString(certificate_path()) &&
           verifier.EndTable();
  }
};

struct DE_MQTTConfigBuilder {
  typedef DE_MQTTConfig Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_host(::flatbuffers::Offset<::flatbuffers::String> host) {
    fbb_.AddOffset(DE_MQTTConfig::VT_HOST, host);
  }
  void add_port(uint16_t port) {
    fbb_.AddElement<uint16_t>(DE_MQTTConfig::VT_PORT, port, 0);
  }
  void add_username(::flatbuffers::Offset<::flatbuffers::String> username) {
    fbb_.AddOffset(DE_MQTTConfig::VT_USERNAME, username);
  }
  void add_password(::flatbuffers::Offset<::flatbuffers::String> password) {
    fbb_.AddOffset(DE_MQTTConfig::VT_PASSWORD, password);
  }
  void add_client_id(::flatbuffers::Offset<::flatbuffers::String> client_id) {
    fbb_.AddOffset(DE_MQTTConfig::VT_CLIENT_ID, client_id);
  }
  void add_tls_enabled(bool tls_enabled) {
    fbb_.AddElement<uint8_t>(DE_MQTTConfig::VT_TLS_ENABLED, static_cast<uint8_t>(tls_enabled), 0);
  }
  void add_certificate_path(::flatbuffers::Offset<::flatbuffers::String> certificate_path) {
    fbb_.AddOffset(DE_MQTTConfig::VT_CERTIFICATE_PATH, certificate_path);
  }
  explicit DE_MQTTConfigBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_MQTTConfig> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_MQTTConfig>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_MQTTConfig> CreateDE_MQTTConfig(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> host = 0,
    uint16_t port = 0,
    ::flatbuffers::Offset<::flatbuffers::String> username = 0,
    ::flatbuffers::Offset<::flatbuffers::String> password = 0,
    ::flatbuffers::Offset<::flatbuffers::String> client_id = 0,
    bool tls_enabled = false,
    ::flatbuffers::Offset<::flatbuffers::String> certificate_path = 0) {
  DE_MQTTConfigBuilder builder_(_fbb);
  builder_.add_certificate_path(certificate_path);
  builder_.add_client_id(client_id);
  builder_.add_password(password);
  builder_.add_username(username);
  builder_.add_host(host);
  builder_.add_port(port);
  builder_.add_tls_enabled(tls_enabled);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DE_MQTTConfig> CreateDE_MQTTConfigDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *host = nullptr,
    uint16_t port = 0,
    const char *username = nullptr,
    const char *password = nullptr,
    const char *client_id = nullptr,
    bool tls_enabled = false,
    const char *certificate_path = nullptr) {
  auto host__ = host ? _fbb.CreateString(host) : 0;
  auto username__ = username ? _fbb.CreateString(username) : 0;
  auto password__ = password ? _fbb.CreateString(password) : 0;
  auto client_id__ = client_id ? _fbb.CreateString(client_id) : 0;
  auto certificate_path__ = certificate_path ? _fbb.CreateString(certificate_path) : 0;
  return MECData::CreateDE_MQTTConfig(
      _fbb,
      host__,
      port,
      username__,
      password__,
      client_id__,
      tls_enabled,
      certificate_path__);
}

struct DE_UDPConfig FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_UDPConfigBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SERVER_ADDRESS = 4,
    VT_SERVER_PORT = 6,
    VT_CLIENT_ADDRESS = 8,
    VT_CLIENT_PORT = 10
  };
  const ::flatbuffers::String *server_address() const {
    return GetPointer<const ::flatbuffers::String *>(VT_SERVER_ADDRESS);
  }
  uint16_t server_port() const {
    return GetField<uint16_t>(VT_SERVER_PORT, 0);
  }
  const ::flatbuffers::String *client_address() const {
    return GetPointer<const ::flatbuffers::String *>(VT_CLIENT_ADDRESS);
  }
  uint16_t client_port() const {
    return GetField<uint16_t>(VT_CLIENT_PORT, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_SERVER_ADDRESS) &&
           verifier.VerifyString(server_address()) &&
           VerifyField<uint16_t>(verifier, VT_SERVER_PORT, 2) &&
           VerifyOffset(verifier, VT_CLIENT_ADDRESS) &&
           verifier.VerifyString(client_address()) &&
           VerifyField<uint16_t>(verifier, VT_CLIENT_PORT, 2) &&
           verifier.EndTable();
  }
};

struct DE_UDPConfigBuilder {
  typedef DE_UDPConfig Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_server_address(::flatbuffers::Offset<::flatbuffers::String> server_address) {
    fbb_.AddOffset(DE_UDPConfig::VT_SERVER_ADDRESS, server_address);
  }
  void add_server_port(uint16_t server_port) {
    fbb_.AddElement<uint16_t>(DE_UDPConfig::VT_SERVER_PORT, server_port, 0);
  }
  void add_client_address(::flatbuffers::Offset<::flatbuffers::String> client_address) {
    fbb_.AddOffset(DE_UDPConfig::VT_CLIENT_ADDRESS, client_address);
  }
  void add_client_port(uint16_t client_port) {
    fbb_.AddElement<uint16_t>(DE_UDPConfig::VT_CLIENT_PORT, client_port, 0);
  }
  explicit DE_UDPConfigBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_UDPConfig> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_UDPConfig>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_UDPConfig> CreateDE_UDPConfig(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> server_address = 0,
    uint16_t server_port = 0,
    ::flatbuffers::Offset<::flatbuffers::String> client_address = 0,
    uint16_t client_port = 0) {
  DE_UDPConfigBuilder builder_(_fbb);
  builder_.add_client_address(client_address);
  builder_.add_server_address(server_address);
  builder_.add_client_port(client_port);
  builder_.add_server_port(server_port);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DE_UDPConfig> CreateDE_UDPConfigDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *server_address = nullptr,
    uint16_t server_port = 0,
    const char *client_address = nullptr,
    uint16_t client_port = 0) {
  auto server_address__ = server_address ? _fbb.CreateString(server_address) : 0;
  auto client_address__ = client_address ? _fbb.CreateString(client_address) : 0;
  return MECData::CreateDE_UDPConfig(
      _fbb,
      server_address__,
      server_port,
      client_address__,
      client_port);
}

struct DE_USOCKConfig FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_USOCKConfigBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SOCKET_FILE = 4
  };
  const ::flatbuffers::String *socket_file() const {
    return GetPointer<const ::flatbuffers::String *>(VT_SOCKET_FILE);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_SOCKET_FILE) &&
           verifier.VerifyString(socket_file()) &&
           verifier.EndTable();
  }
};

struct DE_USOCKConfigBuilder {
  typedef DE_USOCKConfig Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_socket_file(::flatbuffers::Offset<::flatbuffers::String> socket_file) {
    fbb_.AddOffset(DE_USOCKConfig::VT_SOCKET_FILE, socket_file);
  }
  explicit DE_USOCKConfigBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_USOCKConfig> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_USOCKConfig>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_USOCKConfig> CreateDE_USOCKConfig(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> socket_file = 0) {
  DE_USOCKConfigBuilder builder_(_fbb);
  builder_.add_socket_file(socket_file);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DE_USOCKConfig> CreateDE_USOCKConfigDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *socket_file = nullptr) {
  auto socket_file__ = socket_file ? _fbb.CreateString(socket_file) : 0;
  return MECData::CreateDE_USOCKConfig(
      _fbb,
      socket_file__);
}

struct DE_TCPConfig FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_TCPConfigBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SERVER_ADDRESS = 4,
    VT_SERVER_PORT = 6,
    VT_CLIENT_ADDRESS = 8,
    VT_CLIENT_PORT = 10
  };
  const ::flatbuffers::String *server_address() const {
    return GetPointer<const ::flatbuffers::String *>(VT_SERVER_ADDRESS);
  }
  uint16_t server_port() const {
    return GetField<uint16_t>(VT_SERVER_PORT, 0);
  }
  const ::flatbuffers::String *client_address() const {
    return GetPointer<const ::flatbuffers::String *>(VT_CLIENT_ADDRESS);
  }
  uint16_t client_port() const {
    return GetField<uint16_t>(VT_CLIENT_PORT, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_SERVER_ADDRESS) &&
           verifier.VerifyString(server_address()) &&
           VerifyField<uint16_t>(verifier, VT_SERVER_PORT, 2) &&
           VerifyOffset(verifier, VT_CLIENT_ADDRESS) &&
           verifier.VerifyString(client_address()) &&
           VerifyField<uint16_t>(verifier, VT_CLIENT_PORT, 2) &&
           verifier.EndTable();
  }
};

struct DE_TCPConfigBuilder {
  typedef DE_TCPConfig Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_server_address(::flatbuffers::Offset<::flatbuffers::String> server_address) {
    fbb_.AddOffset(DE_TCPConfig::VT_SERVER_ADDRESS, server_address);
  }
  void add_server_port(uint16_t server_port) {
    fbb_.AddElement<uint16_t>(DE_TCPConfig::VT_SERVER_PORT, server_port, 0);
  }
  void add_client_address(::flatbuffers::Offset<::flatbuffers::String> client_address) {
    fbb_.AddOffset(DE_TCPConfig::VT_CLIENT_ADDRESS, client_address);
  }
  void add_client_port(uint16_t client_port) {
    fbb_.AddElement<uint16_t>(DE_TCPConfig::VT_CLIENT_PORT, client_port, 0);
  }
  explicit DE_TCPConfigBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_TCPConfig> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_TCPConfig>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_TCPConfig> CreateDE_TCPConfig(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> server_address = 0,
    uint16_t server_port = 0,
    ::flatbuffers::Offset<::flatbuffers::String> client_address = 0,
    uint16_t client_port = 0) {
  DE_TCPConfigBuilder builder_(_fbb);
  builder_.add_client_address(client_address);
  builder_.add_server_address(server_address);
  builder_.add_client_port(client_port);
  builder_.add_server_port(server_port);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DE_TCPConfig> CreateDE_TCPConfigDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *server_address = nullptr,
    uint16_t server_port = 0,
    const char *client_address = nullptr,
    uint16_t client_port = 0) {
  auto server_address__ = server_address ? _fbb.CreateString(server_address) : 0;
  auto client_address__ = client_address ? _fbb.CreateString(client_address) : 0;
  return MECData::CreateDE_TCPConfig(
      _fbb,
      server_address__,
      server_port,
      client_address__,
      client_port);
}

struct DE_CacheConfig FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_CacheConfigBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MAX_SIZE_BYTES = 4,
    VT_MAX_ENTRIES = 6
  };
  uint64_t max_size_bytes() const {
    return GetField<uint64_t>(VT_MAX_SIZE_BYTES, 0);
  }
  uint64_t max_entries() const {
    return GetField<uint64_t>(VT_MAX_ENTRIES, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint64_t>(verifier, VT_MAX_SIZE_BYTES, 8) &&
           VerifyField<uint64_t>(verifier, VT_MAX_ENTRIES, 8) &&
           verifier.EndTable();
  }
};

struct DE_CacheConfigBuilder {
  typedef DE_CacheConfig Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_max_size_bytes(uint64_t max_size_bytes) {
    fbb_.AddElement<uint64_t>(DE_CacheConfig::VT_MAX_SIZE_BYTES, max_size_bytes, 0);
  }
  void add_max_entries(uint64_t max_entries) {
    fbb_.AddElement<uint64_t>(DE_CacheConfig::VT_MAX_ENTRIES, max_entries, 0);
  }
  explicit DE_CacheConfigBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_CacheConfig> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_CacheConfig>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_CacheConfig> CreateDE_CacheConfig(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint64_t max_size_bytes = 0,
    uint64_t max_entries = 0) {
  DE_CacheConfigBuilder builder_(_fbb);
  builder_.add_max_entries(max_entries);
  builder_.add_max_size_bytes(max_size_bytes);
  return builder_.Finish();
}

struct DF_QueueCacheConfig FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_QueueCacheConfigBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_QUEUE_MODEL = 4,
    VT_CACHE_CONFIG = 6
  };
  MECData::DE_QueueingModel queue_model() const {
    return static_cast<MECData::DE_QueueingModel>(GetField<int8_t>(VT_QUEUE_MODEL, 0));
  }
  const MECData::DE_CacheConfig *cache_config() const {
    return GetPointer<const MECData::DE_CacheConfig *>(VT_CACHE_CONFIG);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_QUEUE_MODEL, 1) &&
           VerifyOffset(verifier, VT_CACHE_CONFIG) &&
           verifier.VerifyTable(cache_config()) &&
           verifier.EndTable();
  }
};

struct DF_QueueCacheConfigBuilder {
  typedef DF_QueueCacheConfig Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_queue_model(MECData::DE_QueueingModel queue_model) {
    fbb_.AddElement<int8_t>(DF_QueueCacheConfig::VT_QUEUE_MODEL, static_cast<int8_t>(queue_model), 0);
  }
  void add_cache_config(::flatbuffers::Offset<MECData::DE_CacheConfig> cache_config) {
    fbb_.AddOffset(DF_QueueCacheConfig::VT_CACHE_CONFIG, cache_config);
  }
  explicit DF_QueueCacheConfigBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_QueueCacheConfig> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_QueueCacheConfig>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_QueueCacheConfig> CreateDF_QueueCacheConfig(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    MECData::DE_QueueingModel queue_model = MECData::DE_QueueingModel_FIFO,
    ::flatbuffers::Offset<MECData::DE_CacheConfig> cache_config = 0) {
  DF_QueueCacheConfigBuilder builder_(_fbb);
  builder_.add_cache_config(cache_config);
  builder_.add_queue_model(queue_model);
  return builder_.Finish();
}

struct DF_EndpointConfig FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_EndpointConfigBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_HOST = 4,
    VT_PORT = 6,
    VT_PROTOCOL = 8
  };
  const ::flatbuffers::String *host() const {
    return GetPointer<const ::flatbuffers::String *>(VT_HOST);
  }
  uint16_t port() const {
    return GetField<uint16_t>(VT_PORT, 0);
  }
  MECData::DE_CommunicationProtocol protocol() const {
    return static_cast<MECData::DE_CommunicationProtocol>(GetField<int8_t>(VT_PROTOCOL, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_HOST) &&
           verifier.VerifyString(host()) &&
           VerifyField<uint16_t>(verifier, VT_PORT, 2) &&
           VerifyField<int8_t>(verifier, VT_PROTOCOL, 1) &&
           verifier.EndTable();
  }
};

struct DF_EndpointConfigBuilder {
  typedef DF_EndpointConfig Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_host(::flatbuffers::Offset<::flatbuffers::String> host) {
    fbb_.AddOffset(DF_EndpointConfig::VT_HOST, host);
  }
  void add_port(uint16_t port) {
    fbb_.AddElement<uint16_t>(DF_EndpointConfig::VT_PORT, port, 0);
  }
  void add_protocol(MECData::DE_CommunicationProtocol protocol) {
    fbb_.AddElement<int8_t>(DF_EndpointConfig::VT_PROTOCOL, static_cast<int8_t>(protocol), 0);
  }
  explicit DF_EndpointConfigBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_EndpointConfig> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_EndpointConfig>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_EndpointConfig> CreateDF_EndpointConfig(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> host = 0,
    uint16_t port = 0,
    MECData::DE_CommunicationProtocol protocol = MECData::DE_CommunicationProtocol_MQTT) {
  DF_EndpointConfigBuilder builder_(_fbb);
  builder_.add_host(host);
  builder_.add_port(port);
  builder_.add_protocol(protocol);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_EndpointConfig> CreateDF_EndpointConfigDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *host = nullptr,
    uint16_t port = 0,
    MECData::DE_CommunicationProtocol protocol = MECData::DE_CommunicationProtocol_MQTT) {
  auto host__ = host ? _fbb.CreateString(host) : 0;
  return MECData::CreateDF_EndpointConfig(
      _fbb,
      host__,
      port,
      protocol);
}

struct DF_Endpoint FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_EndpointBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NAME = 4,
    VT_DESCRIPTION = 6,
    VT_ENDPOINT_CONFIG = 8,
    VT_IO_CONFIG = 10
  };
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  const ::flatbuffers::String *description() const {
    return GetPointer<const ::flatbuffers::String *>(VT_DESCRIPTION);
  }
  const MECData::DF_EndpointConfig *endpoint_config() const {
    return GetPointer<const MECData::DF_EndpointConfig *>(VT_ENDPOINT_CONFIG);
  }
  const MECData::DF_ModuleIOConfig *io_config() const {
    return GetPointer<const MECData::DF_ModuleIOConfig *>(VT_IO_CONFIG);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_DESCRIPTION) &&
           verifier.VerifyString(description()) &&
           VerifyOffset(verifier, VT_ENDPOINT_CONFIG) &&
           verifier.VerifyTable(endpoint_config()) &&
           VerifyOffset(verifier, VT_IO_CONFIG) &&
           verifier.VerifyTable(io_config()) &&
           verifier.EndTable();
  }
};

struct DF_EndpointBuilder {
  typedef DF_Endpoint Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(DF_Endpoint::VT_NAME, name);
  }
  void add_description(::flatbuffers::Offset<::flatbuffers::String> description) {
    fbb_.AddOffset(DF_Endpoint::VT_DESCRIPTION, description);
  }
  void add_endpoint_config(::flatbuffers::Offset<MECData::DF_EndpointConfig> endpoint_config) {
    fbb_.AddOffset(DF_Endpoint::VT_ENDPOINT_CONFIG, endpoint_config);
  }
  void add_io_config(::flatbuffers::Offset<MECData::DF_ModuleIOConfig> io_config) {
    fbb_.AddOffset(DF_Endpoint::VT_IO_CONFIG, io_config);
  }
  explicit DF_EndpointBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_Endpoint> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_Endpoint>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_Endpoint> CreateDF_Endpoint(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    ::flatbuffers::Offset<::flatbuffers::String> description = 0,
    ::flatbuffers::Offset<MECData::DF_EndpointConfig> endpoint_config = 0,
    ::flatbuffers::Offset<MECData::DF_ModuleIOConfig> io_config = 0) {
  DF_EndpointBuilder builder_(_fbb);
  builder_.add_io_config(io_config);
  builder_.add_endpoint_config(endpoint_config);
  builder_.add_description(description);
  builder_.add_name(name);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_Endpoint> CreateDF_EndpointDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *name = nullptr,
    const char *description = nullptr,
    ::flatbuffers::Offset<MECData::DF_EndpointConfig> endpoint_config = 0,
    ::flatbuffers::Offset<MECData::DF_ModuleIOConfig> io_config = 0) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto description__ = description ? _fbb.CreateString(description) : 0;
  return MECData::CreateDF_Endpoint(
      _fbb,
      name__,
      description__,
      endpoint_config,
      io_config);
}

struct DE_MECServiceConfig FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_MECServiceConfigBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_HOST = 4,
    VT_PORT = 6
  };
  const ::flatbuffers::String *host() const {
    return GetPointer<const ::flatbuffers::String *>(VT_HOST);
  }
  uint16_t port() const {
    return GetField<uint16_t>(VT_PORT, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_HOST) &&
           verifier.VerifyString(host()) &&
           VerifyField<uint16_t>(verifier, VT_PORT, 2) &&
           verifier.EndTable();
  }
};

struct DE_MECServiceConfigBuilder {
  typedef DE_MECServiceConfig Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_host(::flatbuffers::Offset<::flatbuffers::String> host) {
    fbb_.AddOffset(DE_MECServiceConfig::VT_HOST, host);
  }
  void add_port(uint16_t port) {
    fbb_.AddElement<uint16_t>(DE_MECServiceConfig::VT_PORT, port, 0);
  }
  explicit DE_MECServiceConfigBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_MECServiceConfig> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_MECServiceConfig>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_MECServiceConfig> CreateDE_MECServiceConfig(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> host = 0,
    uint16_t port = 0) {
  DE_MECServiceConfigBuilder builder_(_fbb);
  builder_.add_host(host);
  builder_.add_port(port);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DE_MECServiceConfig> CreateDE_MECServiceConfigDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *host = nullptr,
    uint16_t port = 0) {
  auto host__ = host ? _fbb.CreateString(host) : 0;
  return MECData::CreateDE_MECServiceConfig(
      _fbb,
      host__,
      port);
}

struct DF_ZStubSettings FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ZStubSettingsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MECDATA_SCHEMA_FILE = 4,
    VT_LOCAL_FS_ROOT = 6,
    VT_MEC_SERVICE = 8,
    VT_COMM_PROTOCOL = 10,
    VT_COMM_CONFIG_TYPE = 12,
    VT_COMM_CONFIG = 14,
    VT_QUEUE_MODEL = 16,
    VT_MODULE_CONNECTABLE_ENDPOINT = 18
  };
  const ::flatbuffers::String *mecdata_schema_file() const {
    return GetPointer<const ::flatbuffers::String *>(VT_MECDATA_SCHEMA_FILE);
  }
  const ::flatbuffers::String *local_fs_root() const {
    return GetPointer<const ::flatbuffers::String *>(VT_LOCAL_FS_ROOT);
  }
  const MECData::DE_MECServiceConfig *mec_service() const {
    return GetPointer<const MECData::DE_MECServiceConfig *>(VT_MEC_SERVICE);
  }
  MECData::DE_CommunicationProtocol comm_protocol() const {
    return static_cast<MECData::DE_CommunicationProtocol>(GetField<int8_t>(VT_COMM_PROTOCOL, 0));
  }
  MECData::DF_CommunicationConfig comm_config_type() const {
    return static_cast<MECData::DF_CommunicationConfig>(GetField<uint8_t>(VT_COMM_CONFIG_TYPE, 0));
  }
  const void *comm_config() const {
    return GetPointer<const void *>(VT_COMM_CONFIG);
  }
  template<typename T> const T *comm_config_as() const;
  const MECData::DE_MQTTConfig *comm_config_as_DE_MQTTConfig() const {
    return comm_config_type() == MECData::DF_CommunicationConfig_DE_MQTTConfig ? static_cast<const MECData::DE_MQTTConfig *>(comm_config()) : nullptr;
  }
  const MECData::DE_UDPConfig *comm_config_as_DE_UDPConfig() const {
    return comm_config_type() == MECData::DF_CommunicationConfig_DE_UDPConfig ? static_cast<const MECData::DE_UDPConfig *>(comm_config()) : nullptr;
  }
  const MECData::DE_USOCKConfig *comm_config_as_DE_USOCKConfig() const {
    return comm_config_type() == MECData::DF_CommunicationConfig_DE_USOCKConfig ? static_cast<const MECData::DE_USOCKConfig *>(comm_config()) : nullptr;
  }
  const MECData::DE_TCPConfig *comm_config_as_DE_TCPConfig() const {
    return comm_config_type() == MECData::DF_CommunicationConfig_DE_TCPConfig ? static_cast<const MECData::DE_TCPConfig *>(comm_config()) : nullptr;
  }
  const MECData::DF_QueueCacheConfig *queue_model() const {
    return GetPointer<const MECData::DF_QueueCacheConfig *>(VT_QUEUE_MODEL);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Endpoint>> *module_connectable_endpoint() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Endpoint>> *>(VT_MODULE_CONNECTABLE_ENDPOINT);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_MECDATA_SCHEMA_FILE) &&
           verifier.VerifyString(mecdata_schema_file()) &&
           VerifyOffset(verifier, VT_LOCAL_FS_ROOT) &&
           verifier.VerifyString(local_fs_root()) &&
           VerifyOffset(verifier, VT_MEC_SERVICE) &&
           verifier.VerifyTable(mec_service()) &&
           VerifyField<int8_t>(verifier, VT_COMM_PROTOCOL, 1) &&
           VerifyField<uint8_t>(verifier, VT_COMM_CONFIG_TYPE, 1) &&
           VerifyOffset(verifier, VT_COMM_CONFIG) &&
           VerifyDF_CommunicationConfig(verifier, comm_config(), comm_config_type()) &&
           VerifyOffset(verifier, VT_QUEUE_MODEL) &&
           verifier.VerifyTable(queue_model()) &&
           VerifyOffset(verifier, VT_MODULE_CONNECTABLE_ENDPOINT) &&
           verifier.VerifyVector(module_connectable_endpoint()) &&
           verifier.VerifyVectorOfTables(module_connectable_endpoint()) &&
           verifier.EndTable();
  }
};

template<> inline const MECData::DE_MQTTConfig *DF_ZStubSettings::comm_config_as<MECData::DE_MQTTConfig>() const {
  return comm_config_as_DE_MQTTConfig();
}

template<> inline const MECData::DE_UDPConfig *DF_ZStubSettings::comm_config_as<MECData::DE_UDPConfig>() const {
  return comm_config_as_DE_UDPConfig();
}

template<> inline const MECData::DE_USOCKConfig *DF_ZStubSettings::comm_config_as<MECData::DE_USOCKConfig>() const {
  return comm_config_as_DE_USOCKConfig();
}

template<> inline const MECData::DE_TCPConfig *DF_ZStubSettings::comm_config_as<MECData::DE_TCPConfig>() const {
  return comm_config_as_DE_TCPConfig();
}

struct DF_ZStubSettingsBuilder {
  typedef DF_ZStubSettings Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_mecdata_schema_file(::flatbuffers::Offset<::flatbuffers::String> mecdata_schema_file) {
    fbb_.AddOffset(DF_ZStubSettings::VT_MECDATA_SCHEMA_FILE, mecdata_schema_file);
  }
  void add_local_fs_root(::flatbuffers::Offset<::flatbuffers::String> local_fs_root) {
    fbb_.AddOffset(DF_ZStubSettings::VT_LOCAL_FS_ROOT, local_fs_root);
  }
  void add_mec_service(::flatbuffers::Offset<MECData::DE_MECServiceConfig> mec_service) {
    fbb_.AddOffset(DF_ZStubSettings::VT_MEC_SERVICE, mec_service);
  }
  void add_comm_protocol(MECData::DE_CommunicationProtocol comm_protocol) {
    fbb_.AddElement<int8_t>(DF_ZStubSettings::VT_COMM_PROTOCOL, static_cast<int8_t>(comm_protocol), 0);
  }
  void add_comm_config_type(MECData::DF_CommunicationConfig comm_config_type) {
    fbb_.AddElement<uint8_t>(DF_ZStubSettings::VT_COMM_CONFIG_TYPE, static_cast<uint8_t>(comm_config_type), 0);
  }
  void add_comm_config(::flatbuffers::Offset<void> comm_config) {
    fbb_.AddOffset(DF_ZStubSettings::VT_COMM_CONFIG, comm_config);
  }
  void add_queue_model(::flatbuffers::Offset<MECData::DF_QueueCacheConfig> queue_model) {
    fbb_.AddOffset(DF_ZStubSettings::VT_QUEUE_MODEL, queue_model);
  }
  void add_module_connectable_endpoint(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Endpoint>>> module_connectable_endpoint) {
    fbb_.AddOffset(DF_ZStubSettings::VT_MODULE_CONNECTABLE_ENDPOINT, module_connectable_endpoint);
  }
  explicit DF_ZStubSettingsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ZStubSettings> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ZStubSettings>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ZStubSettings> CreateDF_ZStubSettings(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> mecdata_schema_file = 0,
    ::flatbuffers::Offset<::flatbuffers::String> local_fs_root = 0,
    ::flatbuffers::Offset<MECData::DE_MECServiceConfig> mec_service = 0,
    MECData::DE_CommunicationProtocol comm_protocol = MECData::DE_CommunicationProtocol_MQTT,
    MECData::DF_CommunicationConfig comm_config_type = MECData::DF_CommunicationConfig_NONE,
    ::flatbuffers::Offset<void> comm_config = 0,
    ::flatbuffers::Offset<MECData::DF_QueueCacheConfig> queue_model = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Endpoint>>> module_connectable_endpoint = 0) {
  DF_ZStubSettingsBuilder builder_(_fbb);
  builder_.add_module_connectable_endpoint(module_connectable_endpoint);
  builder_.add_queue_model(queue_model);
  builder_.add_comm_config(comm_config);
  builder_.add_mec_service(mec_service);
  builder_.add_local_fs_root(local_fs_root);
  builder_.add_mecdata_schema_file(mecdata_schema_file);
  builder_.add_comm_config_type(comm_config_type);
  builder_.add_comm_protocol(comm_protocol);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ZStubSettings> CreateDF_ZStubSettingsDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *mecdata_schema_file = nullptr,
    const char *local_fs_root = nullptr,
    ::flatbuffers::Offset<MECData::DE_MECServiceConfig> mec_service = 0,
    MECData::DE_CommunicationProtocol comm_protocol = MECData::DE_CommunicationProtocol_MQTT,
    MECData::DF_CommunicationConfig comm_config_type = MECData::DF_CommunicationConfig_NONE,
    ::flatbuffers::Offset<void> comm_config = 0,
    ::flatbuffers::Offset<MECData::DF_QueueCacheConfig> queue_model = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_Endpoint>> *module_connectable_endpoint = nullptr) {
  auto mecdata_schema_file__ = mecdata_schema_file ? _fbb.CreateString(mecdata_schema_file) : 0;
  auto local_fs_root__ = local_fs_root ? _fbb.CreateString(local_fs_root) : 0;
  auto module_connectable_endpoint__ = module_connectable_endpoint ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_Endpoint>>(*module_connectable_endpoint) : 0;
  return MECData::CreateDF_ZStubSettings(
      _fbb,
      mecdata_schema_file__,
      local_fs_root__,
      mec_service,
      comm_protocol,
      comm_config_type,
      comm_config,
      queue_model,
      module_connectable_endpoint__);
}

struct DF_ZStubConfig FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ZStubConfigBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ENABLED = 4,
    VT_SETTINGS = 6
  };
  bool enabled() const {
    return GetField<uint8_t>(VT_ENABLED, 0) != 0;
  }
  const MECData::DF_ZStubSettings *settings() const {
    return GetPointer<const MECData::DF_ZStubSettings *>(VT_SETTINGS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_ENABLED, 1) &&
           VerifyOffset(verifier, VT_SETTINGS) &&
           verifier.VerifyTable(settings()) &&
           verifier.EndTable();
  }
};

struct DF_ZStubConfigBuilder {
  typedef DF_ZStubConfig Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_enabled(bool enabled) {
    fbb_.AddElement<uint8_t>(DF_ZStubConfig::VT_ENABLED, static_cast<uint8_t>(enabled), 0);
  }
  void add_settings(::flatbuffers::Offset<MECData::DF_ZStubSettings> settings) {
    fbb_.AddOffset(DF_ZStubConfig::VT_SETTINGS, settings);
  }
  explicit DF_ZStubConfigBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ZStubConfig> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ZStubConfig>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ZStubConfig> CreateDF_ZStubConfig(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    bool enabled = false,
    ::flatbuffers::Offset<MECData::DF_ZStubSettings> settings = 0) {
  DF_ZStubConfigBuilder builder_(_fbb);
  builder_.add_settings(settings);
  builder_.add_enabled(enabled);
  return builder_.Finish();
}

inline bool VerifyDF_CommunicationConfig(::flatbuffers::Verifier &verifier, const void *obj, DF_CommunicationConfig type) {
  switch (type) {
    case DF_CommunicationConfig_NONE: {
      return true;
    }
    case DF_CommunicationConfig_DE_MQTTConfig: {
      auto ptr = reinterpret_cast<const MECData::DE_MQTTConfig *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_CommunicationConfig_DE_UDPConfig: {
      auto ptr = reinterpret_cast<const MECData::DE_UDPConfig *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_CommunicationConfig_DE_USOCKConfig: {
      auto ptr = reinterpret_cast<const MECData::DE_USOCKConfig *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_CommunicationConfig_DE_TCPConfig: {
      auto ptr = reinterpret_cast<const MECData::DE_TCPConfig *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return true;
  }
}

inline bool VerifyDF_CommunicationConfigVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (::flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyDF_CommunicationConfig(
        verifier,  values->Get(i), types->GetEnum<DF_CommunicationConfig>(i))) {
      return false;
    }
  }
  return true;
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_ZSTUB_MECDATA_H_
