// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_SAFETYMESSAGE_MECDATA_H_
#define FLATBUFFERS_GENERATED_SAFETYMESSAGE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "AccelerationSet4Way_generated.h"
#include "BrakeSystemStatus_generated.h"
#include "DebugTimeRecords_generated.h"
#include "MotionConfidenceSet_generated.h"
#include "NodeReferenceID_generated.h"
#include "Position3D_generated.h"
#include "PositionConfidenceSet_generated.h"
#include "ReferPosition_generated.h"
#include "SourceType_generated.h"
#include "TimeConfidence_generated.h"
#include "TransmissionState_generated.h"
#include "VehicleCharEx_generated.h"
#include "VehicleClassification_generated.h"
#include "VehicleSafetyExtensions_generated.h"
#include "VehicleSize_generated.h"

namespace MECData {

struct MSG_SafetyMessage;
struct MSG_SafetyMessageBuilder;

struct MSG_SafetyMessage FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_SafetyMessageBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PTCTYPE = 4,
    VT_PTCID = 6,
    VT_OBUID = 8,
    VT_SOURCE = 10,
    VT_DEVICE = 12,
    VT_PLATENO = 14,
    VT_MOY = 16,
    VT_SECMARK = 18,
    VT_TIMECONFIDENCE = 20,
    VT_POS = 22,
    VT_REFERPOS = 24,
    VT_REGION = 26,
    VT_NODEID = 28,
    VT_SECTIONID = 30,
    VT_LANEID = 32,
    VT_ACCURACY = 34,
    VT_TRANSMISSION = 36,
    VT_SPEED = 38,
    VT_HEADING = 40,
    VT_ANGLE = 42,
    VT_MOTIONCFD = 44,
    VT_ACCELSET = 46,
    VT_BRAKES = 48,
    VT_SIZE = 50,
    VT_VEHICLECLASS = 52,
    VT_SAFETYEXT = 54,
    VT_VEHCHAREX = 56,
    VT_SECTION_EXT_ID = 58,
    VT_LANE_EXT_ID = 60,
    VT_TIME_RECORDS = 62,
    VT_MSG_ID = 64
  };
  uint8_t ptcType() const {
    return GetField<uint8_t>(VT_PTCTYPE, 255);
  }
  uint16_t ptcId() const {
    return GetField<uint16_t>(VT_PTCID, 65535);
  }
  const ::flatbuffers::Vector<uint8_t> *obuId() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_OBUID);
  }
  MECData::DE_SourceType source() const {
    return static_cast<MECData::DE_SourceType>(GetField<uint8_t>(VT_SOURCE, 0));
  }
  const ::flatbuffers::Vector<uint16_t> *device() const {
    return GetPointer<const ::flatbuffers::Vector<uint16_t> *>(VT_DEVICE);
  }
  const ::flatbuffers::String *plateNo() const {
    return GetPointer<const ::flatbuffers::String *>(VT_PLATENO);
  }
  uint32_t moy() const {
    return GetField<uint32_t>(VT_MOY, 4294967295);
  }
  uint16_t secMark() const {
    return GetField<uint16_t>(VT_SECMARK, 65535);
  }
  MECData::DE_TimeConfidence timeConfidence() const {
    return static_cast<MECData::DE_TimeConfidence>(GetField<int8_t>(VT_TIMECONFIDENCE, 0));
  }
  const MECData::DF_Position3D *pos() const {
    return GetPointer<const MECData::DF_Position3D *>(VT_POS);
  }
  const MECData::DF_ReferPosition *referPos() const {
    return GetPointer<const MECData::DF_ReferPosition *>(VT_REFERPOS);
  }
  uint16_t region() const {
    return GetField<uint16_t>(VT_REGION, 0);
  }
  const MECData::DF_NodeReferenceID *nodeId() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_NODEID);
  }
  uint8_t sectionId() const {
    return GetField<uint8_t>(VT_SECTIONID, 0);
  }
  int8_t laneId() const {
    return GetField<int8_t>(VT_LANEID, 0);
  }
  const MECData::DF_PositionConfidenceSet *accuracy() const {
    return GetPointer<const MECData::DF_PositionConfidenceSet *>(VT_ACCURACY);
  }
  MECData::DE_TransmissionState transmission() const {
    return static_cast<MECData::DE_TransmissionState>(GetField<int8_t>(VT_TRANSMISSION, 0));
  }
  uint16_t speed() const {
    return GetField<uint16_t>(VT_SPEED, 65535);
  }
  uint16_t heading() const {
    return GetField<uint16_t>(VT_HEADING, 65535);
  }
  int8_t angle() const {
    return GetField<int8_t>(VT_ANGLE, 0);
  }
  const MECData::DF_MotionConfidenceSet *motionCfd() const {
    return GetPointer<const MECData::DF_MotionConfidenceSet *>(VT_MOTIONCFD);
  }
  const MECData::DF_AccelerationSet4Way *accelSet() const {
    return GetPointer<const MECData::DF_AccelerationSet4Way *>(VT_ACCELSET);
  }
  const MECData::DF_BrakeSystemStatus *brakes() const {
    return GetPointer<const MECData::DF_BrakeSystemStatus *>(VT_BRAKES);
  }
  const MECData::DF_VehicleSize *size() const {
    return GetPointer<const MECData::DF_VehicleSize *>(VT_SIZE);
  }
  const MECData::DF_VehicleClassification *vehicleClass() const {
    return GetPointer<const MECData::DF_VehicleClassification *>(VT_VEHICLECLASS);
  }
  const MECData::DF_VehicleSafetyExtensions *safetyExt() const {
    return GetPointer<const MECData::DF_VehicleSafetyExtensions *>(VT_SAFETYEXT);
  }
  const MECData::DF_VehicleCharEx *vehCharEx() const {
    return GetPointer<const MECData::DF_VehicleCharEx *>(VT_VEHCHAREX);
  }
  const ::flatbuffers::String *section_ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_SECTION_EXT_ID);
  }
  const ::flatbuffers::String *lane_ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_LANE_EXT_ID);
  }
  const MECData::DF_DebugTimeRecords *time_records() const {
    return GetPointer<const MECData::DF_DebugTimeRecords *>(VT_TIME_RECORDS);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_PTCTYPE, 1) &&
           VerifyField<uint16_t>(verifier, VT_PTCID, 2) &&
           VerifyOffset(verifier, VT_OBUID) &&
           verifier.VerifyVector(obuId()) &&
           VerifyField<uint8_t>(verifier, VT_SOURCE, 1) &&
           VerifyOffsetRequired(verifier, VT_DEVICE) &&
           verifier.VerifyVector(device()) &&
           VerifyOffset(verifier, VT_PLATENO) &&
           verifier.VerifyString(plateNo()) &&
           VerifyField<uint32_t>(verifier, VT_MOY, 4) &&
           VerifyField<uint16_t>(verifier, VT_SECMARK, 2) &&
           VerifyField<int8_t>(verifier, VT_TIMECONFIDENCE, 1) &&
           VerifyOffsetRequired(verifier, VT_POS) &&
           verifier.VerifyTable(pos()) &&
           VerifyOffset(verifier, VT_REFERPOS) &&
           verifier.VerifyTable(referPos()) &&
           VerifyField<uint16_t>(verifier, VT_REGION, 2) &&
           VerifyOffset(verifier, VT_NODEID) &&
           verifier.VerifyTable(nodeId()) &&
           VerifyField<uint8_t>(verifier, VT_SECTIONID, 1) &&
           VerifyField<int8_t>(verifier, VT_LANEID, 1) &&
           VerifyOffset(verifier, VT_ACCURACY) &&
           verifier.VerifyTable(accuracy()) &&
           VerifyField<int8_t>(verifier, VT_TRANSMISSION, 1) &&
           VerifyField<uint16_t>(verifier, VT_SPEED, 2) &&
           VerifyField<uint16_t>(verifier, VT_HEADING, 2) &&
           VerifyField<int8_t>(verifier, VT_ANGLE, 1) &&
           VerifyOffset(verifier, VT_MOTIONCFD) &&
           verifier.VerifyTable(motionCfd()) &&
           VerifyOffset(verifier, VT_ACCELSET) &&
           verifier.VerifyTable(accelSet()) &&
           VerifyOffset(verifier, VT_BRAKES) &&
           verifier.VerifyTable(brakes()) &&
           VerifyOffset(verifier, VT_SIZE) &&
           verifier.VerifyTable(size()) &&
           VerifyOffset(verifier, VT_VEHICLECLASS) &&
           verifier.VerifyTable(vehicleClass()) &&
           VerifyOffset(verifier, VT_SAFETYEXT) &&
           verifier.VerifyTable(safetyExt()) &&
           VerifyOffset(verifier, VT_VEHCHAREX) &&
           verifier.VerifyTable(vehCharEx()) &&
           VerifyOffset(verifier, VT_SECTION_EXT_ID) &&
           verifier.VerifyString(section_ext_id()) &&
           VerifyOffset(verifier, VT_LANE_EXT_ID) &&
           verifier.VerifyString(lane_ext_id()) &&
           VerifyOffset(verifier, VT_TIME_RECORDS) &&
           verifier.VerifyTable(time_records()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct MSG_SafetyMessageBuilder {
  typedef MSG_SafetyMessage Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_ptcType(uint8_t ptcType) {
    fbb_.AddElement<uint8_t>(MSG_SafetyMessage::VT_PTCTYPE, ptcType, 255);
  }
  void add_ptcId(uint16_t ptcId) {
    fbb_.AddElement<uint16_t>(MSG_SafetyMessage::VT_PTCID, ptcId, 65535);
  }
  void add_obuId(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> obuId) {
    fbb_.AddOffset(MSG_SafetyMessage::VT_OBUID, obuId);
  }
  void add_source(MECData::DE_SourceType source) {
    fbb_.AddElement<uint8_t>(MSG_SafetyMessage::VT_SOURCE, static_cast<uint8_t>(source), 0);
  }
  void add_device(::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> device) {
    fbb_.AddOffset(MSG_SafetyMessage::VT_DEVICE, device);
  }
  void add_plateNo(::flatbuffers::Offset<::flatbuffers::String> plateNo) {
    fbb_.AddOffset(MSG_SafetyMessage::VT_PLATENO, plateNo);
  }
  void add_moy(uint32_t moy) {
    fbb_.AddElement<uint32_t>(MSG_SafetyMessage::VT_MOY, moy, 4294967295);
  }
  void add_secMark(uint16_t secMark) {
    fbb_.AddElement<uint16_t>(MSG_SafetyMessage::VT_SECMARK, secMark, 65535);
  }
  void add_timeConfidence(MECData::DE_TimeConfidence timeConfidence) {
    fbb_.AddElement<int8_t>(MSG_SafetyMessage::VT_TIMECONFIDENCE, static_cast<int8_t>(timeConfidence), 0);
  }
  void add_pos(::flatbuffers::Offset<MECData::DF_Position3D> pos) {
    fbb_.AddOffset(MSG_SafetyMessage::VT_POS, pos);
  }
  void add_referPos(::flatbuffers::Offset<MECData::DF_ReferPosition> referPos) {
    fbb_.AddOffset(MSG_SafetyMessage::VT_REFERPOS, referPos);
  }
  void add_region(uint16_t region) {
    fbb_.AddElement<uint16_t>(MSG_SafetyMessage::VT_REGION, region, 0);
  }
  void add_nodeId(::flatbuffers::Offset<MECData::DF_NodeReferenceID> nodeId) {
    fbb_.AddOffset(MSG_SafetyMessage::VT_NODEID, nodeId);
  }
  void add_sectionId(uint8_t sectionId) {
    fbb_.AddElement<uint8_t>(MSG_SafetyMessage::VT_SECTIONID, sectionId, 0);
  }
  void add_laneId(int8_t laneId) {
    fbb_.AddElement<int8_t>(MSG_SafetyMessage::VT_LANEID, laneId, 0);
  }
  void add_accuracy(::flatbuffers::Offset<MECData::DF_PositionConfidenceSet> accuracy) {
    fbb_.AddOffset(MSG_SafetyMessage::VT_ACCURACY, accuracy);
  }
  void add_transmission(MECData::DE_TransmissionState transmission) {
    fbb_.AddElement<int8_t>(MSG_SafetyMessage::VT_TRANSMISSION, static_cast<int8_t>(transmission), 0);
  }
  void add_speed(uint16_t speed) {
    fbb_.AddElement<uint16_t>(MSG_SafetyMessage::VT_SPEED, speed, 65535);
  }
  void add_heading(uint16_t heading) {
    fbb_.AddElement<uint16_t>(MSG_SafetyMessage::VT_HEADING, heading, 65535);
  }
  void add_angle(int8_t angle) {
    fbb_.AddElement<int8_t>(MSG_SafetyMessage::VT_ANGLE, angle, 0);
  }
  void add_motionCfd(::flatbuffers::Offset<MECData::DF_MotionConfidenceSet> motionCfd) {
    fbb_.AddOffset(MSG_SafetyMessage::VT_MOTIONCFD, motionCfd);
  }
  void add_accelSet(::flatbuffers::Offset<MECData::DF_AccelerationSet4Way> accelSet) {
    fbb_.AddOffset(MSG_SafetyMessage::VT_ACCELSET, accelSet);
  }
  void add_brakes(::flatbuffers::Offset<MECData::DF_BrakeSystemStatus> brakes) {
    fbb_.AddOffset(MSG_SafetyMessage::VT_BRAKES, brakes);
  }
  void add_size(::flatbuffers::Offset<MECData::DF_VehicleSize> size) {
    fbb_.AddOffset(MSG_SafetyMessage::VT_SIZE, size);
  }
  void add_vehicleClass(::flatbuffers::Offset<MECData::DF_VehicleClassification> vehicleClass) {
    fbb_.AddOffset(MSG_SafetyMessage::VT_VEHICLECLASS, vehicleClass);
  }
  void add_safetyExt(::flatbuffers::Offset<MECData::DF_VehicleSafetyExtensions> safetyExt) {
    fbb_.AddOffset(MSG_SafetyMessage::VT_SAFETYEXT, safetyExt);
  }
  void add_vehCharEx(::flatbuffers::Offset<MECData::DF_VehicleCharEx> vehCharEx) {
    fbb_.AddOffset(MSG_SafetyMessage::VT_VEHCHAREX, vehCharEx);
  }
  void add_section_ext_id(::flatbuffers::Offset<::flatbuffers::String> section_ext_id) {
    fbb_.AddOffset(MSG_SafetyMessage::VT_SECTION_EXT_ID, section_ext_id);
  }
  void add_lane_ext_id(::flatbuffers::Offset<::flatbuffers::String> lane_ext_id) {
    fbb_.AddOffset(MSG_SafetyMessage::VT_LANE_EXT_ID, lane_ext_id);
  }
  void add_time_records(::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records) {
    fbb_.AddOffset(MSG_SafetyMessage::VT_TIME_RECORDS, time_records);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_SafetyMessage::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_SafetyMessageBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_SafetyMessage> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_SafetyMessage>(end);
    fbb_.Required(o, MSG_SafetyMessage::VT_DEVICE);
    fbb_.Required(o, MSG_SafetyMessage::VT_POS);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_SafetyMessage> CreateMSG_SafetyMessage(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t ptcType = 255,
    uint16_t ptcId = 65535,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> obuId = 0,
    MECData::DE_SourceType source = MECData::DE_SourceType_unknown,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> device = 0,
    ::flatbuffers::Offset<::flatbuffers::String> plateNo = 0,
    uint32_t moy = 4294967295,
    uint16_t secMark = 65535,
    MECData::DE_TimeConfidence timeConfidence = MECData::DE_TimeConfidence_unavailable,
    ::flatbuffers::Offset<MECData::DF_Position3D> pos = 0,
    ::flatbuffers::Offset<MECData::DF_ReferPosition> referPos = 0,
    uint16_t region = 0,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> nodeId = 0,
    uint8_t sectionId = 0,
    int8_t laneId = 0,
    ::flatbuffers::Offset<MECData::DF_PositionConfidenceSet> accuracy = 0,
    MECData::DE_TransmissionState transmission = MECData::DE_TransmissionState_neutral,
    uint16_t speed = 65535,
    uint16_t heading = 65535,
    int8_t angle = 0,
    ::flatbuffers::Offset<MECData::DF_MotionConfidenceSet> motionCfd = 0,
    ::flatbuffers::Offset<MECData::DF_AccelerationSet4Way> accelSet = 0,
    ::flatbuffers::Offset<MECData::DF_BrakeSystemStatus> brakes = 0,
    ::flatbuffers::Offset<MECData::DF_VehicleSize> size = 0,
    ::flatbuffers::Offset<MECData::DF_VehicleClassification> vehicleClass = 0,
    ::flatbuffers::Offset<MECData::DF_VehicleSafetyExtensions> safetyExt = 0,
    ::flatbuffers::Offset<MECData::DF_VehicleCharEx> vehCharEx = 0,
    ::flatbuffers::Offset<::flatbuffers::String> section_ext_id = 0,
    ::flatbuffers::Offset<::flatbuffers::String> lane_ext_id = 0,
    ::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records = 0,
    int64_t msg_id = 0) {
  MSG_SafetyMessageBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_time_records(time_records);
  builder_.add_lane_ext_id(lane_ext_id);
  builder_.add_section_ext_id(section_ext_id);
  builder_.add_vehCharEx(vehCharEx);
  builder_.add_safetyExt(safetyExt);
  builder_.add_vehicleClass(vehicleClass);
  builder_.add_size(size);
  builder_.add_brakes(brakes);
  builder_.add_accelSet(accelSet);
  builder_.add_motionCfd(motionCfd);
  builder_.add_accuracy(accuracy);
  builder_.add_nodeId(nodeId);
  builder_.add_referPos(referPos);
  builder_.add_pos(pos);
  builder_.add_moy(moy);
  builder_.add_plateNo(plateNo);
  builder_.add_device(device);
  builder_.add_obuId(obuId);
  builder_.add_heading(heading);
  builder_.add_speed(speed);
  builder_.add_region(region);
  builder_.add_secMark(secMark);
  builder_.add_ptcId(ptcId);
  builder_.add_angle(angle);
  builder_.add_transmission(transmission);
  builder_.add_laneId(laneId);
  builder_.add_sectionId(sectionId);
  builder_.add_timeConfidence(timeConfidence);
  builder_.add_source(source);
  builder_.add_ptcType(ptcType);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_SafetyMessage> CreateMSG_SafetyMessageDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t ptcType = 255,
    uint16_t ptcId = 65535,
    const std::vector<uint8_t> *obuId = nullptr,
    MECData::DE_SourceType source = MECData::DE_SourceType_unknown,
    const std::vector<uint16_t> *device = nullptr,
    const char *plateNo = nullptr,
    uint32_t moy = 4294967295,
    uint16_t secMark = 65535,
    MECData::DE_TimeConfidence timeConfidence = MECData::DE_TimeConfidence_unavailable,
    ::flatbuffers::Offset<MECData::DF_Position3D> pos = 0,
    ::flatbuffers::Offset<MECData::DF_ReferPosition> referPos = 0,
    uint16_t region = 0,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> nodeId = 0,
    uint8_t sectionId = 0,
    int8_t laneId = 0,
    ::flatbuffers::Offset<MECData::DF_PositionConfidenceSet> accuracy = 0,
    MECData::DE_TransmissionState transmission = MECData::DE_TransmissionState_neutral,
    uint16_t speed = 65535,
    uint16_t heading = 65535,
    int8_t angle = 0,
    ::flatbuffers::Offset<MECData::DF_MotionConfidenceSet> motionCfd = 0,
    ::flatbuffers::Offset<MECData::DF_AccelerationSet4Way> accelSet = 0,
    ::flatbuffers::Offset<MECData::DF_BrakeSystemStatus> brakes = 0,
    ::flatbuffers::Offset<MECData::DF_VehicleSize> size = 0,
    ::flatbuffers::Offset<MECData::DF_VehicleClassification> vehicleClass = 0,
    ::flatbuffers::Offset<MECData::DF_VehicleSafetyExtensions> safetyExt = 0,
    ::flatbuffers::Offset<MECData::DF_VehicleCharEx> vehCharEx = 0,
    const char *section_ext_id = nullptr,
    const char *lane_ext_id = nullptr,
    ::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records = 0,
    int64_t msg_id = 0) {
  auto obuId__ = obuId ? _fbb.CreateVector<uint8_t>(*obuId) : 0;
  auto device__ = device ? _fbb.CreateVector<uint16_t>(*device) : 0;
  auto plateNo__ = plateNo ? _fbb.CreateString(plateNo) : 0;
  auto section_ext_id__ = section_ext_id ? _fbb.CreateString(section_ext_id) : 0;
  auto lane_ext_id__ = lane_ext_id ? _fbb.CreateString(lane_ext_id) : 0;
  return MECData::CreateMSG_SafetyMessage(
      _fbb,
      ptcType,
      ptcId,
      obuId__,
      source,
      device__,
      plateNo__,
      moy,
      secMark,
      timeConfidence,
      pos,
      referPos,
      region,
      nodeId,
      sectionId,
      laneId,
      accuracy,
      transmission,
      speed,
      heading,
      angle,
      motionCfd,
      accelSet,
      brakes,
      size,
      vehicleClass,
      safetyExt,
      vehCharEx,
      section_ext_id__,
      lane_ext_id__,
      time_records,
      msg_id);
}

inline const MECData::MSG_SafetyMessage *GetMSG_SafetyMessage(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_SafetyMessage>(buf);
}

inline const MECData::MSG_SafetyMessage *GetSizePrefixedMSG_SafetyMessage(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_SafetyMessage>(buf);
}

inline bool VerifyMSG_SafetyMessageBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_SafetyMessage>(nullptr);
}

inline bool VerifySizePrefixedMSG_SafetyMessageBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_SafetyMessage>(nullptr);
}

inline void FinishMSG_SafetyMessageBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_SafetyMessage> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_SafetyMessageBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_SafetyMessage> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_SAFETYMESSAGE_MECDATA_H_
