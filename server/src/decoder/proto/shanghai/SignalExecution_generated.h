// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_SIGNALEXECUTION_MECDATA_H_
#define FLATBUFFERS_GENERATED_SIGNALEXECUTION_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "NodeReferenceID_generated.h"
#include "SignalControlMode_generated.h"

namespace MECData {

struct DF_PhasicExec;
struct DF_PhasicExecBuilder;

struct MSG_SignalExecution;
struct MSG_SignalExecutionBuilder;

struct DF_PhasicExec FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_PhasicExecBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PHASIC_ID = 4,
    VT_ORDER = 6,
    VT_MOVEMENTS = 8,
    VT_GREEN = 10,
    VT_YELLOW = 12,
    VT_ALLRED = 14
  };
  int32_t phasic_id() const {
    return GetField<int32_t>(VT_PHASIC_ID, 0);
  }
  uint8_t order() const {
    return GetField<uint8_t>(VT_ORDER, 255);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *movements() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *>(VT_MOVEMENTS);
  }
  int16_t green() const {
    return GetField<int16_t>(VT_GREEN, 0);
  }
  int16_t yellow() const {
    return GetField<int16_t>(VT_YELLOW, 0);
  }
  int16_t allred() const {
    return GetField<int16_t>(VT_ALLRED, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_PHASIC_ID, 4) &&
           VerifyField<uint8_t>(verifier, VT_ORDER, 1) &&
           VerifyOffsetRequired(verifier, VT_MOVEMENTS) &&
           verifier.VerifyVector(movements()) &&
           verifier.VerifyVectorOfStrings(movements()) &&
           VerifyField<int16_t>(verifier, VT_GREEN, 2) &&
           VerifyField<int16_t>(verifier, VT_YELLOW, 2) &&
           VerifyField<int16_t>(verifier, VT_ALLRED, 2) &&
           verifier.EndTable();
  }
};

struct DF_PhasicExecBuilder {
  typedef DF_PhasicExec Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_phasic_id(int32_t phasic_id) {
    fbb_.AddElement<int32_t>(DF_PhasicExec::VT_PHASIC_ID, phasic_id, 0);
  }
  void add_order(uint8_t order) {
    fbb_.AddElement<uint8_t>(DF_PhasicExec::VT_ORDER, order, 255);
  }
  void add_movements(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> movements) {
    fbb_.AddOffset(DF_PhasicExec::VT_MOVEMENTS, movements);
  }
  void add_green(int16_t green) {
    fbb_.AddElement<int16_t>(DF_PhasicExec::VT_GREEN, green, 0);
  }
  void add_yellow(int16_t yellow) {
    fbb_.AddElement<int16_t>(DF_PhasicExec::VT_YELLOW, yellow, 0);
  }
  void add_allred(int16_t allred) {
    fbb_.AddElement<int16_t>(DF_PhasicExec::VT_ALLRED, allred, 0);
  }
  explicit DF_PhasicExecBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_PhasicExec> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_PhasicExec>(end);
    fbb_.Required(o, DF_PhasicExec::VT_MOVEMENTS);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_PhasicExec> CreateDF_PhasicExec(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t phasic_id = 0,
    uint8_t order = 255,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> movements = 0,
    int16_t green = 0,
    int16_t yellow = 0,
    int16_t allred = 0) {
  DF_PhasicExecBuilder builder_(_fbb);
  builder_.add_movements(movements);
  builder_.add_phasic_id(phasic_id);
  builder_.add_allred(allred);
  builder_.add_yellow(yellow);
  builder_.add_green(green);
  builder_.add_order(order);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_PhasicExec> CreateDF_PhasicExecDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t phasic_id = 0,
    uint8_t order = 255,
    const std::vector<::flatbuffers::Offset<::flatbuffers::String>> *movements = nullptr,
    int16_t green = 0,
    int16_t yellow = 0,
    int16_t allred = 0) {
  auto movements__ = movements ? _fbb.CreateVector<::flatbuffers::Offset<::flatbuffers::String>>(*movements) : 0;
  return MECData::CreateDF_PhasicExec(
      _fbb,
      phasic_id,
      order,
      movements__,
      green,
      yellow,
      allred);
}

struct MSG_SignalExecution FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_SignalExecutionBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NODE_ID = 4,
    VT_SEQUENCE = 6,
    VT_CONTROL_MODE = 8,
    VT_CYCLE = 10,
    VT_BASE_SIGNAL_SCHEME_ID = 12,
    VT_START_TIME = 14,
    VT_PHASES = 16,
    VT_MSG_ID = 18
  };
  const MECData::DF_NodeReferenceID *node_id() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_NODE_ID);
  }
  uint32_t sequence() const {
    return GetField<uint32_t>(VT_SEQUENCE, 0);
  }
  MECData::DE_SignalControlMode control_mode() const {
    return static_cast<MECData::DE_SignalControlMode>(GetField<uint8_t>(VT_CONTROL_MODE, 0));
  }
  int16_t cycle() const {
    return GetField<int16_t>(VT_CYCLE, 0);
  }
  int32_t base_signal_scheme_id() const {
    return GetField<int32_t>(VT_BASE_SIGNAL_SCHEME_ID, 0);
  }
  int64_t start_time() const {
    return GetField<int64_t>(VT_START_TIME, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhasicExec>> *phases() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhasicExec>> *>(VT_PHASES);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_NODE_ID) &&
           verifier.VerifyTable(node_id()) &&
           VerifyField<uint32_t>(verifier, VT_SEQUENCE, 4) &&
           VerifyField<uint8_t>(verifier, VT_CONTROL_MODE, 1) &&
           VerifyField<int16_t>(verifier, VT_CYCLE, 2) &&
           VerifyField<int32_t>(verifier, VT_BASE_SIGNAL_SCHEME_ID, 4) &&
           VerifyField<int64_t>(verifier, VT_START_TIME, 8) &&
           VerifyOffset(verifier, VT_PHASES) &&
           verifier.VerifyVector(phases()) &&
           verifier.VerifyVectorOfTables(phases()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct MSG_SignalExecutionBuilder {
  typedef MSG_SignalExecution Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_node_id(::flatbuffers::Offset<MECData::DF_NodeReferenceID> node_id) {
    fbb_.AddOffset(MSG_SignalExecution::VT_NODE_ID, node_id);
  }
  void add_sequence(uint32_t sequence) {
    fbb_.AddElement<uint32_t>(MSG_SignalExecution::VT_SEQUENCE, sequence, 0);
  }
  void add_control_mode(MECData::DE_SignalControlMode control_mode) {
    fbb_.AddElement<uint8_t>(MSG_SignalExecution::VT_CONTROL_MODE, static_cast<uint8_t>(control_mode), 0);
  }
  void add_cycle(int16_t cycle) {
    fbb_.AddElement<int16_t>(MSG_SignalExecution::VT_CYCLE, cycle, 0);
  }
  void add_base_signal_scheme_id(int32_t base_signal_scheme_id) {
    fbb_.AddElement<int32_t>(MSG_SignalExecution::VT_BASE_SIGNAL_SCHEME_ID, base_signal_scheme_id, 0);
  }
  void add_start_time(int64_t start_time) {
    fbb_.AddElement<int64_t>(MSG_SignalExecution::VT_START_TIME, start_time, 0);
  }
  void add_phases(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhasicExec>>> phases) {
    fbb_.AddOffset(MSG_SignalExecution::VT_PHASES, phases);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_SignalExecution::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_SignalExecutionBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_SignalExecution> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_SignalExecution>(end);
    fbb_.Required(o, MSG_SignalExecution::VT_NODE_ID);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_SignalExecution> CreateMSG_SignalExecution(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> node_id = 0,
    uint32_t sequence = 0,
    MECData::DE_SignalControlMode control_mode = MECData::DE_SignalControlMode_CYCLIC_FIXED,
    int16_t cycle = 0,
    int32_t base_signal_scheme_id = 0,
    int64_t start_time = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhasicExec>>> phases = 0,
    int64_t msg_id = 0) {
  MSG_SignalExecutionBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_start_time(start_time);
  builder_.add_phases(phases);
  builder_.add_base_signal_scheme_id(base_signal_scheme_id);
  builder_.add_sequence(sequence);
  builder_.add_node_id(node_id);
  builder_.add_cycle(cycle);
  builder_.add_control_mode(control_mode);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_SignalExecution> CreateMSG_SignalExecutionDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> node_id = 0,
    uint32_t sequence = 0,
    MECData::DE_SignalControlMode control_mode = MECData::DE_SignalControlMode_CYCLIC_FIXED,
    int16_t cycle = 0,
    int32_t base_signal_scheme_id = 0,
    int64_t start_time = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_PhasicExec>> *phases = nullptr,
    int64_t msg_id = 0) {
  auto phases__ = phases ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_PhasicExec>>(*phases) : 0;
  return MECData::CreateMSG_SignalExecution(
      _fbb,
      node_id,
      sequence,
      control_mode,
      cycle,
      base_signal_scheme_id,
      start_time,
      phases__,
      msg_id);
}

inline const MECData::MSG_SignalExecution *GetMSG_SignalExecution(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_SignalExecution>(buf);
}

inline const MECData::MSG_SignalExecution *GetSizePrefixedMSG_SignalExecution(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_SignalExecution>(buf);
}

inline bool VerifyMSG_SignalExecutionBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_SignalExecution>(nullptr);
}

inline bool VerifySizePrefixedMSG_SignalExecutionBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_SignalExecution>(nullptr);
}

inline void FinishMSG_SignalExecutionBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_SignalExecution> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_SignalExecutionBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_SignalExecution> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_SIGNALEXECUTION_MECDATA_H_
