// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_VOLUMELANE_MECDATA_H_
#define FLATBUFFERS_GENERATED_VOLUMELANE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct MSG_VolumeLane;
struct MSG_VolumeLaneBuilder;

struct MSG_VolumeLane FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_VolumeLaneBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_DEVICEID = 4,
    VT_DETECTAERAID = 6,
    VT_MOY = 8,
    VT_SECMARK = 10,
    VT_CYCLE = 12,
    VT_VOLUME = 14
  };
  uint16_t deviceId() const {
    return GetField<uint16_t>(VT_DEVICEID, 0);
  }
  int32_t detectAeraId() const {
    return GetField<int32_t>(VT_DETECTAERAID, 0);
  }
  uint32_t moy() const {
    return GetField<uint32_t>(VT_MOY, 4294967295);
  }
  uint16_t secMark() const {
    return GetField<uint16_t>(VT_SECMARK, 65535);
  }
  uint16_t cycle() const {
    return GetField<uint16_t>(VT_CYCLE, 65535);
  }
  uint16_t volume() const {
    return GetField<uint16_t>(VT_VOLUME, 65535);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_DEVICEID, 2) &&
           VerifyField<int32_t>(verifier, VT_DETECTAERAID, 4) &&
           VerifyField<uint32_t>(verifier, VT_MOY, 4) &&
           VerifyField<uint16_t>(verifier, VT_SECMARK, 2) &&
           VerifyField<uint16_t>(verifier, VT_CYCLE, 2) &&
           VerifyField<uint16_t>(verifier, VT_VOLUME, 2) &&
           verifier.EndTable();
  }
};

struct MSG_VolumeLaneBuilder {
  typedef MSG_VolumeLane Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_deviceId(uint16_t deviceId) {
    fbb_.AddElement<uint16_t>(MSG_VolumeLane::VT_DEVICEID, deviceId, 0);
  }
  void add_detectAeraId(int32_t detectAeraId) {
    fbb_.AddElement<int32_t>(MSG_VolumeLane::VT_DETECTAERAID, detectAeraId, 0);
  }
  void add_moy(uint32_t moy) {
    fbb_.AddElement<uint32_t>(MSG_VolumeLane::VT_MOY, moy, 4294967295);
  }
  void add_secMark(uint16_t secMark) {
    fbb_.AddElement<uint16_t>(MSG_VolumeLane::VT_SECMARK, secMark, 65535);
  }
  void add_cycle(uint16_t cycle) {
    fbb_.AddElement<uint16_t>(MSG_VolumeLane::VT_CYCLE, cycle, 65535);
  }
  void add_volume(uint16_t volume) {
    fbb_.AddElement<uint16_t>(MSG_VolumeLane::VT_VOLUME, volume, 65535);
  }
  explicit MSG_VolumeLaneBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_VolumeLane> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_VolumeLane>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_VolumeLane> CreateMSG_VolumeLane(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t deviceId = 0,
    int32_t detectAeraId = 0,
    uint32_t moy = 4294967295,
    uint16_t secMark = 65535,
    uint16_t cycle = 65535,
    uint16_t volume = 65535) {
  MSG_VolumeLaneBuilder builder_(_fbb);
  builder_.add_moy(moy);
  builder_.add_detectAeraId(detectAeraId);
  builder_.add_volume(volume);
  builder_.add_cycle(cycle);
  builder_.add_secMark(secMark);
  builder_.add_deviceId(deviceId);
  return builder_.Finish();
}

inline const MECData::MSG_VolumeLane *GetMSG_VolumeLane(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_VolumeLane>(buf);
}

inline const MECData::MSG_VolumeLane *GetSizePrefixedMSG_VolumeLane(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_VolumeLane>(buf);
}

inline bool VerifyMSG_VolumeLaneBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_VolumeLane>(nullptr);
}

inline bool VerifySizePrefixedMSG_VolumeLaneBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_VolumeLane>(nullptr);
}

inline void FinishMSG_VolumeLaneBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_VolumeLane> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_VolumeLaneBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_VolumeLane> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_VOLUMELANE_MECDATA_H_
