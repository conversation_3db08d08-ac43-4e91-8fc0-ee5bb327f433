// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_REFERENCEPATH_MECDATA_H_
#define FLATBUFFERS_GENERATED_REFERENCEPATH_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "PositionOffsetLLV_generated.h"

namespace MECData {

struct DF_ReferencePath;
struct DF_ReferencePathBuilder;

struct DF_ReferencePath FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ReferencePathBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ACTIVEPATH = 4,
    VT_PATHRADIUS = 6
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PositionOffsetLLV>> *activePath() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PositionOffsetLLV>> *>(VT_ACTIVEPATH);
  }
  uint16_t pathRadius() const {
    return GetField<uint16_t>(VT_PATHRADIUS, 65535);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_ACTIVEPATH) &&
           verifier.VerifyVector(activePath()) &&
           verifier.VerifyVectorOfTables(activePath()) &&
           VerifyField<uint16_t>(verifier, VT_PATHRADIUS, 2) &&
           verifier.EndTable();
  }
};

struct DF_ReferencePathBuilder {
  typedef DF_ReferencePath Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_activePath(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PositionOffsetLLV>>> activePath) {
    fbb_.AddOffset(DF_ReferencePath::VT_ACTIVEPATH, activePath);
  }
  void add_pathRadius(uint16_t pathRadius) {
    fbb_.AddElement<uint16_t>(DF_ReferencePath::VT_PATHRADIUS, pathRadius, 65535);
  }
  explicit DF_ReferencePathBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ReferencePath> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ReferencePath>(end);
    fbb_.Required(o, DF_ReferencePath::VT_ACTIVEPATH);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ReferencePath> CreateDF_ReferencePath(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PositionOffsetLLV>>> activePath = 0,
    uint16_t pathRadius = 65535) {
  DF_ReferencePathBuilder builder_(_fbb);
  builder_.add_activePath(activePath);
  builder_.add_pathRadius(pathRadius);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ReferencePath> CreateDF_ReferencePathDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<MECData::DF_PositionOffsetLLV>> *activePath = nullptr,
    uint16_t pathRadius = 65535) {
  auto activePath__ = activePath ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_PositionOffsetLLV>>(*activePath) : 0;
  return MECData::CreateDF_ReferencePath(
      _fbb,
      activePath__,
      pathRadius);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_REFERENCEPATH_MECDATA_H_
