// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_SECTION_MECDATA_H_
#define FLATBUFFERS_GENERATED_SECTION_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "LaneEx_generated.h"

namespace MECData {

struct DF_Section;
struct DF_SectionBuilder;

struct DF_Section FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SectionBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SECID = 4,
    VT_LANES = 6,
    VT_EXT_ID = 8
  };
  uint8_t secID() const {
    return GetField<uint8_t>(VT_SECID, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_LaneEx>> *lanes() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_LaneEx>> *>(VT_LANES);
  }
  const ::flatbuffers::String *ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_EXT_ID);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_SECID, 1) &&
           VerifyOffset(verifier, VT_LANES) &&
           verifier.VerifyVector(lanes()) &&
           verifier.VerifyVectorOfTables(lanes()) &&
           VerifyOffset(verifier, VT_EXT_ID) &&
           verifier.VerifyString(ext_id()) &&
           verifier.EndTable();
  }
};

struct DF_SectionBuilder {
  typedef DF_Section Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_secID(uint8_t secID) {
    fbb_.AddElement<uint8_t>(DF_Section::VT_SECID, secID, 0);
  }
  void add_lanes(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_LaneEx>>> lanes) {
    fbb_.AddOffset(DF_Section::VT_LANES, lanes);
  }
  void add_ext_id(::flatbuffers::Offset<::flatbuffers::String> ext_id) {
    fbb_.AddOffset(DF_Section::VT_EXT_ID, ext_id);
  }
  explicit DF_SectionBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_Section> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_Section>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_Section> CreateDF_Section(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t secID = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_LaneEx>>> lanes = 0,
    ::flatbuffers::Offset<::flatbuffers::String> ext_id = 0) {
  DF_SectionBuilder builder_(_fbb);
  builder_.add_ext_id(ext_id);
  builder_.add_lanes(lanes);
  builder_.add_secID(secID);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_Section> CreateDF_SectionDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t secID = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_LaneEx>> *lanes = nullptr,
    const char *ext_id = nullptr) {
  auto lanes__ = lanes ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_LaneEx>>(*lanes) : 0;
  auto ext_id__ = ext_id ? _fbb.CreateString(ext_id) : 0;
  return MECData::CreateDF_Section(
      _fbb,
      secID,
      lanes__,
      ext_id__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_SECTION_MECDATA_H_
