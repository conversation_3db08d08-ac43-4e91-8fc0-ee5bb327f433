// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_TRAJECTORYPOINTS_MECDATA_H_
#define FLATBUFFERS_GENERATED_TRAJECTORYPOINTS_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "NodeReferenceID_generated.h"
#include "VehicleCharEx_generated.h"
#include "VehicleClassification_generated.h"

namespace MECData {

struct DF_SingleTrajectoryPoint;
struct DF_SingleTrajectoryPointBuilder;

struct DF_TrajectoryNodeExtension;
struct DF_TrajectoryNodeExtensionBuilder;

struct MSG_TrajectoryPoints;
struct MSG_TrajectoryPointsBuilder;

struct DF_SingleTrajectoryPoint FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SingleTrajectoryPointBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TIMESTAMP = 4,
    VT_LAT = 6,
    VT_LON = 8,
    VT_SPEED = 10,
    VT_DIRECTION_ANGLE = 12,
    VT_SECTION_ID = 14,
    VT_LANE_REF_ID = 16,
    VT_DIS_TO_STOPLINE = 18
  };
  int64_t timestamp() const {
    return GetField<int64_t>(VT_TIMESTAMP, 0);
  }
  int32_t lat() const {
    return GetField<int32_t>(VT_LAT, 0);
  }
  int32_t lon() const {
    return GetField<int32_t>(VT_LON, 0);
  }
  int32_t speed() const {
    return GetField<int32_t>(VT_SPEED, 0);
  }
  uint8_t direction_angle() const {
    return GetField<uint8_t>(VT_DIRECTION_ANGLE, 0);
  }
  uint8_t section_id() const {
    return GetField<uint8_t>(VT_SECTION_ID, 0);
  }
  int8_t lane_ref_id() const {
    return GetField<int8_t>(VT_LANE_REF_ID, 0);
  }
  uint32_t dis_to_stopline() const {
    return GetField<uint32_t>(VT_DIS_TO_STOPLINE, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int64_t>(verifier, VT_TIMESTAMP, 8) &&
           VerifyField<int32_t>(verifier, VT_LAT, 4) &&
           VerifyField<int32_t>(verifier, VT_LON, 4) &&
           VerifyField<int32_t>(verifier, VT_SPEED, 4) &&
           VerifyField<uint8_t>(verifier, VT_DIRECTION_ANGLE, 1) &&
           VerifyField<uint8_t>(verifier, VT_SECTION_ID, 1) &&
           VerifyField<int8_t>(verifier, VT_LANE_REF_ID, 1) &&
           VerifyField<uint32_t>(verifier, VT_DIS_TO_STOPLINE, 4) &&
           verifier.EndTable();
  }
};

struct DF_SingleTrajectoryPointBuilder {
  typedef DF_SingleTrajectoryPoint Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_timestamp(int64_t timestamp) {
    fbb_.AddElement<int64_t>(DF_SingleTrajectoryPoint::VT_TIMESTAMP, timestamp, 0);
  }
  void add_lat(int32_t lat) {
    fbb_.AddElement<int32_t>(DF_SingleTrajectoryPoint::VT_LAT, lat, 0);
  }
  void add_lon(int32_t lon) {
    fbb_.AddElement<int32_t>(DF_SingleTrajectoryPoint::VT_LON, lon, 0);
  }
  void add_speed(int32_t speed) {
    fbb_.AddElement<int32_t>(DF_SingleTrajectoryPoint::VT_SPEED, speed, 0);
  }
  void add_direction_angle(uint8_t direction_angle) {
    fbb_.AddElement<uint8_t>(DF_SingleTrajectoryPoint::VT_DIRECTION_ANGLE, direction_angle, 0);
  }
  void add_section_id(uint8_t section_id) {
    fbb_.AddElement<uint8_t>(DF_SingleTrajectoryPoint::VT_SECTION_ID, section_id, 0);
  }
  void add_lane_ref_id(int8_t lane_ref_id) {
    fbb_.AddElement<int8_t>(DF_SingleTrajectoryPoint::VT_LANE_REF_ID, lane_ref_id, 0);
  }
  void add_dis_to_stopline(uint32_t dis_to_stopline) {
    fbb_.AddElement<uint32_t>(DF_SingleTrajectoryPoint::VT_DIS_TO_STOPLINE, dis_to_stopline, 0);
  }
  explicit DF_SingleTrajectoryPointBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_SingleTrajectoryPoint> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_SingleTrajectoryPoint>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_SingleTrajectoryPoint> CreateDF_SingleTrajectoryPoint(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int64_t timestamp = 0,
    int32_t lat = 0,
    int32_t lon = 0,
    int32_t speed = 0,
    uint8_t direction_angle = 0,
    uint8_t section_id = 0,
    int8_t lane_ref_id = 0,
    uint32_t dis_to_stopline = 0) {
  DF_SingleTrajectoryPointBuilder builder_(_fbb);
  builder_.add_timestamp(timestamp);
  builder_.add_dis_to_stopline(dis_to_stopline);
  builder_.add_speed(speed);
  builder_.add_lon(lon);
  builder_.add_lat(lat);
  builder_.add_lane_ref_id(lane_ref_id);
  builder_.add_section_id(section_id);
  builder_.add_direction_angle(direction_angle);
  return builder_.Finish();
}

struct DF_TrajectoryNodeExtension FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_TrajectoryNodeExtensionBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NODE = 4,
    VT_MOVEMENT_EXT_ID = 6,
    VT_LINK_EXT_IDS = 8
  };
  const MECData::DF_NodeReferenceID *node() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_NODE);
  }
  const ::flatbuffers::String *movement_ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_MOVEMENT_EXT_ID);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *link_ext_ids() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *>(VT_LINK_EXT_IDS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_NODE) &&
           verifier.VerifyTable(node()) &&
           VerifyOffsetRequired(verifier, VT_MOVEMENT_EXT_ID) &&
           verifier.VerifyString(movement_ext_id()) &&
           VerifyOffsetRequired(verifier, VT_LINK_EXT_IDS) &&
           verifier.VerifyVector(link_ext_ids()) &&
           verifier.VerifyVectorOfStrings(link_ext_ids()) &&
           verifier.EndTable();
  }
};

struct DF_TrajectoryNodeExtensionBuilder {
  typedef DF_TrajectoryNodeExtension Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_node(::flatbuffers::Offset<MECData::DF_NodeReferenceID> node) {
    fbb_.AddOffset(DF_TrajectoryNodeExtension::VT_NODE, node);
  }
  void add_movement_ext_id(::flatbuffers::Offset<::flatbuffers::String> movement_ext_id) {
    fbb_.AddOffset(DF_TrajectoryNodeExtension::VT_MOVEMENT_EXT_ID, movement_ext_id);
  }
  void add_link_ext_ids(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> link_ext_ids) {
    fbb_.AddOffset(DF_TrajectoryNodeExtension::VT_LINK_EXT_IDS, link_ext_ids);
  }
  explicit DF_TrajectoryNodeExtensionBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_TrajectoryNodeExtension> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_TrajectoryNodeExtension>(end);
    fbb_.Required(o, DF_TrajectoryNodeExtension::VT_NODE);
    fbb_.Required(o, DF_TrajectoryNodeExtension::VT_MOVEMENT_EXT_ID);
    fbb_.Required(o, DF_TrajectoryNodeExtension::VT_LINK_EXT_IDS);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_TrajectoryNodeExtension> CreateDF_TrajectoryNodeExtension(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> node = 0,
    ::flatbuffers::Offset<::flatbuffers::String> movement_ext_id = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> link_ext_ids = 0) {
  DF_TrajectoryNodeExtensionBuilder builder_(_fbb);
  builder_.add_link_ext_ids(link_ext_ids);
  builder_.add_movement_ext_id(movement_ext_id);
  builder_.add_node(node);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_TrajectoryNodeExtension> CreateDF_TrajectoryNodeExtensionDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> node = 0,
    const char *movement_ext_id = nullptr,
    const std::vector<::flatbuffers::Offset<::flatbuffers::String>> *link_ext_ids = nullptr) {
  auto movement_ext_id__ = movement_ext_id ? _fbb.CreateString(movement_ext_id) : 0;
  auto link_ext_ids__ = link_ext_ids ? _fbb.CreateVector<::flatbuffers::Offset<::flatbuffers::String>>(*link_ext_ids) : 0;
  return MECData::CreateDF_TrajectoryNodeExtension(
      _fbb,
      node,
      movement_ext_id__,
      link_ext_ids__);
}

struct MSG_TrajectoryPoints FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_TrajectoryPointsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MEC_ID = 4,
    VT_PTC_TYPE = 6,
    VT_PTC_ID = 8,
    VT_OBU_ID = 10,
    VT_POINTS = 12,
    VT_CE_TOKEN = 14,
    VT_VEH_TYPE = 16,
    VT_VEH_CHAR = 18,
    VT_NODE_EXT = 20,
    VT_MSG_ID = 22
  };
  const ::flatbuffers::String *mec_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_MEC_ID);
  }
  uint8_t ptc_type() const {
    return GetField<uint8_t>(VT_PTC_TYPE, 255);
  }
  uint16_t ptc_id() const {
    return GetField<uint16_t>(VT_PTC_ID, 65535);
  }
  const ::flatbuffers::Vector<uint8_t> *obu_id() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_OBU_ID);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SingleTrajectoryPoint>> *points() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SingleTrajectoryPoint>> *>(VT_POINTS);
  }
  const ::flatbuffers::String *ce_token() const {
    return GetPointer<const ::flatbuffers::String *>(VT_CE_TOKEN);
  }
  const MECData::DF_VehicleClassification *veh_type() const {
    return GetPointer<const MECData::DF_VehicleClassification *>(VT_VEH_TYPE);
  }
  const MECData::DF_VehicleCharEx *veh_char() const {
    return GetPointer<const MECData::DF_VehicleCharEx *>(VT_VEH_CHAR);
  }
  const MECData::DF_TrajectoryNodeExtension *node_ext() const {
    return GetPointer<const MECData::DF_TrajectoryNodeExtension *>(VT_NODE_EXT);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_MEC_ID) &&
           verifier.VerifyString(mec_id()) &&
           VerifyField<uint8_t>(verifier, VT_PTC_TYPE, 1) &&
           VerifyField<uint16_t>(verifier, VT_PTC_ID, 2) &&
           VerifyOffset(verifier, VT_OBU_ID) &&
           verifier.VerifyVector(obu_id()) &&
           VerifyOffset(verifier, VT_POINTS) &&
           verifier.VerifyVector(points()) &&
           verifier.VerifyVectorOfTables(points()) &&
           VerifyOffset(verifier, VT_CE_TOKEN) &&
           verifier.VerifyString(ce_token()) &&
           VerifyOffset(verifier, VT_VEH_TYPE) &&
           verifier.VerifyTable(veh_type()) &&
           VerifyOffset(verifier, VT_VEH_CHAR) &&
           verifier.VerifyTable(veh_char()) &&
           VerifyOffset(verifier, VT_NODE_EXT) &&
           verifier.VerifyTable(node_ext()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct MSG_TrajectoryPointsBuilder {
  typedef MSG_TrajectoryPoints Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_mec_id(::flatbuffers::Offset<::flatbuffers::String> mec_id) {
    fbb_.AddOffset(MSG_TrajectoryPoints::VT_MEC_ID, mec_id);
  }
  void add_ptc_type(uint8_t ptc_type) {
    fbb_.AddElement<uint8_t>(MSG_TrajectoryPoints::VT_PTC_TYPE, ptc_type, 255);
  }
  void add_ptc_id(uint16_t ptc_id) {
    fbb_.AddElement<uint16_t>(MSG_TrajectoryPoints::VT_PTC_ID, ptc_id, 65535);
  }
  void add_obu_id(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> obu_id) {
    fbb_.AddOffset(MSG_TrajectoryPoints::VT_OBU_ID, obu_id);
  }
  void add_points(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SingleTrajectoryPoint>>> points) {
    fbb_.AddOffset(MSG_TrajectoryPoints::VT_POINTS, points);
  }
  void add_ce_token(::flatbuffers::Offset<::flatbuffers::String> ce_token) {
    fbb_.AddOffset(MSG_TrajectoryPoints::VT_CE_TOKEN, ce_token);
  }
  void add_veh_type(::flatbuffers::Offset<MECData::DF_VehicleClassification> veh_type) {
    fbb_.AddOffset(MSG_TrajectoryPoints::VT_VEH_TYPE, veh_type);
  }
  void add_veh_char(::flatbuffers::Offset<MECData::DF_VehicleCharEx> veh_char) {
    fbb_.AddOffset(MSG_TrajectoryPoints::VT_VEH_CHAR, veh_char);
  }
  void add_node_ext(::flatbuffers::Offset<MECData::DF_TrajectoryNodeExtension> node_ext) {
    fbb_.AddOffset(MSG_TrajectoryPoints::VT_NODE_EXT, node_ext);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_TrajectoryPoints::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_TrajectoryPointsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_TrajectoryPoints> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_TrajectoryPoints>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_TrajectoryPoints> CreateMSG_TrajectoryPoints(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> mec_id = 0,
    uint8_t ptc_type = 255,
    uint16_t ptc_id = 65535,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> obu_id = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SingleTrajectoryPoint>>> points = 0,
    ::flatbuffers::Offset<::flatbuffers::String> ce_token = 0,
    ::flatbuffers::Offset<MECData::DF_VehicleClassification> veh_type = 0,
    ::flatbuffers::Offset<MECData::DF_VehicleCharEx> veh_char = 0,
    ::flatbuffers::Offset<MECData::DF_TrajectoryNodeExtension> node_ext = 0,
    int64_t msg_id = 0) {
  MSG_TrajectoryPointsBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_node_ext(node_ext);
  builder_.add_veh_char(veh_char);
  builder_.add_veh_type(veh_type);
  builder_.add_ce_token(ce_token);
  builder_.add_points(points);
  builder_.add_obu_id(obu_id);
  builder_.add_mec_id(mec_id);
  builder_.add_ptc_id(ptc_id);
  builder_.add_ptc_type(ptc_type);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_TrajectoryPoints> CreateMSG_TrajectoryPointsDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *mec_id = nullptr,
    uint8_t ptc_type = 255,
    uint16_t ptc_id = 65535,
    const std::vector<uint8_t> *obu_id = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_SingleTrajectoryPoint>> *points = nullptr,
    const char *ce_token = nullptr,
    ::flatbuffers::Offset<MECData::DF_VehicleClassification> veh_type = 0,
    ::flatbuffers::Offset<MECData::DF_VehicleCharEx> veh_char = 0,
    ::flatbuffers::Offset<MECData::DF_TrajectoryNodeExtension> node_ext = 0,
    int64_t msg_id = 0) {
  auto mec_id__ = mec_id ? _fbb.CreateString(mec_id) : 0;
  auto obu_id__ = obu_id ? _fbb.CreateVector<uint8_t>(*obu_id) : 0;
  auto points__ = points ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_SingleTrajectoryPoint>>(*points) : 0;
  auto ce_token__ = ce_token ? _fbb.CreateString(ce_token) : 0;
  return MECData::CreateMSG_TrajectoryPoints(
      _fbb,
      mec_id__,
      ptc_type,
      ptc_id,
      obu_id__,
      points__,
      ce_token__,
      veh_type,
      veh_char,
      node_ext,
      msg_id);
}

inline const MECData::MSG_TrajectoryPoints *GetMSG_TrajectoryPoints(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_TrajectoryPoints>(buf);
}

inline const MECData::MSG_TrajectoryPoints *GetSizePrefixedMSG_TrajectoryPoints(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_TrajectoryPoints>(buf);
}

inline bool VerifyMSG_TrajectoryPointsBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_TrajectoryPoints>(nullptr);
}

inline bool VerifySizePrefixedMSG_TrajectoryPointsBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_TrajectoryPoints>(nullptr);
}

inline void FinishMSG_TrajectoryPointsBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_TrajectoryPoints> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_TrajectoryPointsBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_TrajectoryPoints> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_TRAJECTORYPOINTS_MECDATA_H_
