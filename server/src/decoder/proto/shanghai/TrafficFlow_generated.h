// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_TRAFFICFLOW_MECDATA_H_
#define FLATBUFFERS_GENERATED_TRAFFICFLOW_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "BasicVehicleClass_generated.h"
#include "DebugTimeRecords_generated.h"
#include "Direction8_generated.h"
#include "Maneuver_generated.h"
#include "NodeReferenceID_generated.h"

namespace MECData {

struct DE_DetectorAreaStatInfo;
struct DE_DetectorAreaStatInfoBuilder;

struct DE_LaneStatInfo;
struct DE_LaneStatInfoBuilder;

struct DE_SectionStatInfo;
struct DE_SectionStatInfoBuilder;

struct DE_LinkStatInfo;
struct DE_LinkStatInfoBuilder;

struct DE_ConnectingLaneStatInfo;
struct DE_ConnectingLaneStatInfoBuilder;

struct DE_ConnectionStatInfo;
struct DE_ConnectionStatInfoBuilder;

struct DE_MovementStatInfo;
struct DE_MovementStatInfoBuilder;

struct DE_NodeStatInfo;
struct DE_NodeStatInfoBuilder;

struct DE_TrafficFlowStatByInterval;
struct DE_TrafficFlowStatByIntervalBuilder;

struct DE_TrafficFlowStatBySignalCycle;
struct DE_TrafficFlowStatBySignalCycleBuilder;

struct DF_SignalControlStatExtension;
struct DF_SignalControlStatExtensionBuilder;

struct DF_MapElementStatExtension;
struct DF_MapElementStatExtensionBuilder;

struct DF_OtherStatExtension;
struct DF_OtherStatExtensionBuilder;

struct DF_TrafficFlowStatExtension;
struct DF_TrafficFlowStatExtensionBuilder;

struct DF_TrafficFlowStat;
struct DF_TrafficFlowStatBuilder;

struct MSG_TrafficFlow;
struct MSG_TrafficFlowBuilder;

enum DE_TrafficFlowStatMapElement : uint8_t {
  DE_TrafficFlowStatMapElement_NONE = 0,
  DE_TrafficFlowStatMapElement_DE_DetectorAreaStatInfo = 1,
  DE_TrafficFlowStatMapElement_DE_LaneStatInfo = 2,
  DE_TrafficFlowStatMapElement_DE_SectionStatInfo = 3,
  DE_TrafficFlowStatMapElement_DE_LinkStatInfo = 4,
  DE_TrafficFlowStatMapElement_DE_ConnectingLaneStatInfo = 5,
  DE_TrafficFlowStatMapElement_DE_ConnectionStatInfo = 6,
  DE_TrafficFlowStatMapElement_DE_MovementStatInfo = 7,
  DE_TrafficFlowStatMapElement_DE_NodeStatInfo = 8,
  DE_TrafficFlowStatMapElement_MIN = DE_TrafficFlowStatMapElement_NONE,
  DE_TrafficFlowStatMapElement_MAX = DE_TrafficFlowStatMapElement_DE_NodeStatInfo
};

inline const DE_TrafficFlowStatMapElement (&EnumValuesDE_TrafficFlowStatMapElement())[9] {
  static const DE_TrafficFlowStatMapElement values[] = {
    DE_TrafficFlowStatMapElement_NONE,
    DE_TrafficFlowStatMapElement_DE_DetectorAreaStatInfo,
    DE_TrafficFlowStatMapElement_DE_LaneStatInfo,
    DE_TrafficFlowStatMapElement_DE_SectionStatInfo,
    DE_TrafficFlowStatMapElement_DE_LinkStatInfo,
    DE_TrafficFlowStatMapElement_DE_ConnectingLaneStatInfo,
    DE_TrafficFlowStatMapElement_DE_ConnectionStatInfo,
    DE_TrafficFlowStatMapElement_DE_MovementStatInfo,
    DE_TrafficFlowStatMapElement_DE_NodeStatInfo
  };
  return values;
}

inline const char * const *EnumNamesDE_TrafficFlowStatMapElement() {
  static const char * const names[10] = {
    "NONE",
    "DE_DetectorAreaStatInfo",
    "DE_LaneStatInfo",
    "DE_SectionStatInfo",
    "DE_LinkStatInfo",
    "DE_ConnectingLaneStatInfo",
    "DE_ConnectionStatInfo",
    "DE_MovementStatInfo",
    "DE_NodeStatInfo",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_TrafficFlowStatMapElement(DE_TrafficFlowStatMapElement e) {
  if (::flatbuffers::IsOutRange(e, DE_TrafficFlowStatMapElement_NONE, DE_TrafficFlowStatMapElement_DE_NodeStatInfo)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_TrafficFlowStatMapElement()[index];
}

template<typename T> struct DE_TrafficFlowStatMapElementTraits {
  static const DE_TrafficFlowStatMapElement enum_value = DE_TrafficFlowStatMapElement_NONE;
};

template<> struct DE_TrafficFlowStatMapElementTraits<MECData::DE_DetectorAreaStatInfo> {
  static const DE_TrafficFlowStatMapElement enum_value = DE_TrafficFlowStatMapElement_DE_DetectorAreaStatInfo;
};

template<> struct DE_TrafficFlowStatMapElementTraits<MECData::DE_LaneStatInfo> {
  static const DE_TrafficFlowStatMapElement enum_value = DE_TrafficFlowStatMapElement_DE_LaneStatInfo;
};

template<> struct DE_TrafficFlowStatMapElementTraits<MECData::DE_SectionStatInfo> {
  static const DE_TrafficFlowStatMapElement enum_value = DE_TrafficFlowStatMapElement_DE_SectionStatInfo;
};

template<> struct DE_TrafficFlowStatMapElementTraits<MECData::DE_LinkStatInfo> {
  static const DE_TrafficFlowStatMapElement enum_value = DE_TrafficFlowStatMapElement_DE_LinkStatInfo;
};

template<> struct DE_TrafficFlowStatMapElementTraits<MECData::DE_ConnectingLaneStatInfo> {
  static const DE_TrafficFlowStatMapElement enum_value = DE_TrafficFlowStatMapElement_DE_ConnectingLaneStatInfo;
};

template<> struct DE_TrafficFlowStatMapElementTraits<MECData::DE_ConnectionStatInfo> {
  static const DE_TrafficFlowStatMapElement enum_value = DE_TrafficFlowStatMapElement_DE_ConnectionStatInfo;
};

template<> struct DE_TrafficFlowStatMapElementTraits<MECData::DE_MovementStatInfo> {
  static const DE_TrafficFlowStatMapElement enum_value = DE_TrafficFlowStatMapElement_DE_MovementStatInfo;
};

template<> struct DE_TrafficFlowStatMapElementTraits<MECData::DE_NodeStatInfo> {
  static const DE_TrafficFlowStatMapElement enum_value = DE_TrafficFlowStatMapElement_DE_NodeStatInfo;
};

bool VerifyDE_TrafficFlowStatMapElement(::flatbuffers::Verifier &verifier, const void *obj, DE_TrafficFlowStatMapElement type);
bool VerifyDE_TrafficFlowStatMapElementVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types);

enum DE_TrafficFlowStatType : uint8_t {
  DE_TrafficFlowStatType_NONE = 0,
  DE_TrafficFlowStatType_DE_TrafficFlowStatByInterval = 1,
  DE_TrafficFlowStatType_DE_TrafficFlowStatBySignalCycle = 2,
  DE_TrafficFlowStatType_MIN = DE_TrafficFlowStatType_NONE,
  DE_TrafficFlowStatType_MAX = DE_TrafficFlowStatType_DE_TrafficFlowStatBySignalCycle
};

inline const DE_TrafficFlowStatType (&EnumValuesDE_TrafficFlowStatType())[3] {
  static const DE_TrafficFlowStatType values[] = {
    DE_TrafficFlowStatType_NONE,
    DE_TrafficFlowStatType_DE_TrafficFlowStatByInterval,
    DE_TrafficFlowStatType_DE_TrafficFlowStatBySignalCycle
  };
  return values;
}

inline const char * const *EnumNamesDE_TrafficFlowStatType() {
  static const char * const names[4] = {
    "NONE",
    "DE_TrafficFlowStatByInterval",
    "DE_TrafficFlowStatBySignalCycle",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_TrafficFlowStatType(DE_TrafficFlowStatType e) {
  if (::flatbuffers::IsOutRange(e, DE_TrafficFlowStatType_NONE, DE_TrafficFlowStatType_DE_TrafficFlowStatBySignalCycle)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_TrafficFlowStatType()[index];
}

template<typename T> struct DE_TrafficFlowStatTypeTraits {
  static const DE_TrafficFlowStatType enum_value = DE_TrafficFlowStatType_NONE;
};

template<> struct DE_TrafficFlowStatTypeTraits<MECData::DE_TrafficFlowStatByInterval> {
  static const DE_TrafficFlowStatType enum_value = DE_TrafficFlowStatType_DE_TrafficFlowStatByInterval;
};

template<> struct DE_TrafficFlowStatTypeTraits<MECData::DE_TrafficFlowStatBySignalCycle> {
  static const DE_TrafficFlowStatType enum_value = DE_TrafficFlowStatType_DE_TrafficFlowStatBySignalCycle;
};

bool VerifyDE_TrafficFlowStatType(::flatbuffers::Verifier &verifier, const void *obj, DE_TrafficFlowStatType type);
bool VerifyDE_TrafficFlowStatTypeVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types);

struct DE_DetectorAreaStatInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_DetectorAreaStatInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_DETECTOR_AREA_ID = 4
  };
  uint32_t detector_area_id() const {
    return GetField<uint32_t>(VT_DETECTOR_AREA_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_DETECTOR_AREA_ID, 4) &&
           verifier.EndTable();
  }
};

struct DE_DetectorAreaStatInfoBuilder {
  typedef DE_DetectorAreaStatInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_detector_area_id(uint32_t detector_area_id) {
    fbb_.AddElement<uint32_t>(DE_DetectorAreaStatInfo::VT_DETECTOR_AREA_ID, detector_area_id, 0);
  }
  explicit DE_DetectorAreaStatInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_DetectorAreaStatInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_DetectorAreaStatInfo>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_DetectorAreaStatInfo> CreateDE_DetectorAreaStatInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t detector_area_id = 0) {
  DE_DetectorAreaStatInfoBuilder builder_(_fbb);
  builder_.add_detector_area_id(detector_area_id);
  return builder_.Finish();
}

struct DE_LaneStatInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_LaneStatInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_EXT_ID = 4,
    VT_DIRECTION = 6
  };
  const ::flatbuffers::String *ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_EXT_ID);
  }
  MECData::DE_Direction8 direction() const {
    return static_cast<MECData::DE_Direction8>(GetField<uint8_t>(VT_DIRECTION, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_EXT_ID) &&
           verifier.VerifyString(ext_id()) &&
           VerifyField<uint8_t>(verifier, VT_DIRECTION, 1) &&
           verifier.EndTable();
  }
};

struct DE_LaneStatInfoBuilder {
  typedef DE_LaneStatInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_ext_id(::flatbuffers::Offset<::flatbuffers::String> ext_id) {
    fbb_.AddOffset(DE_LaneStatInfo::VT_EXT_ID, ext_id);
  }
  void add_direction(MECData::DE_Direction8 direction) {
    fbb_.AddElement<uint8_t>(DE_LaneStatInfo::VT_DIRECTION, static_cast<uint8_t>(direction), 0);
  }
  explicit DE_LaneStatInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_LaneStatInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_LaneStatInfo>(end);
    fbb_.Required(o, DE_LaneStatInfo::VT_EXT_ID);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_LaneStatInfo> CreateDE_LaneStatInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> ext_id = 0,
    MECData::DE_Direction8 direction = MECData::DE_Direction8_N) {
  DE_LaneStatInfoBuilder builder_(_fbb);
  builder_.add_ext_id(ext_id);
  builder_.add_direction(direction);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DE_LaneStatInfo> CreateDE_LaneStatInfoDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *ext_id = nullptr,
    MECData::DE_Direction8 direction = MECData::DE_Direction8_N) {
  auto ext_id__ = ext_id ? _fbb.CreateString(ext_id) : 0;
  return MECData::CreateDE_LaneStatInfo(
      _fbb,
      ext_id__,
      direction);
}

struct DE_SectionStatInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_SectionStatInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_EXT_ID = 4
  };
  const ::flatbuffers::String *ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_EXT_ID);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_EXT_ID) &&
           verifier.VerifyString(ext_id()) &&
           verifier.EndTable();
  }
};

struct DE_SectionStatInfoBuilder {
  typedef DE_SectionStatInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_ext_id(::flatbuffers::Offset<::flatbuffers::String> ext_id) {
    fbb_.AddOffset(DE_SectionStatInfo::VT_EXT_ID, ext_id);
  }
  explicit DE_SectionStatInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_SectionStatInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_SectionStatInfo>(end);
    fbb_.Required(o, DE_SectionStatInfo::VT_EXT_ID);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_SectionStatInfo> CreateDE_SectionStatInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> ext_id = 0) {
  DE_SectionStatInfoBuilder builder_(_fbb);
  builder_.add_ext_id(ext_id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DE_SectionStatInfo> CreateDE_SectionStatInfoDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *ext_id = nullptr) {
  auto ext_id__ = ext_id ? _fbb.CreateString(ext_id) : 0;
  return MECData::CreateDE_SectionStatInfo(
      _fbb,
      ext_id__);
}

struct DE_LinkStatInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_LinkStatInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_EXT_ID = 4,
    VT_DIRECTION = 6
  };
  const ::flatbuffers::String *ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_EXT_ID);
  }
  MECData::DE_Direction8 direction() const {
    return static_cast<MECData::DE_Direction8>(GetField<uint8_t>(VT_DIRECTION, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_EXT_ID) &&
           verifier.VerifyString(ext_id()) &&
           VerifyField<uint8_t>(verifier, VT_DIRECTION, 1) &&
           verifier.EndTable();
  }
};

struct DE_LinkStatInfoBuilder {
  typedef DE_LinkStatInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_ext_id(::flatbuffers::Offset<::flatbuffers::String> ext_id) {
    fbb_.AddOffset(DE_LinkStatInfo::VT_EXT_ID, ext_id);
  }
  void add_direction(MECData::DE_Direction8 direction) {
    fbb_.AddElement<uint8_t>(DE_LinkStatInfo::VT_DIRECTION, static_cast<uint8_t>(direction), 0);
  }
  explicit DE_LinkStatInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_LinkStatInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_LinkStatInfo>(end);
    fbb_.Required(o, DE_LinkStatInfo::VT_EXT_ID);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_LinkStatInfo> CreateDE_LinkStatInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> ext_id = 0,
    MECData::DE_Direction8 direction = MECData::DE_Direction8_N) {
  DE_LinkStatInfoBuilder builder_(_fbb);
  builder_.add_ext_id(ext_id);
  builder_.add_direction(direction);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DE_LinkStatInfo> CreateDE_LinkStatInfoDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *ext_id = nullptr,
    MECData::DE_Direction8 direction = MECData::DE_Direction8_N) {
  auto ext_id__ = ext_id ? _fbb.CreateString(ext_id) : 0;
  return MECData::CreateDE_LinkStatInfo(
      _fbb,
      ext_id__,
      direction);
}

struct DE_ConnectingLaneStatInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_ConnectingLaneStatInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_EXT_ID = 4
  };
  const ::flatbuffers::String *ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_EXT_ID);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_EXT_ID) &&
           verifier.VerifyString(ext_id()) &&
           verifier.EndTable();
  }
};

struct DE_ConnectingLaneStatInfoBuilder {
  typedef DE_ConnectingLaneStatInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_ext_id(::flatbuffers::Offset<::flatbuffers::String> ext_id) {
    fbb_.AddOffset(DE_ConnectingLaneStatInfo::VT_EXT_ID, ext_id);
  }
  explicit DE_ConnectingLaneStatInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_ConnectingLaneStatInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_ConnectingLaneStatInfo>(end);
    fbb_.Required(o, DE_ConnectingLaneStatInfo::VT_EXT_ID);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_ConnectingLaneStatInfo> CreateDE_ConnectingLaneStatInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> ext_id = 0) {
  DE_ConnectingLaneStatInfoBuilder builder_(_fbb);
  builder_.add_ext_id(ext_id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DE_ConnectingLaneStatInfo> CreateDE_ConnectingLaneStatInfoDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *ext_id = nullptr) {
  auto ext_id__ = ext_id ? _fbb.CreateString(ext_id) : 0;
  return MECData::CreateDE_ConnectingLaneStatInfo(
      _fbb,
      ext_id__);
}

struct DE_ConnectionStatInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_ConnectionStatInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_EXT_ID = 4
  };
  const ::flatbuffers::String *ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_EXT_ID);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_EXT_ID) &&
           verifier.VerifyString(ext_id()) &&
           verifier.EndTable();
  }
};

struct DE_ConnectionStatInfoBuilder {
  typedef DE_ConnectionStatInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_ext_id(::flatbuffers::Offset<::flatbuffers::String> ext_id) {
    fbb_.AddOffset(DE_ConnectionStatInfo::VT_EXT_ID, ext_id);
  }
  explicit DE_ConnectionStatInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_ConnectionStatInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_ConnectionStatInfo>(end);
    fbb_.Required(o, DE_ConnectionStatInfo::VT_EXT_ID);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_ConnectionStatInfo> CreateDE_ConnectionStatInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> ext_id = 0) {
  DE_ConnectionStatInfoBuilder builder_(_fbb);
  builder_.add_ext_id(ext_id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DE_ConnectionStatInfo> CreateDE_ConnectionStatInfoDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *ext_id = nullptr) {
  auto ext_id__ = ext_id ? _fbb.CreateString(ext_id) : 0;
  return MECData::CreateDE_ConnectionStatInfo(
      _fbb,
      ext_id__);
}

struct DE_MovementStatInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_MovementStatInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_EXT_ID = 4,
    VT_FROM_DIRECTION = 6,
    VT_MANEUVER = 8
  };
  const ::flatbuffers::String *ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_EXT_ID);
  }
  MECData::DE_Direction8 from_direction() const {
    return static_cast<MECData::DE_Direction8>(GetField<uint8_t>(VT_FROM_DIRECTION, 0));
  }
  MECData::DE_Maneuver maneuver() const {
    return static_cast<MECData::DE_Maneuver>(GetField<uint8_t>(VT_MANEUVER, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_EXT_ID) &&
           verifier.VerifyString(ext_id()) &&
           VerifyField<uint8_t>(verifier, VT_FROM_DIRECTION, 1) &&
           VerifyField<uint8_t>(verifier, VT_MANEUVER, 1) &&
           verifier.EndTable();
  }
};

struct DE_MovementStatInfoBuilder {
  typedef DE_MovementStatInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_ext_id(::flatbuffers::Offset<::flatbuffers::String> ext_id) {
    fbb_.AddOffset(DE_MovementStatInfo::VT_EXT_ID, ext_id);
  }
  void add_from_direction(MECData::DE_Direction8 from_direction) {
    fbb_.AddElement<uint8_t>(DE_MovementStatInfo::VT_FROM_DIRECTION, static_cast<uint8_t>(from_direction), 0);
  }
  void add_maneuver(MECData::DE_Maneuver maneuver) {
    fbb_.AddElement<uint8_t>(DE_MovementStatInfo::VT_MANEUVER, static_cast<uint8_t>(maneuver), 0);
  }
  explicit DE_MovementStatInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_MovementStatInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_MovementStatInfo>(end);
    fbb_.Required(o, DE_MovementStatInfo::VT_EXT_ID);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_MovementStatInfo> CreateDE_MovementStatInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> ext_id = 0,
    MECData::DE_Direction8 from_direction = MECData::DE_Direction8_N,
    MECData::DE_Maneuver maneuver = MECData::DE_Maneuver_maneuverStraight) {
  DE_MovementStatInfoBuilder builder_(_fbb);
  builder_.add_ext_id(ext_id);
  builder_.add_maneuver(maneuver);
  builder_.add_from_direction(from_direction);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DE_MovementStatInfo> CreateDE_MovementStatInfoDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *ext_id = nullptr,
    MECData::DE_Direction8 from_direction = MECData::DE_Direction8_N,
    MECData::DE_Maneuver maneuver = MECData::DE_Maneuver_maneuverStraight) {
  auto ext_id__ = ext_id ? _fbb.CreateString(ext_id) : 0;
  return MECData::CreateDE_MovementStatInfo(
      _fbb,
      ext_id__,
      from_direction,
      maneuver);
}

struct DE_NodeStatInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_NodeStatInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ID = 4
  };
  const MECData::DF_NodeReferenceID *id() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_ID);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_ID) &&
           verifier.VerifyTable(id()) &&
           verifier.EndTable();
  }
};

struct DE_NodeStatInfoBuilder {
  typedef DE_NodeStatInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_id(::flatbuffers::Offset<MECData::DF_NodeReferenceID> id) {
    fbb_.AddOffset(DE_NodeStatInfo::VT_ID, id);
  }
  explicit DE_NodeStatInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_NodeStatInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_NodeStatInfo>(end);
    fbb_.Required(o, DE_NodeStatInfo::VT_ID);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_NodeStatInfo> CreateDE_NodeStatInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> id = 0) {
  DE_NodeStatInfoBuilder builder_(_fbb);
  builder_.add_id(id);
  return builder_.Finish();
}

struct DE_TrafficFlowStatByInterval FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_TrafficFlowStatByIntervalBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_INTERVAL = 4
  };
  uint32_t interval() const {
    return GetField<uint32_t>(VT_INTERVAL, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_INTERVAL, 4) &&
           verifier.EndTable();
  }
};

struct DE_TrafficFlowStatByIntervalBuilder {
  typedef DE_TrafficFlowStatByInterval Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_interval(uint32_t interval) {
    fbb_.AddElement<uint32_t>(DE_TrafficFlowStatByInterval::VT_INTERVAL, interval, 0);
  }
  explicit DE_TrafficFlowStatByIntervalBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_TrafficFlowStatByInterval> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_TrafficFlowStatByInterval>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_TrafficFlowStatByInterval> CreateDE_TrafficFlowStatByInterval(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t interval = 0) {
  DE_TrafficFlowStatByIntervalBuilder builder_(_fbb);
  builder_.add_interval(interval);
  return builder_.Finish();
}

struct DE_TrafficFlowStatBySignalCycle FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_TrafficFlowStatBySignalCycleBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SEQUENCE = 4
  };
  uint32_t sequence() const {
    return GetField<uint32_t>(VT_SEQUENCE, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_SEQUENCE, 4) &&
           verifier.EndTable();
  }
};

struct DE_TrafficFlowStatBySignalCycleBuilder {
  typedef DE_TrafficFlowStatBySignalCycle Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_sequence(uint32_t sequence) {
    fbb_.AddElement<uint32_t>(DE_TrafficFlowStatBySignalCycle::VT_SEQUENCE, sequence, 0);
  }
  explicit DE_TrafficFlowStatBySignalCycleBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_TrafficFlowStatBySignalCycle> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_TrafficFlowStatBySignalCycle>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_TrafficFlowStatBySignalCycle> CreateDE_TrafficFlowStatBySignalCycle(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t sequence = 0) {
  DE_TrafficFlowStatBySignalCycleBuilder builder_(_fbb);
  builder_.add_sequence(sequence);
  return builder_.Finish();
}

struct DF_SignalControlStatExtension FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SignalControlStatExtensionBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PHASIC_ORDER = 4,
    VT_GREEN_START_QUEUE = 6,
    VT_RED_START_QUEUE = 8,
    VT_GREEN_UTILIZATION = 10
  };
  uint8_t phasic_order() const {
    return GetField<uint8_t>(VT_PHASIC_ORDER, 0);
  }
  uint32_t green_start_queue() const {
    return GetField<uint32_t>(VT_GREEN_START_QUEUE, 4294967295);
  }
  uint32_t red_start_queue() const {
    return GetField<uint32_t>(VT_RED_START_QUEUE, 4294967295);
  }
  uint16_t green_utilization() const {
    return GetField<uint16_t>(VT_GREEN_UTILIZATION, 65535);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_PHASIC_ORDER, 1) &&
           VerifyField<uint32_t>(verifier, VT_GREEN_START_QUEUE, 4) &&
           VerifyField<uint32_t>(verifier, VT_RED_START_QUEUE, 4) &&
           VerifyField<uint16_t>(verifier, VT_GREEN_UTILIZATION, 2) &&
           verifier.EndTable();
  }
};

struct DF_SignalControlStatExtensionBuilder {
  typedef DF_SignalControlStatExtension Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_phasic_order(uint8_t phasic_order) {
    fbb_.AddElement<uint8_t>(DF_SignalControlStatExtension::VT_PHASIC_ORDER, phasic_order, 0);
  }
  void add_green_start_queue(uint32_t green_start_queue) {
    fbb_.AddElement<uint32_t>(DF_SignalControlStatExtension::VT_GREEN_START_QUEUE, green_start_queue, 4294967295);
  }
  void add_red_start_queue(uint32_t red_start_queue) {
    fbb_.AddElement<uint32_t>(DF_SignalControlStatExtension::VT_RED_START_QUEUE, red_start_queue, 4294967295);
  }
  void add_green_utilization(uint16_t green_utilization) {
    fbb_.AddElement<uint16_t>(DF_SignalControlStatExtension::VT_GREEN_UTILIZATION, green_utilization, 65535);
  }
  explicit DF_SignalControlStatExtensionBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_SignalControlStatExtension> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_SignalControlStatExtension>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_SignalControlStatExtension> CreateDF_SignalControlStatExtension(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t phasic_order = 0,
    uint32_t green_start_queue = 4294967295,
    uint32_t red_start_queue = 4294967295,
    uint16_t green_utilization = 65535) {
  DF_SignalControlStatExtensionBuilder builder_(_fbb);
  builder_.add_red_start_queue(red_start_queue);
  builder_.add_green_start_queue(green_start_queue);
  builder_.add_green_utilization(green_utilization);
  builder_.add_phasic_order(phasic_order);
  return builder_.Finish();
}

struct DF_MapElementStatExtension FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_MapElementStatExtensionBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TIMESTAMP = 4,
    VT_CAPACITY = 6,
    VT_AVG_SATURATION = 8,
    VT_AVG_OCCUPATION = 10,
    VT_AVG_TIME_OCCUPATION = 12,
    VT_AVG_GREEN_START_QUEUE = 14,
    VT_AVG_RED_START_QUEUE = 16,
    VT_GREEN_UTILIZATION = 18,
    VT_LAST_INTERVAL_VOLUME = 20,
    VT_LAST_INTERVAL_SPEED_AREA = 22
  };
  uint64_t timestamp() const {
    return GetField<uint64_t>(VT_TIMESTAMP, 0);
  }
  uint32_t capacity() const {
    return GetField<uint32_t>(VT_CAPACITY, 4294967295);
  }
  uint16_t avg_saturation() const {
    return GetField<uint16_t>(VT_AVG_SATURATION, 65535);
  }
  uint16_t avg_occupation() const {
    return GetField<uint16_t>(VT_AVG_OCCUPATION, 65535);
  }
  uint16_t avg_time_occupation() const {
    return GetField<uint16_t>(VT_AVG_TIME_OCCUPATION, 65535);
  }
  uint32_t avg_green_start_queue() const {
    return GetField<uint32_t>(VT_AVG_GREEN_START_QUEUE, 4294967295);
  }
  uint32_t avg_red_start_queue() const {
    return GetField<uint32_t>(VT_AVG_RED_START_QUEUE, 4294967295);
  }
  uint16_t green_utilization() const {
    return GetField<uint16_t>(VT_GREEN_UTILIZATION, 65535);
  }
  uint64_t last_interval_volume() const {
    return GetField<uint64_t>(VT_LAST_INTERVAL_VOLUME, 18446744073709551615ULL);
  }
  uint32_t last_interval_speed_area() const {
    return GetField<uint32_t>(VT_LAST_INTERVAL_SPEED_AREA, 4294967295);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint64_t>(verifier, VT_TIMESTAMP, 8) &&
           VerifyField<uint32_t>(verifier, VT_CAPACITY, 4) &&
           VerifyField<uint16_t>(verifier, VT_AVG_SATURATION, 2) &&
           VerifyField<uint16_t>(verifier, VT_AVG_OCCUPATION, 2) &&
           VerifyField<uint16_t>(verifier, VT_AVG_TIME_OCCUPATION, 2) &&
           VerifyField<uint32_t>(verifier, VT_AVG_GREEN_START_QUEUE, 4) &&
           VerifyField<uint32_t>(verifier, VT_AVG_RED_START_QUEUE, 4) &&
           VerifyField<uint16_t>(verifier, VT_GREEN_UTILIZATION, 2) &&
           VerifyField<uint64_t>(verifier, VT_LAST_INTERVAL_VOLUME, 8) &&
           VerifyField<uint32_t>(verifier, VT_LAST_INTERVAL_SPEED_AREA, 4) &&
           verifier.EndTable();
  }
};

struct DF_MapElementStatExtensionBuilder {
  typedef DF_MapElementStatExtension Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_timestamp(uint64_t timestamp) {
    fbb_.AddElement<uint64_t>(DF_MapElementStatExtension::VT_TIMESTAMP, timestamp, 0);
  }
  void add_capacity(uint32_t capacity) {
    fbb_.AddElement<uint32_t>(DF_MapElementStatExtension::VT_CAPACITY, capacity, 4294967295);
  }
  void add_avg_saturation(uint16_t avg_saturation) {
    fbb_.AddElement<uint16_t>(DF_MapElementStatExtension::VT_AVG_SATURATION, avg_saturation, 65535);
  }
  void add_avg_occupation(uint16_t avg_occupation) {
    fbb_.AddElement<uint16_t>(DF_MapElementStatExtension::VT_AVG_OCCUPATION, avg_occupation, 65535);
  }
  void add_avg_time_occupation(uint16_t avg_time_occupation) {
    fbb_.AddElement<uint16_t>(DF_MapElementStatExtension::VT_AVG_TIME_OCCUPATION, avg_time_occupation, 65535);
  }
  void add_avg_green_start_queue(uint32_t avg_green_start_queue) {
    fbb_.AddElement<uint32_t>(DF_MapElementStatExtension::VT_AVG_GREEN_START_QUEUE, avg_green_start_queue, 4294967295);
  }
  void add_avg_red_start_queue(uint32_t avg_red_start_queue) {
    fbb_.AddElement<uint32_t>(DF_MapElementStatExtension::VT_AVG_RED_START_QUEUE, avg_red_start_queue, 4294967295);
  }
  void add_green_utilization(uint16_t green_utilization) {
    fbb_.AddElement<uint16_t>(DF_MapElementStatExtension::VT_GREEN_UTILIZATION, green_utilization, 65535);
  }
  void add_last_interval_volume(uint64_t last_interval_volume) {
    fbb_.AddElement<uint64_t>(DF_MapElementStatExtension::VT_LAST_INTERVAL_VOLUME, last_interval_volume, 18446744073709551615ULL);
  }
  void add_last_interval_speed_area(uint32_t last_interval_speed_area) {
    fbb_.AddElement<uint32_t>(DF_MapElementStatExtension::VT_LAST_INTERVAL_SPEED_AREA, last_interval_speed_area, 4294967295);
  }
  explicit DF_MapElementStatExtensionBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_MapElementStatExtension> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_MapElementStatExtension>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_MapElementStatExtension> CreateDF_MapElementStatExtension(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint64_t timestamp = 0,
    uint32_t capacity = 4294967295,
    uint16_t avg_saturation = 65535,
    uint16_t avg_occupation = 65535,
    uint16_t avg_time_occupation = 65535,
    uint32_t avg_green_start_queue = 4294967295,
    uint32_t avg_red_start_queue = 4294967295,
    uint16_t green_utilization = 65535,
    uint64_t last_interval_volume = 18446744073709551615ULL,
    uint32_t last_interval_speed_area = 4294967295) {
  DF_MapElementStatExtensionBuilder builder_(_fbb);
  builder_.add_last_interval_volume(last_interval_volume);
  builder_.add_timestamp(timestamp);
  builder_.add_last_interval_speed_area(last_interval_speed_area);
  builder_.add_avg_red_start_queue(avg_red_start_queue);
  builder_.add_avg_green_start_queue(avg_green_start_queue);
  builder_.add_capacity(capacity);
  builder_.add_green_utilization(green_utilization);
  builder_.add_avg_time_occupation(avg_time_occupation);
  builder_.add_avg_occupation(avg_occupation);
  builder_.add_avg_saturation(avg_saturation);
  return builder_.Finish();
}

struct DF_OtherStatExtension FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_OtherStatExtensionBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_STAT_NAME = 4,
    VT_NUMERIC_VALUE = 6,
    VT_STRING_VALUE = 8
  };
  const ::flatbuffers::String *stat_name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_STAT_NAME);
  }
  int64_t numeric_value() const {
    return GetField<int64_t>(VT_NUMERIC_VALUE, 0);
  }
  const ::flatbuffers::String *string_value() const {
    return GetPointer<const ::flatbuffers::String *>(VT_STRING_VALUE);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_STAT_NAME) &&
           verifier.VerifyString(stat_name()) &&
           VerifyField<int64_t>(verifier, VT_NUMERIC_VALUE, 8) &&
           VerifyOffset(verifier, VT_STRING_VALUE) &&
           verifier.VerifyString(string_value()) &&
           verifier.EndTable();
  }
};

struct DF_OtherStatExtensionBuilder {
  typedef DF_OtherStatExtension Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_stat_name(::flatbuffers::Offset<::flatbuffers::String> stat_name) {
    fbb_.AddOffset(DF_OtherStatExtension::VT_STAT_NAME, stat_name);
  }
  void add_numeric_value(int64_t numeric_value) {
    fbb_.AddElement<int64_t>(DF_OtherStatExtension::VT_NUMERIC_VALUE, numeric_value, 0);
  }
  void add_string_value(::flatbuffers::Offset<::flatbuffers::String> string_value) {
    fbb_.AddOffset(DF_OtherStatExtension::VT_STRING_VALUE, string_value);
  }
  explicit DF_OtherStatExtensionBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_OtherStatExtension> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_OtherStatExtension>(end);
    fbb_.Required(o, DF_OtherStatExtension::VT_STAT_NAME);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_OtherStatExtension> CreateDF_OtherStatExtension(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> stat_name = 0,
    int64_t numeric_value = 0,
    ::flatbuffers::Offset<::flatbuffers::String> string_value = 0) {
  DF_OtherStatExtensionBuilder builder_(_fbb);
  builder_.add_numeric_value(numeric_value);
  builder_.add_string_value(string_value);
  builder_.add_stat_name(stat_name);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_OtherStatExtension> CreateDF_OtherStatExtensionDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *stat_name = nullptr,
    int64_t numeric_value = 0,
    const char *string_value = nullptr) {
  auto stat_name__ = stat_name ? _fbb.CreateString(stat_name) : 0;
  auto string_value__ = string_value ? _fbb.CreateString(string_value) : 0;
  return MECData::CreateDF_OtherStatExtension(
      _fbb,
      stat_name__,
      numeric_value,
      string_value__);
}

struct DF_TrafficFlowStatExtension FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_TrafficFlowStatExtensionBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MAP_ELEMENT = 4,
    VT_SIGNAL = 6,
    VT_OTHERS = 8
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_MapElementStatExtension>> *map_element() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_MapElementStatExtension>> *>(VT_MAP_ELEMENT);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SignalControlStatExtension>> *signal() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SignalControlStatExtension>> *>(VT_SIGNAL);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_OtherStatExtension>> *others() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_OtherStatExtension>> *>(VT_OTHERS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_MAP_ELEMENT) &&
           verifier.VerifyVector(map_element()) &&
           verifier.VerifyVectorOfTables(map_element()) &&
           VerifyOffset(verifier, VT_SIGNAL) &&
           verifier.VerifyVector(signal()) &&
           verifier.VerifyVectorOfTables(signal()) &&
           VerifyOffset(verifier, VT_OTHERS) &&
           verifier.VerifyVector(others()) &&
           verifier.VerifyVectorOfTables(others()) &&
           verifier.EndTable();
  }
};

struct DF_TrafficFlowStatExtensionBuilder {
  typedef DF_TrafficFlowStatExtension Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_map_element(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_MapElementStatExtension>>> map_element) {
    fbb_.AddOffset(DF_TrafficFlowStatExtension::VT_MAP_ELEMENT, map_element);
  }
  void add_signal(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SignalControlStatExtension>>> signal) {
    fbb_.AddOffset(DF_TrafficFlowStatExtension::VT_SIGNAL, signal);
  }
  void add_others(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_OtherStatExtension>>> others) {
    fbb_.AddOffset(DF_TrafficFlowStatExtension::VT_OTHERS, others);
  }
  explicit DF_TrafficFlowStatExtensionBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_TrafficFlowStatExtension> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_TrafficFlowStatExtension>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_TrafficFlowStatExtension> CreateDF_TrafficFlowStatExtension(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_MapElementStatExtension>>> map_element = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SignalControlStatExtension>>> signal = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_OtherStatExtension>>> others = 0) {
  DF_TrafficFlowStatExtensionBuilder builder_(_fbb);
  builder_.add_others(others);
  builder_.add_signal(signal);
  builder_.add_map_element(map_element);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_TrafficFlowStatExtension> CreateDF_TrafficFlowStatExtensionDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<MECData::DF_MapElementStatExtension>> *map_element = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_SignalControlStatExtension>> *signal = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_OtherStatExtension>> *others = nullptr) {
  auto map_element__ = map_element ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_MapElementStatExtension>>(*map_element) : 0;
  auto signal__ = signal ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_SignalControlStatExtension>>(*signal) : 0;
  auto others__ = others ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_OtherStatExtension>>(*others) : 0;
  return MECData::CreateDF_TrafficFlowStatExtension(
      _fbb,
      map_element__,
      signal__,
      others__);
}

struct DF_TrafficFlowStat FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_TrafficFlowStatBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MAP_ELEMENT_TYPE = 4,
    VT_MAP_ELEMENT = 6,
    VT_PTC_TYPE = 8,
    VT_VEH_TYPE = 10,
    VT_VOLUME = 12,
    VT_SPEED_POINT = 14,
    VT_SPEED_AREA = 16,
    VT_DENSITY = 18,
    VT_DELAY = 20,
    VT_QUEUE_LENGTH = 22,
    VT_CONGESTION = 24,
    VT_EXT = 26,
    VT_OCCUPATION = 28,
    VT_TIME_HEADWAY = 30,
    VT_SPACE_HEADWAY = 32,
    VT_TRAVEL_TIME = 34,
    VT_GREEN_UTILIZATION = 36,
    VT_SATURATION = 38,
    VT_STOPS = 40,
    VT_QUEUED_VEHICLES = 42,
    VT_TIME_OCCUPATION = 44
  };
  MECData::DE_TrafficFlowStatMapElement map_element_type() const {
    return static_cast<MECData::DE_TrafficFlowStatMapElement>(GetField<uint8_t>(VT_MAP_ELEMENT_TYPE, 0));
  }
  const void *map_element() const {
    return GetPointer<const void *>(VT_MAP_ELEMENT);
  }
  template<typename T> const T *map_element_as() const;
  const MECData::DE_DetectorAreaStatInfo *map_element_as_DE_DetectorAreaStatInfo() const {
    return map_element_type() == MECData::DE_TrafficFlowStatMapElement_DE_DetectorAreaStatInfo ? static_cast<const MECData::DE_DetectorAreaStatInfo *>(map_element()) : nullptr;
  }
  const MECData::DE_LaneStatInfo *map_element_as_DE_LaneStatInfo() const {
    return map_element_type() == MECData::DE_TrafficFlowStatMapElement_DE_LaneStatInfo ? static_cast<const MECData::DE_LaneStatInfo *>(map_element()) : nullptr;
  }
  const MECData::DE_SectionStatInfo *map_element_as_DE_SectionStatInfo() const {
    return map_element_type() == MECData::DE_TrafficFlowStatMapElement_DE_SectionStatInfo ? static_cast<const MECData::DE_SectionStatInfo *>(map_element()) : nullptr;
  }
  const MECData::DE_LinkStatInfo *map_element_as_DE_LinkStatInfo() const {
    return map_element_type() == MECData::DE_TrafficFlowStatMapElement_DE_LinkStatInfo ? static_cast<const MECData::DE_LinkStatInfo *>(map_element()) : nullptr;
  }
  const MECData::DE_ConnectingLaneStatInfo *map_element_as_DE_ConnectingLaneStatInfo() const {
    return map_element_type() == MECData::DE_TrafficFlowStatMapElement_DE_ConnectingLaneStatInfo ? static_cast<const MECData::DE_ConnectingLaneStatInfo *>(map_element()) : nullptr;
  }
  const MECData::DE_ConnectionStatInfo *map_element_as_DE_ConnectionStatInfo() const {
    return map_element_type() == MECData::DE_TrafficFlowStatMapElement_DE_ConnectionStatInfo ? static_cast<const MECData::DE_ConnectionStatInfo *>(map_element()) : nullptr;
  }
  const MECData::DE_MovementStatInfo *map_element_as_DE_MovementStatInfo() const {
    return map_element_type() == MECData::DE_TrafficFlowStatMapElement_DE_MovementStatInfo ? static_cast<const MECData::DE_MovementStatInfo *>(map_element()) : nullptr;
  }
  const MECData::DE_NodeStatInfo *map_element_as_DE_NodeStatInfo() const {
    return map_element_type() == MECData::DE_TrafficFlowStatMapElement_DE_NodeStatInfo ? static_cast<const MECData::DE_NodeStatInfo *>(map_element()) : nullptr;
  }
  uint8_t ptc_type() const {
    return GetField<uint8_t>(VT_PTC_TYPE, 255);
  }
  MECData::DE_BasicVehicleClass veh_type() const {
    return static_cast<MECData::DE_BasicVehicleClass>(GetField<uint8_t>(VT_VEH_TYPE, 0));
  }
  uint64_t volume() const {
    return GetField<uint64_t>(VT_VOLUME, 18446744073709551615ULL);
  }
  uint32_t speed_point() const {
    return GetField<uint32_t>(VT_SPEED_POINT, 4294967295);
  }
  uint32_t speed_area() const {
    return GetField<uint32_t>(VT_SPEED_AREA, 4294967295);
  }
  uint64_t density() const {
    return GetField<uint64_t>(VT_DENSITY, 18446744073709551615ULL);
  }
  uint16_t delay() const {
    return GetField<uint16_t>(VT_DELAY, 65535);
  }
  uint16_t queue_length() const {
    return GetField<uint16_t>(VT_QUEUE_LENGTH, 65535);
  }
  uint8_t congestion() const {
    return GetField<uint8_t>(VT_CONGESTION, 255);
  }
  const MECData::DF_TrafficFlowStatExtension *ext() const {
    return GetPointer<const MECData::DF_TrafficFlowStatExtension *>(VT_EXT);
  }
  uint16_t occupation() const {
    return GetField<uint16_t>(VT_OCCUPATION, 65535);
  }
  uint32_t time_headway() const {
    return GetField<uint32_t>(VT_TIME_HEADWAY, 4294967295);
  }
  uint32_t space_headway() const {
    return GetField<uint32_t>(VT_SPACE_HEADWAY, 4294967295);
  }
  uint16_t travel_time() const {
    return GetField<uint16_t>(VT_TRAVEL_TIME, 65535);
  }
  uint16_t green_utilization() const {
    return GetField<uint16_t>(VT_GREEN_UTILIZATION, 65535);
  }
  uint16_t saturation() const {
    return GetField<uint16_t>(VT_SATURATION, 65535);
  }
  uint8_t stops() const {
    return GetField<uint8_t>(VT_STOPS, 255);
  }
  uint16_t queued_vehicles() const {
    return GetField<uint16_t>(VT_QUEUED_VEHICLES, 65535);
  }
  uint16_t time_occupation() const {
    return GetField<uint16_t>(VT_TIME_OCCUPATION, 65535);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_MAP_ELEMENT_TYPE, 1) &&
           VerifyOffset(verifier, VT_MAP_ELEMENT) &&
           VerifyDE_TrafficFlowStatMapElement(verifier, map_element(), map_element_type()) &&
           VerifyField<uint8_t>(verifier, VT_PTC_TYPE, 1) &&
           VerifyField<uint8_t>(verifier, VT_VEH_TYPE, 1) &&
           VerifyField<uint64_t>(verifier, VT_VOLUME, 8) &&
           VerifyField<uint32_t>(verifier, VT_SPEED_POINT, 4) &&
           VerifyField<uint32_t>(verifier, VT_SPEED_AREA, 4) &&
           VerifyField<uint64_t>(verifier, VT_DENSITY, 8) &&
           VerifyField<uint16_t>(verifier, VT_DELAY, 2) &&
           VerifyField<uint16_t>(verifier, VT_QUEUE_LENGTH, 2) &&
           VerifyField<uint8_t>(verifier, VT_CONGESTION, 1) &&
           VerifyOffset(verifier, VT_EXT) &&
           verifier.VerifyTable(ext()) &&
           VerifyField<uint16_t>(verifier, VT_OCCUPATION, 2) &&
           VerifyField<uint32_t>(verifier, VT_TIME_HEADWAY, 4) &&
           VerifyField<uint32_t>(verifier, VT_SPACE_HEADWAY, 4) &&
           VerifyField<uint16_t>(verifier, VT_TRAVEL_TIME, 2) &&
           VerifyField<uint16_t>(verifier, VT_GREEN_UTILIZATION, 2) &&
           VerifyField<uint16_t>(verifier, VT_SATURATION, 2) &&
           VerifyField<uint8_t>(verifier, VT_STOPS, 1) &&
           VerifyField<uint16_t>(verifier, VT_QUEUED_VEHICLES, 2) &&
           VerifyField<uint16_t>(verifier, VT_TIME_OCCUPATION, 2) &&
           verifier.EndTable();
  }
};

template<> inline const MECData::DE_DetectorAreaStatInfo *DF_TrafficFlowStat::map_element_as<MECData::DE_DetectorAreaStatInfo>() const {
  return map_element_as_DE_DetectorAreaStatInfo();
}

template<> inline const MECData::DE_LaneStatInfo *DF_TrafficFlowStat::map_element_as<MECData::DE_LaneStatInfo>() const {
  return map_element_as_DE_LaneStatInfo();
}

template<> inline const MECData::DE_SectionStatInfo *DF_TrafficFlowStat::map_element_as<MECData::DE_SectionStatInfo>() const {
  return map_element_as_DE_SectionStatInfo();
}

template<> inline const MECData::DE_LinkStatInfo *DF_TrafficFlowStat::map_element_as<MECData::DE_LinkStatInfo>() const {
  return map_element_as_DE_LinkStatInfo();
}

template<> inline const MECData::DE_ConnectingLaneStatInfo *DF_TrafficFlowStat::map_element_as<MECData::DE_ConnectingLaneStatInfo>() const {
  return map_element_as_DE_ConnectingLaneStatInfo();
}

template<> inline const MECData::DE_ConnectionStatInfo *DF_TrafficFlowStat::map_element_as<MECData::DE_ConnectionStatInfo>() const {
  return map_element_as_DE_ConnectionStatInfo();
}

template<> inline const MECData::DE_MovementStatInfo *DF_TrafficFlowStat::map_element_as<MECData::DE_MovementStatInfo>() const {
  return map_element_as_DE_MovementStatInfo();
}

template<> inline const MECData::DE_NodeStatInfo *DF_TrafficFlowStat::map_element_as<MECData::DE_NodeStatInfo>() const {
  return map_element_as_DE_NodeStatInfo();
}

struct DF_TrafficFlowStatBuilder {
  typedef DF_TrafficFlowStat Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_map_element_type(MECData::DE_TrafficFlowStatMapElement map_element_type) {
    fbb_.AddElement<uint8_t>(DF_TrafficFlowStat::VT_MAP_ELEMENT_TYPE, static_cast<uint8_t>(map_element_type), 0);
  }
  void add_map_element(::flatbuffers::Offset<void> map_element) {
    fbb_.AddOffset(DF_TrafficFlowStat::VT_MAP_ELEMENT, map_element);
  }
  void add_ptc_type(uint8_t ptc_type) {
    fbb_.AddElement<uint8_t>(DF_TrafficFlowStat::VT_PTC_TYPE, ptc_type, 255);
  }
  void add_veh_type(MECData::DE_BasicVehicleClass veh_type) {
    fbb_.AddElement<uint8_t>(DF_TrafficFlowStat::VT_VEH_TYPE, static_cast<uint8_t>(veh_type), 0);
  }
  void add_volume(uint64_t volume) {
    fbb_.AddElement<uint64_t>(DF_TrafficFlowStat::VT_VOLUME, volume, 18446744073709551615ULL);
  }
  void add_speed_point(uint32_t speed_point) {
    fbb_.AddElement<uint32_t>(DF_TrafficFlowStat::VT_SPEED_POINT, speed_point, 4294967295);
  }
  void add_speed_area(uint32_t speed_area) {
    fbb_.AddElement<uint32_t>(DF_TrafficFlowStat::VT_SPEED_AREA, speed_area, 4294967295);
  }
  void add_density(uint64_t density) {
    fbb_.AddElement<uint64_t>(DF_TrafficFlowStat::VT_DENSITY, density, 18446744073709551615ULL);
  }
  void add_delay(uint16_t delay) {
    fbb_.AddElement<uint16_t>(DF_TrafficFlowStat::VT_DELAY, delay, 65535);
  }
  void add_queue_length(uint16_t queue_length) {
    fbb_.AddElement<uint16_t>(DF_TrafficFlowStat::VT_QUEUE_LENGTH, queue_length, 65535);
  }
  void add_congestion(uint8_t congestion) {
    fbb_.AddElement<uint8_t>(DF_TrafficFlowStat::VT_CONGESTION, congestion, 255);
  }
  void add_ext(::flatbuffers::Offset<MECData::DF_TrafficFlowStatExtension> ext) {
    fbb_.AddOffset(DF_TrafficFlowStat::VT_EXT, ext);
  }
  void add_occupation(uint16_t occupation) {
    fbb_.AddElement<uint16_t>(DF_TrafficFlowStat::VT_OCCUPATION, occupation, 65535);
  }
  void add_time_headway(uint32_t time_headway) {
    fbb_.AddElement<uint32_t>(DF_TrafficFlowStat::VT_TIME_HEADWAY, time_headway, 4294967295);
  }
  void add_space_headway(uint32_t space_headway) {
    fbb_.AddElement<uint32_t>(DF_TrafficFlowStat::VT_SPACE_HEADWAY, space_headway, 4294967295);
  }
  void add_travel_time(uint16_t travel_time) {
    fbb_.AddElement<uint16_t>(DF_TrafficFlowStat::VT_TRAVEL_TIME, travel_time, 65535);
  }
  void add_green_utilization(uint16_t green_utilization) {
    fbb_.AddElement<uint16_t>(DF_TrafficFlowStat::VT_GREEN_UTILIZATION, green_utilization, 65535);
  }
  void add_saturation(uint16_t saturation) {
    fbb_.AddElement<uint16_t>(DF_TrafficFlowStat::VT_SATURATION, saturation, 65535);
  }
  void add_stops(uint8_t stops) {
    fbb_.AddElement<uint8_t>(DF_TrafficFlowStat::VT_STOPS, stops, 255);
  }
  void add_queued_vehicles(uint16_t queued_vehicles) {
    fbb_.AddElement<uint16_t>(DF_TrafficFlowStat::VT_QUEUED_VEHICLES, queued_vehicles, 65535);
  }
  void add_time_occupation(uint16_t time_occupation) {
    fbb_.AddElement<uint16_t>(DF_TrafficFlowStat::VT_TIME_OCCUPATION, time_occupation, 65535);
  }
  explicit DF_TrafficFlowStatBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_TrafficFlowStat> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_TrafficFlowStat>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_TrafficFlowStat> CreateDF_TrafficFlowStat(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    MECData::DE_TrafficFlowStatMapElement map_element_type = MECData::DE_TrafficFlowStatMapElement_NONE,
    ::flatbuffers::Offset<void> map_element = 0,
    uint8_t ptc_type = 255,
    MECData::DE_BasicVehicleClass veh_type = MECData::DE_BasicVehicleClass_unknownVehicleClass,
    uint64_t volume = 18446744073709551615ULL,
    uint32_t speed_point = 4294967295,
    uint32_t speed_area = 4294967295,
    uint64_t density = 18446744073709551615ULL,
    uint16_t delay = 65535,
    uint16_t queue_length = 65535,
    uint8_t congestion = 255,
    ::flatbuffers::Offset<MECData::DF_TrafficFlowStatExtension> ext = 0,
    uint16_t occupation = 65535,
    uint32_t time_headway = 4294967295,
    uint32_t space_headway = 4294967295,
    uint16_t travel_time = 65535,
    uint16_t green_utilization = 65535,
    uint16_t saturation = 65535,
    uint8_t stops = 255,
    uint16_t queued_vehicles = 65535,
    uint16_t time_occupation = 65535) {
  DF_TrafficFlowStatBuilder builder_(_fbb);
  builder_.add_density(density);
  builder_.add_volume(volume);
  builder_.add_space_headway(space_headway);
  builder_.add_time_headway(time_headway);
  builder_.add_ext(ext);
  builder_.add_speed_area(speed_area);
  builder_.add_speed_point(speed_point);
  builder_.add_map_element(map_element);
  builder_.add_time_occupation(time_occupation);
  builder_.add_queued_vehicles(queued_vehicles);
  builder_.add_saturation(saturation);
  builder_.add_green_utilization(green_utilization);
  builder_.add_travel_time(travel_time);
  builder_.add_occupation(occupation);
  builder_.add_queue_length(queue_length);
  builder_.add_delay(delay);
  builder_.add_stops(stops);
  builder_.add_congestion(congestion);
  builder_.add_veh_type(veh_type);
  builder_.add_ptc_type(ptc_type);
  builder_.add_map_element_type(map_element_type);
  return builder_.Finish();
}

struct MSG_TrafficFlow FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_TrafficFlowBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NODE = 4,
    VT_GEN_TIME = 6,
    VT_STAT_TYPE_TYPE = 8,
    VT_STAT_TYPE = 10,
    VT_STATS = 12,
    VT_TIME_RECORDS = 14,
    VT_MSG_ID = 16
  };
  const MECData::DF_NodeReferenceID *node() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_NODE);
  }
  uint64_t gen_time() const {
    return GetField<uint64_t>(VT_GEN_TIME, 0);
  }
  MECData::DE_TrafficFlowStatType stat_type_type() const {
    return static_cast<MECData::DE_TrafficFlowStatType>(GetField<uint8_t>(VT_STAT_TYPE_TYPE, 0));
  }
  const void *stat_type() const {
    return GetPointer<const void *>(VT_STAT_TYPE);
  }
  template<typename T> const T *stat_type_as() const;
  const MECData::DE_TrafficFlowStatByInterval *stat_type_as_DE_TrafficFlowStatByInterval() const {
    return stat_type_type() == MECData::DE_TrafficFlowStatType_DE_TrafficFlowStatByInterval ? static_cast<const MECData::DE_TrafficFlowStatByInterval *>(stat_type()) : nullptr;
  }
  const MECData::DE_TrafficFlowStatBySignalCycle *stat_type_as_DE_TrafficFlowStatBySignalCycle() const {
    return stat_type_type() == MECData::DE_TrafficFlowStatType_DE_TrafficFlowStatBySignalCycle ? static_cast<const MECData::DE_TrafficFlowStatBySignalCycle *>(stat_type()) : nullptr;
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_TrafficFlowStat>> *stats() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_TrafficFlowStat>> *>(VT_STATS);
  }
  const MECData::DF_DebugTimeRecords *time_records() const {
    return GetPointer<const MECData::DF_DebugTimeRecords *>(VT_TIME_RECORDS);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NODE) &&
           verifier.VerifyTable(node()) &&
           VerifyField<uint64_t>(verifier, VT_GEN_TIME, 8) &&
           VerifyField<uint8_t>(verifier, VT_STAT_TYPE_TYPE, 1) &&
           VerifyOffsetRequired(verifier, VT_STAT_TYPE) &&
           VerifyDE_TrafficFlowStatType(verifier, stat_type(), stat_type_type()) &&
           VerifyOffset(verifier, VT_STATS) &&
           verifier.VerifyVector(stats()) &&
           verifier.VerifyVectorOfTables(stats()) &&
           VerifyOffset(verifier, VT_TIME_RECORDS) &&
           verifier.VerifyTable(time_records()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

template<> inline const MECData::DE_TrafficFlowStatByInterval *MSG_TrafficFlow::stat_type_as<MECData::DE_TrafficFlowStatByInterval>() const {
  return stat_type_as_DE_TrafficFlowStatByInterval();
}

template<> inline const MECData::DE_TrafficFlowStatBySignalCycle *MSG_TrafficFlow::stat_type_as<MECData::DE_TrafficFlowStatBySignalCycle>() const {
  return stat_type_as_DE_TrafficFlowStatBySignalCycle();
}

struct MSG_TrafficFlowBuilder {
  typedef MSG_TrafficFlow Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_node(::flatbuffers::Offset<MECData::DF_NodeReferenceID> node) {
    fbb_.AddOffset(MSG_TrafficFlow::VT_NODE, node);
  }
  void add_gen_time(uint64_t gen_time) {
    fbb_.AddElement<uint64_t>(MSG_TrafficFlow::VT_GEN_TIME, gen_time, 0);
  }
  void add_stat_type_type(MECData::DE_TrafficFlowStatType stat_type_type) {
    fbb_.AddElement<uint8_t>(MSG_TrafficFlow::VT_STAT_TYPE_TYPE, static_cast<uint8_t>(stat_type_type), 0);
  }
  void add_stat_type(::flatbuffers::Offset<void> stat_type) {
    fbb_.AddOffset(MSG_TrafficFlow::VT_STAT_TYPE, stat_type);
  }
  void add_stats(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_TrafficFlowStat>>> stats) {
    fbb_.AddOffset(MSG_TrafficFlow::VT_STATS, stats);
  }
  void add_time_records(::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records) {
    fbb_.AddOffset(MSG_TrafficFlow::VT_TIME_RECORDS, time_records);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_TrafficFlow::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_TrafficFlowBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_TrafficFlow> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_TrafficFlow>(end);
    fbb_.Required(o, MSG_TrafficFlow::VT_STAT_TYPE);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_TrafficFlow> CreateMSG_TrafficFlow(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> node = 0,
    uint64_t gen_time = 0,
    MECData::DE_TrafficFlowStatType stat_type_type = MECData::DE_TrafficFlowStatType_NONE,
    ::flatbuffers::Offset<void> stat_type = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_TrafficFlowStat>>> stats = 0,
    ::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records = 0,
    int64_t msg_id = 0) {
  MSG_TrafficFlowBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_gen_time(gen_time);
  builder_.add_time_records(time_records);
  builder_.add_stats(stats);
  builder_.add_stat_type(stat_type);
  builder_.add_node(node);
  builder_.add_stat_type_type(stat_type_type);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_TrafficFlow> CreateMSG_TrafficFlowDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> node = 0,
    uint64_t gen_time = 0,
    MECData::DE_TrafficFlowStatType stat_type_type = MECData::DE_TrafficFlowStatType_NONE,
    ::flatbuffers::Offset<void> stat_type = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_TrafficFlowStat>> *stats = nullptr,
    ::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records = 0,
    int64_t msg_id = 0) {
  auto stats__ = stats ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_TrafficFlowStat>>(*stats) : 0;
  return MECData::CreateMSG_TrafficFlow(
      _fbb,
      node,
      gen_time,
      stat_type_type,
      stat_type,
      stats__,
      time_records,
      msg_id);
}

inline bool VerifyDE_TrafficFlowStatMapElement(::flatbuffers::Verifier &verifier, const void *obj, DE_TrafficFlowStatMapElement type) {
  switch (type) {
    case DE_TrafficFlowStatMapElement_NONE: {
      return true;
    }
    case DE_TrafficFlowStatMapElement_DE_DetectorAreaStatInfo: {
      auto ptr = reinterpret_cast<const MECData::DE_DetectorAreaStatInfo *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DE_TrafficFlowStatMapElement_DE_LaneStatInfo: {
      auto ptr = reinterpret_cast<const MECData::DE_LaneStatInfo *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DE_TrafficFlowStatMapElement_DE_SectionStatInfo: {
      auto ptr = reinterpret_cast<const MECData::DE_SectionStatInfo *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DE_TrafficFlowStatMapElement_DE_LinkStatInfo: {
      auto ptr = reinterpret_cast<const MECData::DE_LinkStatInfo *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DE_TrafficFlowStatMapElement_DE_ConnectingLaneStatInfo: {
      auto ptr = reinterpret_cast<const MECData::DE_ConnectingLaneStatInfo *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DE_TrafficFlowStatMapElement_DE_ConnectionStatInfo: {
      auto ptr = reinterpret_cast<const MECData::DE_ConnectionStatInfo *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DE_TrafficFlowStatMapElement_DE_MovementStatInfo: {
      auto ptr = reinterpret_cast<const MECData::DE_MovementStatInfo *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DE_TrafficFlowStatMapElement_DE_NodeStatInfo: {
      auto ptr = reinterpret_cast<const MECData::DE_NodeStatInfo *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return true;
  }
}

inline bool VerifyDE_TrafficFlowStatMapElementVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (::flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyDE_TrafficFlowStatMapElement(
        verifier,  values->Get(i), types->GetEnum<DE_TrafficFlowStatMapElement>(i))) {
      return false;
    }
  }
  return true;
}

inline bool VerifyDE_TrafficFlowStatType(::flatbuffers::Verifier &verifier, const void *obj, DE_TrafficFlowStatType type) {
  switch (type) {
    case DE_TrafficFlowStatType_NONE: {
      return true;
    }
    case DE_TrafficFlowStatType_DE_TrafficFlowStatByInterval: {
      auto ptr = reinterpret_cast<const MECData::DE_TrafficFlowStatByInterval *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DE_TrafficFlowStatType_DE_TrafficFlowStatBySignalCycle: {
      auto ptr = reinterpret_cast<const MECData::DE_TrafficFlowStatBySignalCycle *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return true;
  }
}

inline bool VerifyDE_TrafficFlowStatTypeVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (::flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyDE_TrafficFlowStatType(
        verifier,  values->Get(i), types->GetEnum<DE_TrafficFlowStatType>(i))) {
      return false;
    }
  }
  return true;
}

inline const MECData::MSG_TrafficFlow *GetMSG_TrafficFlow(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_TrafficFlow>(buf);
}

inline const MECData::MSG_TrafficFlow *GetSizePrefixedMSG_TrafficFlow(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_TrafficFlow>(buf);
}

inline bool VerifyMSG_TrafficFlowBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_TrafficFlow>(nullptr);
}

inline bool VerifySizePrefixedMSG_TrafficFlowBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_TrafficFlow>(nullptr);
}

inline void FinishMSG_TrafficFlowBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_TrafficFlow> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_TrafficFlowBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_TrafficFlow> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_TRAFFICFLOW_MECDATA_H_
