// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_REQSTATUS_MECDATA_H_
#define FLATBUFFERS_GENERATED_REQSTATUS_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_ReqStatus : int8_t {
  DE_ReqStatus_unknown = 0,
  DE_ReqStatus_request = 1,
  DE_ReqStatus_comfirmed = 2,
  DE_ReqStatus_cancel = 3,
  DE_ReqStatus_complete = 4,
  DE_ReqStatus_MIN = DE_ReqStatus_unknown,
  DE_ReqStatus_MAX = DE_ReqStatus_complete
};

inline const DE_ReqStatus (&EnumValuesDE_ReqStatus())[5] {
  static const DE_ReqStatus values[] = {
    DE_ReqStatus_unknown,
    DE_ReqStatus_request,
    DE_ReqStatus_comfirmed,
    DE_ReqStatus_cancel,
    DE_ReqStatus_complete
  };
  return values;
}

inline const char * const *EnumNamesDE_ReqStatus() {
  static const char * const names[6] = {
    "unknown",
    "request",
    "comfirmed",
    "cancel",
    "complete",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_ReqStatus(DE_ReqStatus e) {
  if (::flatbuffers::IsOutRange(e, DE_ReqStatus_unknown, DE_ReqStatus_complete)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_ReqStatus()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_REQSTATUS_MECDATA_H_
