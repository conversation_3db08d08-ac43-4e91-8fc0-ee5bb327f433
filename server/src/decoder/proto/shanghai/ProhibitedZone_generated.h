// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_PROHIBITEDZONE_MECDATA_H_
#define FLATBUFFERS_GENERATED_PROHIBITEDZONE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "Polygon_generated.h"

namespace MECData {

struct DF_ProhibitedZone;
struct DF_ProhibitedZoneBuilder;

struct DF_ProhibitedZone FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ProhibitedZoneBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_CENTRALCIRCLEPRIHIBITEDZONE = 4,
    VT_NON_MOTORVEHICLEPROHIBITEDZONES = 6,
    VT_GRIDLINEMARKINGPROHIBITEDZONES = 8
  };
  const MECData::DF_Polygon *centralCirclePrihibitedZone() const {
    return GetPointer<const MECData::DF_Polygon *>(VT_CENTRALCIRCLEPRIHIBITEDZONE);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Polygon>> *non_motorVehicleProhibitedZones() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Polygon>> *>(VT_NON_MOTORVEHICLEPROHIBITEDZONES);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Polygon>> *gridLineMarkingProhibitedZones() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Polygon>> *>(VT_GRIDLINEMARKINGPROHIBITEDZONES);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_CENTRALCIRCLEPRIHIBITEDZONE) &&
           verifier.VerifyTable(centralCirclePrihibitedZone()) &&
           VerifyOffset(verifier, VT_NON_MOTORVEHICLEPROHIBITEDZONES) &&
           verifier.VerifyVector(non_motorVehicleProhibitedZones()) &&
           verifier.VerifyVectorOfTables(non_motorVehicleProhibitedZones()) &&
           VerifyOffset(verifier, VT_GRIDLINEMARKINGPROHIBITEDZONES) &&
           verifier.VerifyVector(gridLineMarkingProhibitedZones()) &&
           verifier.VerifyVectorOfTables(gridLineMarkingProhibitedZones()) &&
           verifier.EndTable();
  }
};

struct DF_ProhibitedZoneBuilder {
  typedef DF_ProhibitedZone Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_centralCirclePrihibitedZone(::flatbuffers::Offset<MECData::DF_Polygon> centralCirclePrihibitedZone) {
    fbb_.AddOffset(DF_ProhibitedZone::VT_CENTRALCIRCLEPRIHIBITEDZONE, centralCirclePrihibitedZone);
  }
  void add_non_motorVehicleProhibitedZones(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Polygon>>> non_motorVehicleProhibitedZones) {
    fbb_.AddOffset(DF_ProhibitedZone::VT_NON_MOTORVEHICLEPROHIBITEDZONES, non_motorVehicleProhibitedZones);
  }
  void add_gridLineMarkingProhibitedZones(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Polygon>>> gridLineMarkingProhibitedZones) {
    fbb_.AddOffset(DF_ProhibitedZone::VT_GRIDLINEMARKINGPROHIBITEDZONES, gridLineMarkingProhibitedZones);
  }
  explicit DF_ProhibitedZoneBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ProhibitedZone> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ProhibitedZone>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ProhibitedZone> CreateDF_ProhibitedZone(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_Polygon> centralCirclePrihibitedZone = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Polygon>>> non_motorVehicleProhibitedZones = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Polygon>>> gridLineMarkingProhibitedZones = 0) {
  DF_ProhibitedZoneBuilder builder_(_fbb);
  builder_.add_gridLineMarkingProhibitedZones(gridLineMarkingProhibitedZones);
  builder_.add_non_motorVehicleProhibitedZones(non_motorVehicleProhibitedZones);
  builder_.add_centralCirclePrihibitedZone(centralCirclePrihibitedZone);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ProhibitedZone> CreateDF_ProhibitedZoneDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_Polygon> centralCirclePrihibitedZone = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_Polygon>> *non_motorVehicleProhibitedZones = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_Polygon>> *gridLineMarkingProhibitedZones = nullptr) {
  auto non_motorVehicleProhibitedZones__ = non_motorVehicleProhibitedZones ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_Polygon>>(*non_motorVehicleProhibitedZones) : 0;
  auto gridLineMarkingProhibitedZones__ = gridLineMarkingProhibitedZones ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_Polygon>>(*gridLineMarkingProhibitedZones) : 0;
  return MECData::CreateDF_ProhibitedZone(
      _fbb,
      centralCirclePrihibitedZone,
      non_motorVehicleProhibitedZones__,
      gridLineMarkingProhibitedZones__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_PROHIBITEDZONE_MECDATA_H_
