// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_WILDCARD_MECDATA_H_
#define FLATBUFFERS_GENERATED_WILDCARD_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct MSG_WildCard;
struct MSG_WildCardBuilder;

struct MSG_WildCard FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_WildCardBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_DEST_APP_IDS = 4,
    VT_DEST_DEV_IDS = 6,
    VT_VERIFY_CODE = 8,
    VT_DATA = 10,
    VT_MSG_ID = 12
  };
  const ::flatbuffers::Vector<uint16_t> *dest_app_ids() const {
    return GetPointer<const ::flatbuffers::Vector<uint16_t> *>(VT_DEST_APP_IDS);
  }
  const ::flatbuffers::Vector<uint16_t> *dest_dev_ids() const {
    return GetPointer<const ::flatbuffers::Vector<uint16_t> *>(VT_DEST_DEV_IDS);
  }
  uint8_t verify_code() const {
    return GetField<uint8_t>(VT_VERIFY_CODE, 0);
  }
  const ::flatbuffers::Vector<uint8_t> *data() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_DATA);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_DEST_APP_IDS) &&
           verifier.VerifyVector(dest_app_ids()) &&
           VerifyOffset(verifier, VT_DEST_DEV_IDS) &&
           verifier.VerifyVector(dest_dev_ids()) &&
           VerifyField<uint8_t>(verifier, VT_VERIFY_CODE, 1) &&
           VerifyOffset(verifier, VT_DATA) &&
           verifier.VerifyVector(data()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct MSG_WildCardBuilder {
  typedef MSG_WildCard Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_dest_app_ids(::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> dest_app_ids) {
    fbb_.AddOffset(MSG_WildCard::VT_DEST_APP_IDS, dest_app_ids);
  }
  void add_dest_dev_ids(::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> dest_dev_ids) {
    fbb_.AddOffset(MSG_WildCard::VT_DEST_DEV_IDS, dest_dev_ids);
  }
  void add_verify_code(uint8_t verify_code) {
    fbb_.AddElement<uint8_t>(MSG_WildCard::VT_VERIFY_CODE, verify_code, 0);
  }
  void add_data(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> data) {
    fbb_.AddOffset(MSG_WildCard::VT_DATA, data);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_WildCard::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_WildCardBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_WildCard> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_WildCard>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_WildCard> CreateMSG_WildCard(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> dest_app_ids = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> dest_dev_ids = 0,
    uint8_t verify_code = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> data = 0,
    int64_t msg_id = 0) {
  MSG_WildCardBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_data(data);
  builder_.add_dest_dev_ids(dest_dev_ids);
  builder_.add_dest_app_ids(dest_app_ids);
  builder_.add_verify_code(verify_code);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_WildCard> CreateMSG_WildCardDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint16_t> *dest_app_ids = nullptr,
    const std::vector<uint16_t> *dest_dev_ids = nullptr,
    uint8_t verify_code = 0,
    const std::vector<uint8_t> *data = nullptr,
    int64_t msg_id = 0) {
  auto dest_app_ids__ = dest_app_ids ? _fbb.CreateVector<uint16_t>(*dest_app_ids) : 0;
  auto dest_dev_ids__ = dest_dev_ids ? _fbb.CreateVector<uint16_t>(*dest_dev_ids) : 0;
  auto data__ = data ? _fbb.CreateVector<uint8_t>(*data) : 0;
  return MECData::CreateMSG_WildCard(
      _fbb,
      dest_app_ids__,
      dest_dev_ids__,
      verify_code,
      data__,
      msg_id);
}

inline const MECData::MSG_WildCard *GetMSG_WildCard(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_WildCard>(buf);
}

inline const MECData::MSG_WildCard *GetSizePrefixedMSG_WildCard(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_WildCard>(buf);
}

inline bool VerifyMSG_WildCardBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_WildCard>(nullptr);
}

inline bool VerifySizePrefixedMSG_WildCardBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_WildCard>(nullptr);
}

inline void FinishMSG_WildCardBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_WildCard> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_WildCardBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_WildCard> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_WILDCARD_MECDATA_H_
