// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_REFERPOSITION_MECDATA_H_
#define FLATBUFFERS_GENERATED_REFERPOSITION_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_ReferPosition;
struct DF_ReferPositionBuilder;

struct DF_ReferPosition FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ReferPositionBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_DEVICEID = 4,
    VT_POSITIONX = 6,
    VT_POSITIONY = 8
  };
  uint16_t deviceId() const {
    return GetField<uint16_t>(VT_DEVICEID, 0);
  }
  int16_t positionX() const {
    return GetField<int16_t>(VT_POSITIONX, 0);
  }
  int16_t positionY() const {
    return GetField<int16_t>(VT_POSITIONY, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_DEVICEID, 2) &&
           VerifyField<int16_t>(verifier, VT_POSITIONX, 2) &&
           VerifyField<int16_t>(verifier, VT_POSITIONY, 2) &&
           verifier.EndTable();
  }
};

struct DF_ReferPositionBuilder {
  typedef DF_ReferPosition Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_deviceId(uint16_t deviceId) {
    fbb_.AddElement<uint16_t>(DF_ReferPosition::VT_DEVICEID, deviceId, 0);
  }
  void add_positionX(int16_t positionX) {
    fbb_.AddElement<int16_t>(DF_ReferPosition::VT_POSITIONX, positionX, 0);
  }
  void add_positionY(int16_t positionY) {
    fbb_.AddElement<int16_t>(DF_ReferPosition::VT_POSITIONY, positionY, 0);
  }
  explicit DF_ReferPositionBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ReferPosition> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ReferPosition>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ReferPosition> CreateDF_ReferPosition(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t deviceId = 0,
    int16_t positionX = 0,
    int16_t positionY = 0) {
  DF_ReferPositionBuilder builder_(_fbb);
  builder_.add_positionY(positionY);
  builder_.add_positionX(positionX);
  builder_.add_deviceId(deviceId);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_REFERPOSITION_MECDATA_H_
