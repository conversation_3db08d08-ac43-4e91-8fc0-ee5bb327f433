// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_REQSENSORSHARING_MECDATA_H_
#define FLATBUFFERS_GENERATED_REQSENSORSHARING_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "ReferencePath_generated.h"

namespace MECData {

struct DF_ReqSensorSharing;
struct DF_ReqSensorSharingBuilder;

struct DF_ReqSensorSharing FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ReqSensorSharingBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_DETECTAREA = 4
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferencePath>> *detectArea() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferencePath>> *>(VT_DETECTAREA);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_DETECTAREA) &&
           verifier.VerifyVector(detectArea()) &&
           verifier.VerifyVectorOfTables(detectArea()) &&
           verifier.EndTable();
  }
};

struct DF_ReqSensorSharingBuilder {
  typedef DF_ReqSensorSharing Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_detectArea(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferencePath>>> detectArea) {
    fbb_.AddOffset(DF_ReqSensorSharing::VT_DETECTAREA, detectArea);
  }
  explicit DF_ReqSensorSharingBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ReqSensorSharing> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ReqSensorSharing>(end);
    fbb_.Required(o, DF_ReqSensorSharing::VT_DETECTAREA);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ReqSensorSharing> CreateDF_ReqSensorSharing(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferencePath>>> detectArea = 0) {
  DF_ReqSensorSharingBuilder builder_(_fbb);
  builder_.add_detectArea(detectArea);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ReqSensorSharing> CreateDF_ReqSensorSharingDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<MECData::DF_ReferencePath>> *detectArea = nullptr) {
  auto detectArea__ = detectArea ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_ReferencePath>>(*detectArea) : 0;
  return MECData::CreateDF_ReqSensorSharing(
      _fbb,
      detectArea__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_REQSENSORSHARING_MECDATA_H_
