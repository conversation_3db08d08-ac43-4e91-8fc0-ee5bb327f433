// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_ZBRSFEATURES_MECDATA_H_
#define FLATBUFFERS_GENERATED_ZBRSFEATURES_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "ErrorLevel_generated.h"
#include "LogConfig_generated.h"
#include "MapCollection_generated.h"
#include "Mileage_generated.h"
#include "OperationTags_generated.h"
#include "PacketFlowRecord_generated.h"
#include "Position3D_generated.h"
#include "SSLCertificate_generated.h"
#include "Snowflake_generated.h"
#include "ZStub_generated.h"

namespace MECData {

struct DF_ZBRSVersion;
struct DF_ZBRSVersionBuilder;

struct DF_PacketRoute;
struct DF_PacketRouteBuilder;

struct DF_OTAServerConfig;
struct DF_OTAServerConfigBuilder;

struct DF_DockerRegistryConfig;
struct DF_DockerRegistryConfigBuilder;

struct DE_CrashReportServerConfig;
struct DE_CrashReportServerConfigBuilder;

struct DF_CrashReportConfig;
struct DF_CrashReportConfigBuilder;

struct DE_SystemGPUToolchainEnv;
struct DE_SystemGPUToolchainEnvBuilder;

struct DF_ZBRSEnvironment;
struct DF_ZBRSEnvironmentBuilder;

struct DF_ZBRSLocalMachineConfig;
struct DF_ZBRSLocalMachineConfigBuilder;

struct MSG_ZBRSFeatures;
struct MSG_ZBRSFeaturesBuilder;

enum DE_SystemArchitecture : int8_t {
  DE_SystemArchitecture_X86_64 = 0,
  DE_SystemArchitecture_AARCH64 = 1,
  DE_SystemArchitecture_MIN = DE_SystemArchitecture_X86_64,
  DE_SystemArchitecture_MAX = DE_SystemArchitecture_AARCH64
};

inline const DE_SystemArchitecture (&EnumValuesDE_SystemArchitecture())[2] {
  static const DE_SystemArchitecture values[] = {
    DE_SystemArchitecture_X86_64,
    DE_SystemArchitecture_AARCH64
  };
  return values;
}

inline const char * const *EnumNamesDE_SystemArchitecture() {
  static const char * const names[3] = {
    "X86_64",
    "AARCH64",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_SystemArchitecture(DE_SystemArchitecture e) {
  if (::flatbuffers::IsOutRange(e, DE_SystemArchitecture_X86_64, DE_SystemArchitecture_AARCH64)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_SystemArchitecture()[index];
}

struct DF_ZBRSVersion FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ZBRSVersionBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MAJOR = 4,
    VT_MINOR = 6,
    VT_PATCH = 8,
    VT_PRE_RELEASE = 10,
    VT_BUILD_DATE = 12
  };
  uint16_t major() const {
    return GetField<uint16_t>(VT_MAJOR, 0);
  }
  uint16_t minor() const {
    return GetField<uint16_t>(VT_MINOR, 0);
  }
  uint16_t patch() const {
    return GetField<uint16_t>(VT_PATCH, 0);
  }
  const ::flatbuffers::String *pre_release() const {
    return GetPointer<const ::flatbuffers::String *>(VT_PRE_RELEASE);
  }
  const ::flatbuffers::String *build_date() const {
    return GetPointer<const ::flatbuffers::String *>(VT_BUILD_DATE);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_MAJOR, 2) &&
           VerifyField<uint16_t>(verifier, VT_MINOR, 2) &&
           VerifyField<uint16_t>(verifier, VT_PATCH, 2) &&
           VerifyOffset(verifier, VT_PRE_RELEASE) &&
           verifier.VerifyString(pre_release()) &&
           VerifyOffset(verifier, VT_BUILD_DATE) &&
           verifier.VerifyString(build_date()) &&
           verifier.EndTable();
  }
};

struct DF_ZBRSVersionBuilder {
  typedef DF_ZBRSVersion Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_major(uint16_t major) {
    fbb_.AddElement<uint16_t>(DF_ZBRSVersion::VT_MAJOR, major, 0);
  }
  void add_minor(uint16_t minor) {
    fbb_.AddElement<uint16_t>(DF_ZBRSVersion::VT_MINOR, minor, 0);
  }
  void add_patch(uint16_t patch) {
    fbb_.AddElement<uint16_t>(DF_ZBRSVersion::VT_PATCH, patch, 0);
  }
  void add_pre_release(::flatbuffers::Offset<::flatbuffers::String> pre_release) {
    fbb_.AddOffset(DF_ZBRSVersion::VT_PRE_RELEASE, pre_release);
  }
  void add_build_date(::flatbuffers::Offset<::flatbuffers::String> build_date) {
    fbb_.AddOffset(DF_ZBRSVersion::VT_BUILD_DATE, build_date);
  }
  explicit DF_ZBRSVersionBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ZBRSVersion> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ZBRSVersion>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ZBRSVersion> CreateDF_ZBRSVersion(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t major = 0,
    uint16_t minor = 0,
    uint16_t patch = 0,
    ::flatbuffers::Offset<::flatbuffers::String> pre_release = 0,
    ::flatbuffers::Offset<::flatbuffers::String> build_date = 0) {
  DF_ZBRSVersionBuilder builder_(_fbb);
  builder_.add_build_date(build_date);
  builder_.add_pre_release(pre_release);
  builder_.add_patch(patch);
  builder_.add_minor(minor);
  builder_.add_major(major);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ZBRSVersion> CreateDF_ZBRSVersionDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t major = 0,
    uint16_t minor = 0,
    uint16_t patch = 0,
    const char *pre_release = nullptr,
    const char *build_date = nullptr) {
  auto pre_release__ = pre_release ? _fbb.CreateString(pre_release) : 0;
  auto build_date__ = build_date ? _fbb.CreateString(build_date) : 0;
  return MECData::CreateDF_ZBRSVersion(
      _fbb,
      major,
      minor,
      patch,
      pre_release__,
      build_date__);
}

struct DF_PacketRoute FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_PacketRouteBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MEC_DATA_TYPE = 4,
    VT_SRC_MODULE_ID = 6,
    VT_DST_MODULE_IDS = 8
  };
  uint16_t mec_data_type() const {
    return GetField<uint16_t>(VT_MEC_DATA_TYPE, 0);
  }
  uint16_t src_module_id() const {
    return GetField<uint16_t>(VT_SRC_MODULE_ID, 0);
  }
  const ::flatbuffers::Vector<uint16_t> *dst_module_ids() const {
    return GetPointer<const ::flatbuffers::Vector<uint16_t> *>(VT_DST_MODULE_IDS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_MEC_DATA_TYPE, 2) &&
           VerifyField<uint16_t>(verifier, VT_SRC_MODULE_ID, 2) &&
           VerifyOffset(verifier, VT_DST_MODULE_IDS) &&
           verifier.VerifyVector(dst_module_ids()) &&
           verifier.EndTable();
  }
};

struct DF_PacketRouteBuilder {
  typedef DF_PacketRoute Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_mec_data_type(uint16_t mec_data_type) {
    fbb_.AddElement<uint16_t>(DF_PacketRoute::VT_MEC_DATA_TYPE, mec_data_type, 0);
  }
  void add_src_module_id(uint16_t src_module_id) {
    fbb_.AddElement<uint16_t>(DF_PacketRoute::VT_SRC_MODULE_ID, src_module_id, 0);
  }
  void add_dst_module_ids(::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> dst_module_ids) {
    fbb_.AddOffset(DF_PacketRoute::VT_DST_MODULE_IDS, dst_module_ids);
  }
  explicit DF_PacketRouteBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_PacketRoute> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_PacketRoute>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_PacketRoute> CreateDF_PacketRoute(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t mec_data_type = 0,
    uint16_t src_module_id = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> dst_module_ids = 0) {
  DF_PacketRouteBuilder builder_(_fbb);
  builder_.add_dst_module_ids(dst_module_ids);
  builder_.add_src_module_id(src_module_id);
  builder_.add_mec_data_type(mec_data_type);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_PacketRoute> CreateDF_PacketRouteDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t mec_data_type = 0,
    uint16_t src_module_id = 0,
    const std::vector<uint16_t> *dst_module_ids = nullptr) {
  auto dst_module_ids__ = dst_module_ids ? _fbb.CreateVector<uint16_t>(*dst_module_ids) : 0;
  return MECData::CreateDF_PacketRoute(
      _fbb,
      mec_data_type,
      src_module_id,
      dst_module_ids__);
}

struct DF_OTAServerConfig FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_OTAServerConfigBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_OTA_HOST = 4,
    VT_OTA_PORT = 6,
    VT_USERNAME = 8,
    VT_PASSWORD = 10
  };
  const ::flatbuffers::String *ota_host() const {
    return GetPointer<const ::flatbuffers::String *>(VT_OTA_HOST);
  }
  uint16_t ota_port() const {
    return GetField<uint16_t>(VT_OTA_PORT, 0);
  }
  const ::flatbuffers::String *username() const {
    return GetPointer<const ::flatbuffers::String *>(VT_USERNAME);
  }
  const ::flatbuffers::String *password() const {
    return GetPointer<const ::flatbuffers::String *>(VT_PASSWORD);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_OTA_HOST) &&
           verifier.VerifyString(ota_host()) &&
           VerifyField<uint16_t>(verifier, VT_OTA_PORT, 2) &&
           VerifyOffset(verifier, VT_USERNAME) &&
           verifier.VerifyString(username()) &&
           VerifyOffset(verifier, VT_PASSWORD) &&
           verifier.VerifyString(password()) &&
           verifier.EndTable();
  }
};

struct DF_OTAServerConfigBuilder {
  typedef DF_OTAServerConfig Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_ota_host(::flatbuffers::Offset<::flatbuffers::String> ota_host) {
    fbb_.AddOffset(DF_OTAServerConfig::VT_OTA_HOST, ota_host);
  }
  void add_ota_port(uint16_t ota_port) {
    fbb_.AddElement<uint16_t>(DF_OTAServerConfig::VT_OTA_PORT, ota_port, 0);
  }
  void add_username(::flatbuffers::Offset<::flatbuffers::String> username) {
    fbb_.AddOffset(DF_OTAServerConfig::VT_USERNAME, username);
  }
  void add_password(::flatbuffers::Offset<::flatbuffers::String> password) {
    fbb_.AddOffset(DF_OTAServerConfig::VT_PASSWORD, password);
  }
  explicit DF_OTAServerConfigBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_OTAServerConfig> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_OTAServerConfig>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_OTAServerConfig> CreateDF_OTAServerConfig(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> ota_host = 0,
    uint16_t ota_port = 0,
    ::flatbuffers::Offset<::flatbuffers::String> username = 0,
    ::flatbuffers::Offset<::flatbuffers::String> password = 0) {
  DF_OTAServerConfigBuilder builder_(_fbb);
  builder_.add_password(password);
  builder_.add_username(username);
  builder_.add_ota_host(ota_host);
  builder_.add_ota_port(ota_port);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_OTAServerConfig> CreateDF_OTAServerConfigDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *ota_host = nullptr,
    uint16_t ota_port = 0,
    const char *username = nullptr,
    const char *password = nullptr) {
  auto ota_host__ = ota_host ? _fbb.CreateString(ota_host) : 0;
  auto username__ = username ? _fbb.CreateString(username) : 0;
  auto password__ = password ? _fbb.CreateString(password) : 0;
  return MECData::CreateDF_OTAServerConfig(
      _fbb,
      ota_host__,
      ota_port,
      username__,
      password__);
}

struct DF_DockerRegistryConfig FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_DockerRegistryConfigBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_REGISTRY_HOST = 4,
    VT_REGISTRY_PORT = 6,
    VT_SECURE = 8,
    VT_USERNAME = 10,
    VT_PASSWORD = 12
  };
  const ::flatbuffers::String *registry_host() const {
    return GetPointer<const ::flatbuffers::String *>(VT_REGISTRY_HOST);
  }
  uint16_t registry_port() const {
    return GetField<uint16_t>(VT_REGISTRY_PORT, 0);
  }
  bool secure() const {
    return GetField<uint8_t>(VT_SECURE, 0) != 0;
  }
  const ::flatbuffers::String *username() const {
    return GetPointer<const ::flatbuffers::String *>(VT_USERNAME);
  }
  const ::flatbuffers::String *password() const {
    return GetPointer<const ::flatbuffers::String *>(VT_PASSWORD);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_REGISTRY_HOST) &&
           verifier.VerifyString(registry_host()) &&
           VerifyField<uint16_t>(verifier, VT_REGISTRY_PORT, 2) &&
           VerifyField<uint8_t>(verifier, VT_SECURE, 1) &&
           VerifyOffset(verifier, VT_USERNAME) &&
           verifier.VerifyString(username()) &&
           VerifyOffset(verifier, VT_PASSWORD) &&
           verifier.VerifyString(password()) &&
           verifier.EndTable();
  }
};

struct DF_DockerRegistryConfigBuilder {
  typedef DF_DockerRegistryConfig Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_registry_host(::flatbuffers::Offset<::flatbuffers::String> registry_host) {
    fbb_.AddOffset(DF_DockerRegistryConfig::VT_REGISTRY_HOST, registry_host);
  }
  void add_registry_port(uint16_t registry_port) {
    fbb_.AddElement<uint16_t>(DF_DockerRegistryConfig::VT_REGISTRY_PORT, registry_port, 0);
  }
  void add_secure(bool secure) {
    fbb_.AddElement<uint8_t>(DF_DockerRegistryConfig::VT_SECURE, static_cast<uint8_t>(secure), 0);
  }
  void add_username(::flatbuffers::Offset<::flatbuffers::String> username) {
    fbb_.AddOffset(DF_DockerRegistryConfig::VT_USERNAME, username);
  }
  void add_password(::flatbuffers::Offset<::flatbuffers::String> password) {
    fbb_.AddOffset(DF_DockerRegistryConfig::VT_PASSWORD, password);
  }
  explicit DF_DockerRegistryConfigBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_DockerRegistryConfig> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_DockerRegistryConfig>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_DockerRegistryConfig> CreateDF_DockerRegistryConfig(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> registry_host = 0,
    uint16_t registry_port = 0,
    bool secure = false,
    ::flatbuffers::Offset<::flatbuffers::String> username = 0,
    ::flatbuffers::Offset<::flatbuffers::String> password = 0) {
  DF_DockerRegistryConfigBuilder builder_(_fbb);
  builder_.add_password(password);
  builder_.add_username(username);
  builder_.add_registry_host(registry_host);
  builder_.add_registry_port(registry_port);
  builder_.add_secure(secure);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_DockerRegistryConfig> CreateDF_DockerRegistryConfigDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *registry_host = nullptr,
    uint16_t registry_port = 0,
    bool secure = false,
    const char *username = nullptr,
    const char *password = nullptr) {
  auto registry_host__ = registry_host ? _fbb.CreateString(registry_host) : 0;
  auto username__ = username ? _fbb.CreateString(username) : 0;
  auto password__ = password ? _fbb.CreateString(password) : 0;
  return MECData::CreateDF_DockerRegistryConfig(
      _fbb,
      registry_host__,
      registry_port,
      secure,
      username__,
      password__);
}

struct DE_CrashReportServerConfig FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_CrashReportServerConfigBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SERVER_HOST = 4,
    VT_SERVER_PORT = 6
  };
  const ::flatbuffers::String *server_host() const {
    return GetPointer<const ::flatbuffers::String *>(VT_SERVER_HOST);
  }
  uint16_t server_port() const {
    return GetField<uint16_t>(VT_SERVER_PORT, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_SERVER_HOST) &&
           verifier.VerifyString(server_host()) &&
           VerifyField<uint16_t>(verifier, VT_SERVER_PORT, 2) &&
           verifier.EndTable();
  }
};

struct DE_CrashReportServerConfigBuilder {
  typedef DE_CrashReportServerConfig Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_server_host(::flatbuffers::Offset<::flatbuffers::String> server_host) {
    fbb_.AddOffset(DE_CrashReportServerConfig::VT_SERVER_HOST, server_host);
  }
  void add_server_port(uint16_t server_port) {
    fbb_.AddElement<uint16_t>(DE_CrashReportServerConfig::VT_SERVER_PORT, server_port, 0);
  }
  explicit DE_CrashReportServerConfigBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_CrashReportServerConfig> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_CrashReportServerConfig>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_CrashReportServerConfig> CreateDE_CrashReportServerConfig(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> server_host = 0,
    uint16_t server_port = 0) {
  DE_CrashReportServerConfigBuilder builder_(_fbb);
  builder_.add_server_host(server_host);
  builder_.add_server_port(server_port);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DE_CrashReportServerConfig> CreateDE_CrashReportServerConfigDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *server_host = nullptr,
    uint16_t server_port = 0) {
  auto server_host__ = server_host ? _fbb.CreateString(server_host) : 0;
  return MECData::CreateDE_CrashReportServerConfig(
      _fbb,
      server_host__,
      server_port);
}

struct DF_CrashReportConfig FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_CrashReportConfigBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_CRASH_REPORT_SERVER_CONFIG = 4,
    VT_COREDUMP_PATH = 6,
    VT_MAX_DAYS_TO_KEEP = 8
  };
  const MECData::DE_CrashReportServerConfig *crash_report_server_config() const {
    return GetPointer<const MECData::DE_CrashReportServerConfig *>(VT_CRASH_REPORT_SERVER_CONFIG);
  }
  const ::flatbuffers::String *coredump_path() const {
    return GetPointer<const ::flatbuffers::String *>(VT_COREDUMP_PATH);
  }
  uint16_t max_days_to_keep() const {
    return GetField<uint16_t>(VT_MAX_DAYS_TO_KEEP, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_CRASH_REPORT_SERVER_CONFIG) &&
           verifier.VerifyTable(crash_report_server_config()) &&
           VerifyOffset(verifier, VT_COREDUMP_PATH) &&
           verifier.VerifyString(coredump_path()) &&
           VerifyField<uint16_t>(verifier, VT_MAX_DAYS_TO_KEEP, 2) &&
           verifier.EndTable();
  }
};

struct DF_CrashReportConfigBuilder {
  typedef DF_CrashReportConfig Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_crash_report_server_config(::flatbuffers::Offset<MECData::DE_CrashReportServerConfig> crash_report_server_config) {
    fbb_.AddOffset(DF_CrashReportConfig::VT_CRASH_REPORT_SERVER_CONFIG, crash_report_server_config);
  }
  void add_coredump_path(::flatbuffers::Offset<::flatbuffers::String> coredump_path) {
    fbb_.AddOffset(DF_CrashReportConfig::VT_COREDUMP_PATH, coredump_path);
  }
  void add_max_days_to_keep(uint16_t max_days_to_keep) {
    fbb_.AddElement<uint16_t>(DF_CrashReportConfig::VT_MAX_DAYS_TO_KEEP, max_days_to_keep, 0);
  }
  explicit DF_CrashReportConfigBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_CrashReportConfig> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_CrashReportConfig>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_CrashReportConfig> CreateDF_CrashReportConfig(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DE_CrashReportServerConfig> crash_report_server_config = 0,
    ::flatbuffers::Offset<::flatbuffers::String> coredump_path = 0,
    uint16_t max_days_to_keep = 0) {
  DF_CrashReportConfigBuilder builder_(_fbb);
  builder_.add_coredump_path(coredump_path);
  builder_.add_crash_report_server_config(crash_report_server_config);
  builder_.add_max_days_to_keep(max_days_to_keep);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_CrashReportConfig> CreateDF_CrashReportConfigDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DE_CrashReportServerConfig> crash_report_server_config = 0,
    const char *coredump_path = nullptr,
    uint16_t max_days_to_keep = 0) {
  auto coredump_path__ = coredump_path ? _fbb.CreateString(coredump_path) : 0;
  return MECData::CreateDF_CrashReportConfig(
      _fbb,
      crash_report_server_config,
      coredump_path__,
      max_days_to_keep);
}

struct DE_SystemGPUToolchainEnv FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_SystemGPUToolchainEnvBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_CUDA_VERSION = 4,
    VT_CUDNN_VERSION = 6
  };
  const ::flatbuffers::String *cuda_version() const {
    return GetPointer<const ::flatbuffers::String *>(VT_CUDA_VERSION);
  }
  const ::flatbuffers::String *cudnn_version() const {
    return GetPointer<const ::flatbuffers::String *>(VT_CUDNN_VERSION);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_CUDA_VERSION) &&
           verifier.VerifyString(cuda_version()) &&
           VerifyOffset(verifier, VT_CUDNN_VERSION) &&
           verifier.VerifyString(cudnn_version()) &&
           verifier.EndTable();
  }
};

struct DE_SystemGPUToolchainEnvBuilder {
  typedef DE_SystemGPUToolchainEnv Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_cuda_version(::flatbuffers::Offset<::flatbuffers::String> cuda_version) {
    fbb_.AddOffset(DE_SystemGPUToolchainEnv::VT_CUDA_VERSION, cuda_version);
  }
  void add_cudnn_version(::flatbuffers::Offset<::flatbuffers::String> cudnn_version) {
    fbb_.AddOffset(DE_SystemGPUToolchainEnv::VT_CUDNN_VERSION, cudnn_version);
  }
  explicit DE_SystemGPUToolchainEnvBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_SystemGPUToolchainEnv> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_SystemGPUToolchainEnv>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_SystemGPUToolchainEnv> CreateDE_SystemGPUToolchainEnv(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> cuda_version = 0,
    ::flatbuffers::Offset<::flatbuffers::String> cudnn_version = 0) {
  DE_SystemGPUToolchainEnvBuilder builder_(_fbb);
  builder_.add_cudnn_version(cudnn_version);
  builder_.add_cuda_version(cuda_version);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DE_SystemGPUToolchainEnv> CreateDE_SystemGPUToolchainEnvDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *cuda_version = nullptr,
    const char *cudnn_version = nullptr) {
  auto cuda_version__ = cuda_version ? _fbb.CreateString(cuda_version) : 0;
  auto cudnn_version__ = cudnn_version ? _fbb.CreateString(cudnn_version) : 0;
  return MECData::CreateDE_SystemGPUToolchainEnv(
      _fbb,
      cuda_version__,
      cudnn_version__);
}

struct DF_ZBRSEnvironment FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ZBRSEnvironmentBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ZBRS_VERSION = 4,
    VT_INTERNAL_MODULES = 6,
    VT_SYSTEM_ARCH = 8,
    VT_GPU_TOOLCHAIN = 10,
    VT_GLIBC_VERSION = 12,
    VT_DOCKER_API_VERSION = 14,
    VT_INSTALLED_APPS = 16
  };
  const MECData::DF_ZBRSVersion *zbrs_version() const {
    return GetPointer<const MECData::DF_ZBRSVersion *>(VT_ZBRS_VERSION);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *internal_modules() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *>(VT_INTERNAL_MODULES);
  }
  MECData::DE_SystemArchitecture system_arch() const {
    return static_cast<MECData::DE_SystemArchitecture>(GetField<int8_t>(VT_SYSTEM_ARCH, 0));
  }
  const MECData::DE_SystemGPUToolchainEnv *gpu_toolchain() const {
    return GetPointer<const MECData::DE_SystemGPUToolchainEnv *>(VT_GPU_TOOLCHAIN);
  }
  const ::flatbuffers::String *glibc_version() const {
    return GetPointer<const ::flatbuffers::String *>(VT_GLIBC_VERSION);
  }
  const ::flatbuffers::String *docker_api_version() const {
    return GetPointer<const ::flatbuffers::String *>(VT_DOCKER_API_VERSION);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *installed_apps() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *>(VT_INSTALLED_APPS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_ZBRS_VERSION) &&
           verifier.VerifyTable(zbrs_version()) &&
           VerifyOffset(verifier, VT_INTERNAL_MODULES) &&
           verifier.VerifyVector(internal_modules()) &&
           verifier.VerifyVectorOfStrings(internal_modules()) &&
           VerifyField<int8_t>(verifier, VT_SYSTEM_ARCH, 1) &&
           VerifyOffset(verifier, VT_GPU_TOOLCHAIN) &&
           verifier.VerifyTable(gpu_toolchain()) &&
           VerifyOffset(verifier, VT_GLIBC_VERSION) &&
           verifier.VerifyString(glibc_version()) &&
           VerifyOffset(verifier, VT_DOCKER_API_VERSION) &&
           verifier.VerifyString(docker_api_version()) &&
           VerifyOffset(verifier, VT_INSTALLED_APPS) &&
           verifier.VerifyVector(installed_apps()) &&
           verifier.VerifyVectorOfStrings(installed_apps()) &&
           verifier.EndTable();
  }
};

struct DF_ZBRSEnvironmentBuilder {
  typedef DF_ZBRSEnvironment Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_zbrs_version(::flatbuffers::Offset<MECData::DF_ZBRSVersion> zbrs_version) {
    fbb_.AddOffset(DF_ZBRSEnvironment::VT_ZBRS_VERSION, zbrs_version);
  }
  void add_internal_modules(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> internal_modules) {
    fbb_.AddOffset(DF_ZBRSEnvironment::VT_INTERNAL_MODULES, internal_modules);
  }
  void add_system_arch(MECData::DE_SystemArchitecture system_arch) {
    fbb_.AddElement<int8_t>(DF_ZBRSEnvironment::VT_SYSTEM_ARCH, static_cast<int8_t>(system_arch), 0);
  }
  void add_gpu_toolchain(::flatbuffers::Offset<MECData::DE_SystemGPUToolchainEnv> gpu_toolchain) {
    fbb_.AddOffset(DF_ZBRSEnvironment::VT_GPU_TOOLCHAIN, gpu_toolchain);
  }
  void add_glibc_version(::flatbuffers::Offset<::flatbuffers::String> glibc_version) {
    fbb_.AddOffset(DF_ZBRSEnvironment::VT_GLIBC_VERSION, glibc_version);
  }
  void add_docker_api_version(::flatbuffers::Offset<::flatbuffers::String> docker_api_version) {
    fbb_.AddOffset(DF_ZBRSEnvironment::VT_DOCKER_API_VERSION, docker_api_version);
  }
  void add_installed_apps(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> installed_apps) {
    fbb_.AddOffset(DF_ZBRSEnvironment::VT_INSTALLED_APPS, installed_apps);
  }
  explicit DF_ZBRSEnvironmentBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ZBRSEnvironment> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ZBRSEnvironment>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ZBRSEnvironment> CreateDF_ZBRSEnvironment(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_ZBRSVersion> zbrs_version = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> internal_modules = 0,
    MECData::DE_SystemArchitecture system_arch = MECData::DE_SystemArchitecture_X86_64,
    ::flatbuffers::Offset<MECData::DE_SystemGPUToolchainEnv> gpu_toolchain = 0,
    ::flatbuffers::Offset<::flatbuffers::String> glibc_version = 0,
    ::flatbuffers::Offset<::flatbuffers::String> docker_api_version = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> installed_apps = 0) {
  DF_ZBRSEnvironmentBuilder builder_(_fbb);
  builder_.add_installed_apps(installed_apps);
  builder_.add_docker_api_version(docker_api_version);
  builder_.add_glibc_version(glibc_version);
  builder_.add_gpu_toolchain(gpu_toolchain);
  builder_.add_internal_modules(internal_modules);
  builder_.add_zbrs_version(zbrs_version);
  builder_.add_system_arch(system_arch);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ZBRSEnvironment> CreateDF_ZBRSEnvironmentDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_ZBRSVersion> zbrs_version = 0,
    const std::vector<::flatbuffers::Offset<::flatbuffers::String>> *internal_modules = nullptr,
    MECData::DE_SystemArchitecture system_arch = MECData::DE_SystemArchitecture_X86_64,
    ::flatbuffers::Offset<MECData::DE_SystemGPUToolchainEnv> gpu_toolchain = 0,
    const char *glibc_version = nullptr,
    const char *docker_api_version = nullptr,
    const std::vector<::flatbuffers::Offset<::flatbuffers::String>> *installed_apps = nullptr) {
  auto internal_modules__ = internal_modules ? _fbb.CreateVector<::flatbuffers::Offset<::flatbuffers::String>>(*internal_modules) : 0;
  auto glibc_version__ = glibc_version ? _fbb.CreateString(glibc_version) : 0;
  auto docker_api_version__ = docker_api_version ? _fbb.CreateString(docker_api_version) : 0;
  auto installed_apps__ = installed_apps ? _fbb.CreateVector<::flatbuffers::Offset<::flatbuffers::String>>(*installed_apps) : 0;
  return MECData::CreateDF_ZBRSEnvironment(
      _fbb,
      zbrs_version,
      internal_modules__,
      system_arch,
      gpu_toolchain,
      glibc_version__,
      docker_api_version__,
      installed_apps__);
}

struct DF_ZBRSLocalMachineConfig FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ZBRSLocalMachineConfigBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SERIAL_NUMBER = 4,
    VT_MODEL = 6,
    VT_DESCRIPTION = 8,
    VT_POS = 10,
    VT_NODE = 12,
    VT_MILEAGE = 14,
    VT_ROUTES = 16,
    VT_OTA_SERVER = 18,
    VT_DOCKER_REGISTRY = 20,
    VT_LOG_SETTINGS = 22,
    VT_SSL_CERTIFICATES = 24,
    VT_CRASH_REPORT_SETTINGS = 26,
    VT_IMMEDIATELY_REPORT_LEVEL = 28,
    VT_MAP_COLLECTION = 30,
    VT_SNOWFLAKE = 32,
    VT_TAGS = 34,
    VT_MQTT_BROKER = 36
  };
  const ::flatbuffers::String *serial_number() const {
    return GetPointer<const ::flatbuffers::String *>(VT_SERIAL_NUMBER);
  }
  const ::flatbuffers::String *model() const {
    return GetPointer<const ::flatbuffers::String *>(VT_MODEL);
  }
  const ::flatbuffers::String *description() const {
    return GetPointer<const ::flatbuffers::String *>(VT_DESCRIPTION);
  }
  const MECData::DF_Position3D *pos() const {
    return GetPointer<const MECData::DF_Position3D *>(VT_POS);
  }
  const MECData::DF_NodeReferenceID *node() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_NODE);
  }
  const MECData::DF_Mileage *mileage() const {
    return GetPointer<const MECData::DF_Mileage *>(VT_MILEAGE);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PacketRoute>> *routes() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PacketRoute>> *>(VT_ROUTES);
  }
  const MECData::DF_OTAServerConfig *ota_server() const {
    return GetPointer<const MECData::DF_OTAServerConfig *>(VT_OTA_SERVER);
  }
  const MECData::DF_DockerRegistryConfig *docker_registry() const {
    return GetPointer<const MECData::DF_DockerRegistryConfig *>(VT_DOCKER_REGISTRY);
  }
  const MECData::DF_LogConfig *log_settings() const {
    return GetPointer<const MECData::DF_LogConfig *>(VT_LOG_SETTINGS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SSLCertificate>> *ssl_certificates() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SSLCertificate>> *>(VT_SSL_CERTIFICATES);
  }
  const MECData::DF_CrashReportConfig *crash_report_settings() const {
    return GetPointer<const MECData::DF_CrashReportConfig *>(VT_CRASH_REPORT_SETTINGS);
  }
  MECData::DE_ErrorLevel immediately_report_level() const {
    return static_cast<MECData::DE_ErrorLevel>(GetField<int8_t>(VT_IMMEDIATELY_REPORT_LEVEL, 0));
  }
  const MECData::DE_MapCollection *map_collection() const {
    return GetPointer<const MECData::DE_MapCollection *>(VT_MAP_COLLECTION);
  }
  const MECData::DF_Snowflake *snowflake() const {
    return GetPointer<const MECData::DF_Snowflake *>(VT_SNOWFLAKE);
  }
  const MECData::DE_OperationTags *tags() const {
    return GetPointer<const MECData::DE_OperationTags *>(VT_TAGS);
  }
  const MECData::DE_MQTTConfig *mqtt_broker() const {
    return GetPointer<const MECData::DE_MQTTConfig *>(VT_MQTT_BROKER);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_SERIAL_NUMBER) &&
           verifier.VerifyString(serial_number()) &&
           VerifyOffset(verifier, VT_MODEL) &&
           verifier.VerifyString(model()) &&
           VerifyOffset(verifier, VT_DESCRIPTION) &&
           verifier.VerifyString(description()) &&
           VerifyOffset(verifier, VT_POS) &&
           verifier.VerifyTable(pos()) &&
           VerifyOffset(verifier, VT_NODE) &&
           verifier.VerifyTable(node()) &&
           VerifyOffset(verifier, VT_MILEAGE) &&
           verifier.VerifyTable(mileage()) &&
           VerifyOffset(verifier, VT_ROUTES) &&
           verifier.VerifyVector(routes()) &&
           verifier.VerifyVectorOfTables(routes()) &&
           VerifyOffset(verifier, VT_OTA_SERVER) &&
           verifier.VerifyTable(ota_server()) &&
           VerifyOffset(verifier, VT_DOCKER_REGISTRY) &&
           verifier.VerifyTable(docker_registry()) &&
           VerifyOffset(verifier, VT_LOG_SETTINGS) &&
           verifier.VerifyTable(log_settings()) &&
           VerifyOffset(verifier, VT_SSL_CERTIFICATES) &&
           verifier.VerifyVector(ssl_certificates()) &&
           verifier.VerifyVectorOfTables(ssl_certificates()) &&
           VerifyOffset(verifier, VT_CRASH_REPORT_SETTINGS) &&
           verifier.VerifyTable(crash_report_settings()) &&
           VerifyField<int8_t>(verifier, VT_IMMEDIATELY_REPORT_LEVEL, 1) &&
           VerifyOffset(verifier, VT_MAP_COLLECTION) &&
           verifier.VerifyTable(map_collection()) &&
           VerifyOffset(verifier, VT_SNOWFLAKE) &&
           verifier.VerifyTable(snowflake()) &&
           VerifyOffset(verifier, VT_TAGS) &&
           verifier.VerifyTable(tags()) &&
           VerifyOffset(verifier, VT_MQTT_BROKER) &&
           verifier.VerifyTable(mqtt_broker()) &&
           verifier.EndTable();
  }
};

struct DF_ZBRSLocalMachineConfigBuilder {
  typedef DF_ZBRSLocalMachineConfig Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_serial_number(::flatbuffers::Offset<::flatbuffers::String> serial_number) {
    fbb_.AddOffset(DF_ZBRSLocalMachineConfig::VT_SERIAL_NUMBER, serial_number);
  }
  void add_model(::flatbuffers::Offset<::flatbuffers::String> model) {
    fbb_.AddOffset(DF_ZBRSLocalMachineConfig::VT_MODEL, model);
  }
  void add_description(::flatbuffers::Offset<::flatbuffers::String> description) {
    fbb_.AddOffset(DF_ZBRSLocalMachineConfig::VT_DESCRIPTION, description);
  }
  void add_pos(::flatbuffers::Offset<MECData::DF_Position3D> pos) {
    fbb_.AddOffset(DF_ZBRSLocalMachineConfig::VT_POS, pos);
  }
  void add_node(::flatbuffers::Offset<MECData::DF_NodeReferenceID> node) {
    fbb_.AddOffset(DF_ZBRSLocalMachineConfig::VT_NODE, node);
  }
  void add_mileage(::flatbuffers::Offset<MECData::DF_Mileage> mileage) {
    fbb_.AddOffset(DF_ZBRSLocalMachineConfig::VT_MILEAGE, mileage);
  }
  void add_routes(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PacketRoute>>> routes) {
    fbb_.AddOffset(DF_ZBRSLocalMachineConfig::VT_ROUTES, routes);
  }
  void add_ota_server(::flatbuffers::Offset<MECData::DF_OTAServerConfig> ota_server) {
    fbb_.AddOffset(DF_ZBRSLocalMachineConfig::VT_OTA_SERVER, ota_server);
  }
  void add_docker_registry(::flatbuffers::Offset<MECData::DF_DockerRegistryConfig> docker_registry) {
    fbb_.AddOffset(DF_ZBRSLocalMachineConfig::VT_DOCKER_REGISTRY, docker_registry);
  }
  void add_log_settings(::flatbuffers::Offset<MECData::DF_LogConfig> log_settings) {
    fbb_.AddOffset(DF_ZBRSLocalMachineConfig::VT_LOG_SETTINGS, log_settings);
  }
  void add_ssl_certificates(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SSLCertificate>>> ssl_certificates) {
    fbb_.AddOffset(DF_ZBRSLocalMachineConfig::VT_SSL_CERTIFICATES, ssl_certificates);
  }
  void add_crash_report_settings(::flatbuffers::Offset<MECData::DF_CrashReportConfig> crash_report_settings) {
    fbb_.AddOffset(DF_ZBRSLocalMachineConfig::VT_CRASH_REPORT_SETTINGS, crash_report_settings);
  }
  void add_immediately_report_level(MECData::DE_ErrorLevel immediately_report_level) {
    fbb_.AddElement<int8_t>(DF_ZBRSLocalMachineConfig::VT_IMMEDIATELY_REPORT_LEVEL, static_cast<int8_t>(immediately_report_level), 0);
  }
  void add_map_collection(::flatbuffers::Offset<MECData::DE_MapCollection> map_collection) {
    fbb_.AddOffset(DF_ZBRSLocalMachineConfig::VT_MAP_COLLECTION, map_collection);
  }
  void add_snowflake(::flatbuffers::Offset<MECData::DF_Snowflake> snowflake) {
    fbb_.AddOffset(DF_ZBRSLocalMachineConfig::VT_SNOWFLAKE, snowflake);
  }
  void add_tags(::flatbuffers::Offset<MECData::DE_OperationTags> tags) {
    fbb_.AddOffset(DF_ZBRSLocalMachineConfig::VT_TAGS, tags);
  }
  void add_mqtt_broker(::flatbuffers::Offset<MECData::DE_MQTTConfig> mqtt_broker) {
    fbb_.AddOffset(DF_ZBRSLocalMachineConfig::VT_MQTT_BROKER, mqtt_broker);
  }
  explicit DF_ZBRSLocalMachineConfigBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ZBRSLocalMachineConfig> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ZBRSLocalMachineConfig>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ZBRSLocalMachineConfig> CreateDF_ZBRSLocalMachineConfig(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> serial_number = 0,
    ::flatbuffers::Offset<::flatbuffers::String> model = 0,
    ::flatbuffers::Offset<::flatbuffers::String> description = 0,
    ::flatbuffers::Offset<MECData::DF_Position3D> pos = 0,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> node = 0,
    ::flatbuffers::Offset<MECData::DF_Mileage> mileage = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PacketRoute>>> routes = 0,
    ::flatbuffers::Offset<MECData::DF_OTAServerConfig> ota_server = 0,
    ::flatbuffers::Offset<MECData::DF_DockerRegistryConfig> docker_registry = 0,
    ::flatbuffers::Offset<MECData::DF_LogConfig> log_settings = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SSLCertificate>>> ssl_certificates = 0,
    ::flatbuffers::Offset<MECData::DF_CrashReportConfig> crash_report_settings = 0,
    MECData::DE_ErrorLevel immediately_report_level = MECData::DE_ErrorLevel_DEBUG,
    ::flatbuffers::Offset<MECData::DE_MapCollection> map_collection = 0,
    ::flatbuffers::Offset<MECData::DF_Snowflake> snowflake = 0,
    ::flatbuffers::Offset<MECData::DE_OperationTags> tags = 0,
    ::flatbuffers::Offset<MECData::DE_MQTTConfig> mqtt_broker = 0) {
  DF_ZBRSLocalMachineConfigBuilder builder_(_fbb);
  builder_.add_mqtt_broker(mqtt_broker);
  builder_.add_tags(tags);
  builder_.add_snowflake(snowflake);
  builder_.add_map_collection(map_collection);
  builder_.add_crash_report_settings(crash_report_settings);
  builder_.add_ssl_certificates(ssl_certificates);
  builder_.add_log_settings(log_settings);
  builder_.add_docker_registry(docker_registry);
  builder_.add_ota_server(ota_server);
  builder_.add_routes(routes);
  builder_.add_mileage(mileage);
  builder_.add_node(node);
  builder_.add_pos(pos);
  builder_.add_description(description);
  builder_.add_model(model);
  builder_.add_serial_number(serial_number);
  builder_.add_immediately_report_level(immediately_report_level);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ZBRSLocalMachineConfig> CreateDF_ZBRSLocalMachineConfigDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *serial_number = nullptr,
    const char *model = nullptr,
    const char *description = nullptr,
    ::flatbuffers::Offset<MECData::DF_Position3D> pos = 0,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> node = 0,
    ::flatbuffers::Offset<MECData::DF_Mileage> mileage = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_PacketRoute>> *routes = nullptr,
    ::flatbuffers::Offset<MECData::DF_OTAServerConfig> ota_server = 0,
    ::flatbuffers::Offset<MECData::DF_DockerRegistryConfig> docker_registry = 0,
    ::flatbuffers::Offset<MECData::DF_LogConfig> log_settings = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_SSLCertificate>> *ssl_certificates = nullptr,
    ::flatbuffers::Offset<MECData::DF_CrashReportConfig> crash_report_settings = 0,
    MECData::DE_ErrorLevel immediately_report_level = MECData::DE_ErrorLevel_DEBUG,
    ::flatbuffers::Offset<MECData::DE_MapCollection> map_collection = 0,
    ::flatbuffers::Offset<MECData::DF_Snowflake> snowflake = 0,
    ::flatbuffers::Offset<MECData::DE_OperationTags> tags = 0,
    ::flatbuffers::Offset<MECData::DE_MQTTConfig> mqtt_broker = 0) {
  auto serial_number__ = serial_number ? _fbb.CreateString(serial_number) : 0;
  auto model__ = model ? _fbb.CreateString(model) : 0;
  auto description__ = description ? _fbb.CreateString(description) : 0;
  auto routes__ = routes ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_PacketRoute>>(*routes) : 0;
  auto ssl_certificates__ = ssl_certificates ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_SSLCertificate>>(*ssl_certificates) : 0;
  return MECData::CreateDF_ZBRSLocalMachineConfig(
      _fbb,
      serial_number__,
      model__,
      description__,
      pos,
      node,
      mileage,
      routes__,
      ota_server,
      docker_registry,
      log_settings,
      ssl_certificates__,
      crash_report_settings,
      immediately_report_level,
      map_collection,
      snowflake,
      tags,
      mqtt_broker);
}

struct MSG_ZBRSFeatures FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_ZBRSFeaturesBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_LOCAL = 4,
    VT_ENV = 6,
    VT_PACKET_STATS = 8,
    VT_MSG_ID = 10
  };
  const MECData::DF_ZBRSLocalMachineConfig *local() const {
    return GetPointer<const MECData::DF_ZBRSLocalMachineConfig *>(VT_LOCAL);
  }
  const MECData::DF_ZBRSEnvironment *env() const {
    return GetPointer<const MECData::DF_ZBRSEnvironment *>(VT_ENV);
  }
  const MECData::DF_PacketFlowRecord *packet_stats() const {
    return GetPointer<const MECData::DF_PacketFlowRecord *>(VT_PACKET_STATS);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_LOCAL) &&
           verifier.VerifyTable(local()) &&
           VerifyOffset(verifier, VT_ENV) &&
           verifier.VerifyTable(env()) &&
           VerifyOffset(verifier, VT_PACKET_STATS) &&
           verifier.VerifyTable(packet_stats()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct MSG_ZBRSFeaturesBuilder {
  typedef MSG_ZBRSFeatures Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_local(::flatbuffers::Offset<MECData::DF_ZBRSLocalMachineConfig> local) {
    fbb_.AddOffset(MSG_ZBRSFeatures::VT_LOCAL, local);
  }
  void add_env(::flatbuffers::Offset<MECData::DF_ZBRSEnvironment> env) {
    fbb_.AddOffset(MSG_ZBRSFeatures::VT_ENV, env);
  }
  void add_packet_stats(::flatbuffers::Offset<MECData::DF_PacketFlowRecord> packet_stats) {
    fbb_.AddOffset(MSG_ZBRSFeatures::VT_PACKET_STATS, packet_stats);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_ZBRSFeatures::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_ZBRSFeaturesBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_ZBRSFeatures> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_ZBRSFeatures>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_ZBRSFeatures> CreateMSG_ZBRSFeatures(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_ZBRSLocalMachineConfig> local = 0,
    ::flatbuffers::Offset<MECData::DF_ZBRSEnvironment> env = 0,
    ::flatbuffers::Offset<MECData::DF_PacketFlowRecord> packet_stats = 0,
    int64_t msg_id = 0) {
  MSG_ZBRSFeaturesBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_packet_stats(packet_stats);
  builder_.add_env(env);
  builder_.add_local(local);
  return builder_.Finish();
}

inline const MECData::MSG_ZBRSFeatures *GetMSG_ZBRSFeatures(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_ZBRSFeatures>(buf);
}

inline const MECData::MSG_ZBRSFeatures *GetSizePrefixedMSG_ZBRSFeatures(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_ZBRSFeatures>(buf);
}

inline bool VerifyMSG_ZBRSFeaturesBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_ZBRSFeatures>(nullptr);
}

inline bool VerifySizePrefixedMSG_ZBRSFeaturesBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_ZBRSFeatures>(nullptr);
}

inline void FinishMSG_ZBRSFeaturesBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_ZBRSFeatures> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_ZBRSFeaturesBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_ZBRSFeatures> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_ZBRSFEATURES_MECDATA_H_
