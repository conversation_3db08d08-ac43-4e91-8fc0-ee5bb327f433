// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_VARIABLESPEEDLIMIT_MECDATA_H_
#define FLATBUFFERS_GENERATED_VARIABLESPEEDLIMIT_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "SpeedLimitScheme_generated.h"

namespace MECData {

struct MSG_VariableSpeedLimit;
struct MSG_VariableSpeedLimitBuilder;

struct MSG_VariableSpeedLimit FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_VariableSpeedLimitBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MOY = 4,
    VT_SEC_MARK = 6,
    VT_SCHEMES = 8,
    VT_MSG_ID = 10
  };
  uint32_t moy() const {
    return GetField<uint32_t>(VT_MOY, 4294967295);
  }
  uint16_t sec_mark() const {
    return GetField<uint16_t>(VT_SEC_MARK, 65535);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SpeedLimitScheme>> *schemes() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SpeedLimitScheme>> *>(VT_SCHEMES);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_MOY, 4) &&
           VerifyField<uint16_t>(verifier, VT_SEC_MARK, 2) &&
           VerifyOffsetRequired(verifier, VT_SCHEMES) &&
           verifier.VerifyVector(schemes()) &&
           verifier.VerifyVectorOfTables(schemes()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct MSG_VariableSpeedLimitBuilder {
  typedef MSG_VariableSpeedLimit Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_moy(uint32_t moy) {
    fbb_.AddElement<uint32_t>(MSG_VariableSpeedLimit::VT_MOY, moy, 4294967295);
  }
  void add_sec_mark(uint16_t sec_mark) {
    fbb_.AddElement<uint16_t>(MSG_VariableSpeedLimit::VT_SEC_MARK, sec_mark, 65535);
  }
  void add_schemes(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SpeedLimitScheme>>> schemes) {
    fbb_.AddOffset(MSG_VariableSpeedLimit::VT_SCHEMES, schemes);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_VariableSpeedLimit::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_VariableSpeedLimitBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_VariableSpeedLimit> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_VariableSpeedLimit>(end);
    fbb_.Required(o, MSG_VariableSpeedLimit::VT_SCHEMES);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_VariableSpeedLimit> CreateMSG_VariableSpeedLimit(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t moy = 4294967295,
    uint16_t sec_mark = 65535,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SpeedLimitScheme>>> schemes = 0,
    int64_t msg_id = 0) {
  MSG_VariableSpeedLimitBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_schemes(schemes);
  builder_.add_moy(moy);
  builder_.add_sec_mark(sec_mark);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_VariableSpeedLimit> CreateMSG_VariableSpeedLimitDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t moy = 4294967295,
    uint16_t sec_mark = 65535,
    const std::vector<::flatbuffers::Offset<MECData::DF_SpeedLimitScheme>> *schemes = nullptr,
    int64_t msg_id = 0) {
  auto schemes__ = schemes ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_SpeedLimitScheme>>(*schemes) : 0;
  return MECData::CreateMSG_VariableSpeedLimit(
      _fbb,
      moy,
      sec_mark,
      schemes__,
      msg_id);
}

inline const MECData::MSG_VariableSpeedLimit *GetMSG_VariableSpeedLimit(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_VariableSpeedLimit>(buf);
}

inline const MECData::MSG_VariableSpeedLimit *GetSizePrefixedMSG_VariableSpeedLimit(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_VariableSpeedLimit>(buf);
}

inline bool VerifyMSG_VariableSpeedLimitBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_VariableSpeedLimit>(nullptr);
}

inline bool VerifySizePrefixedMSG_VariableSpeedLimitBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_VariableSpeedLimit>(nullptr);
}

inline void FinishMSG_VariableSpeedLimitBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_VariableSpeedLimit> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_VariableSpeedLimitBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_VariableSpeedLimit> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_VARIABLESPEEDLIMIT_MECDATA_H_
