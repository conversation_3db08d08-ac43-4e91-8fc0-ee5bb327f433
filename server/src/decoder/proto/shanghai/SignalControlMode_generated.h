// automatically generated by the <PERSON><PERSON><PERSON>ers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_SIGNALCONTROLMODE_MECDATA_H_
#define FLATBUFFERS_GENERATED_SIGNALCONTROLMODE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_SignalControlMode : uint8_t {
  DE_SignalControlMode_CYCLIC_FIXED = 0,
  DE_SignalControlMode_CYCLIC_COORDINATED = 1,
  DE_SignalControlMode_ACTUATED = 2,
  DE_SignalControlMode_ADAPTIVE = 3,
  DE_SignalControlMode_STEP = 4,
  DE_SignalControlMode_AMBER_FLASH = 5,
  DE_SignalControlMode_ALL_RED = 6,
  DE_SignalControlMode_OFF = 255,
  DE_SignalControlMode_MIN = DE_SignalControlMode_CYCLIC_FIXED,
  DE_SignalControlMode_MAX = DE_SignalControlMode_OFF
};

inline const DE_SignalControlMode (&EnumValuesDE_SignalControlMode())[8] {
  static const DE_SignalControlMode values[] = {
    DE_SignalControlMode_CYCLIC_FIXED,
    DE_SignalControlMode_CYCLIC_COORDINATED,
    DE_SignalControlMode_ACTUATED,
    DE_SignalControlMode_ADAPTIVE,
    DE_SignalControlMode_STEP,
    DE_SignalControlMode_AMBER_FLASH,
    DE_SignalControlMode_ALL_RED,
    DE_SignalControlMode_OFF
  };
  return values;
}

inline const char *EnumNameDE_SignalControlMode(DE_SignalControlMode e) {
  switch (e) {
    case DE_SignalControlMode_CYCLIC_FIXED: return "CYCLIC_FIXED";
    case DE_SignalControlMode_CYCLIC_COORDINATED: return "CYCLIC_COORDINATED";
    case DE_SignalControlMode_ACTUATED: return "ACTUATED";
    case DE_SignalControlMode_ADAPTIVE: return "ADAPTIVE";
    case DE_SignalControlMode_STEP: return "STEP";
    case DE_SignalControlMode_AMBER_FLASH: return "AMBER_FLASH";
    case DE_SignalControlMode_ALL_RED: return "ALL_RED";
    case DE_SignalControlMode_OFF: return "OFF";
    default: return "";
  }
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_SIGNALCONTROLMODE_MECDATA_H_
