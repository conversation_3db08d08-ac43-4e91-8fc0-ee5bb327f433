// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_VEHICLESAFETYEXTENSIONS_MECDATA_H_
#define FLATBUFFERS_GENERATED_VEHICLESAFETYEXTENSIONS_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "ExteriorLights_generated.h"
#include "VehicleEventFlags_generated.h"

namespace MECData {

struct DF_VehicleSafetyExtensions;
struct DF_VehicleSafetyExtensionsBuilder;

struct DF_VehicleSafetyExtensions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_VehicleSafetyExtensionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_EVENTS = 4,
    VT_LIGHTS = 6
  };
  const MECData::DF_VehicleEventFlags *events() const {
    return GetPointer<const MECData::DF_VehicleEventFlags *>(VT_EVENTS);
  }
  const MECData::DE_ExteriorLights *lights() const {
    return GetPointer<const MECData::DE_ExteriorLights *>(VT_LIGHTS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_EVENTS) &&
           verifier.VerifyTable(events()) &&
           VerifyOffset(verifier, VT_LIGHTS) &&
           verifier.VerifyTable(lights()) &&
           verifier.EndTable();
  }
};

struct DF_VehicleSafetyExtensionsBuilder {
  typedef DF_VehicleSafetyExtensions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_events(::flatbuffers::Offset<MECData::DF_VehicleEventFlags> events) {
    fbb_.AddOffset(DF_VehicleSafetyExtensions::VT_EVENTS, events);
  }
  void add_lights(::flatbuffers::Offset<MECData::DE_ExteriorLights> lights) {
    fbb_.AddOffset(DF_VehicleSafetyExtensions::VT_LIGHTS, lights);
  }
  explicit DF_VehicleSafetyExtensionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_VehicleSafetyExtensions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_VehicleSafetyExtensions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_VehicleSafetyExtensions> CreateDF_VehicleSafetyExtensions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_VehicleEventFlags> events = 0,
    ::flatbuffers::Offset<MECData::DE_ExteriorLights> lights = 0) {
  DF_VehicleSafetyExtensionsBuilder builder_(_fbb);
  builder_.add_lights(lights);
  builder_.add_events(events);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_VEHICLESAFETYEXTENSIONS_MECDATA_H_
