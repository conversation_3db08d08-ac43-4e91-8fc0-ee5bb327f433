// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_VEHICLESIZE_MECDATA_H_
#define FLATBUFFERS_GENERATED_VEHICLESIZE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_VehicleSize;
struct DF_VehicleSizeBuilder;

struct DF_VehicleSize FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_VehicleSizeBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_WIDTH = 4,
    VT_LENGTH = 6,
    VT_HEIGHT = 8
  };
  uint16_t width() const {
    return GetField<uint16_t>(VT_WIDTH, 65535);
  }
  uint16_t length() const {
    return GetField<uint16_t>(VT_LENGTH, 65535);
  }
  uint8_t height() const {
    return GetField<uint8_t>(VT_HEIGHT, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_WIDTH, 2) &&
           VerifyField<uint16_t>(verifier, VT_LENGTH, 2) &&
           VerifyField<uint8_t>(verifier, VT_HEIGHT, 1) &&
           verifier.EndTable();
  }
};

struct DF_VehicleSizeBuilder {
  typedef DF_VehicleSize Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_width(uint16_t width) {
    fbb_.AddElement<uint16_t>(DF_VehicleSize::VT_WIDTH, width, 65535);
  }
  void add_length(uint16_t length) {
    fbb_.AddElement<uint16_t>(DF_VehicleSize::VT_LENGTH, length, 65535);
  }
  void add_height(uint8_t height) {
    fbb_.AddElement<uint8_t>(DF_VehicleSize::VT_HEIGHT, height, 0);
  }
  explicit DF_VehicleSizeBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_VehicleSize> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_VehicleSize>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_VehicleSize> CreateDF_VehicleSize(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t width = 65535,
    uint16_t length = 65535,
    uint8_t height = 0) {
  DF_VehicleSizeBuilder builder_(_fbb);
  builder_.add_length(length);
  builder_.add_width(width);
  builder_.add_height(height);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_VEHICLESIZE_MECDATA_H_
