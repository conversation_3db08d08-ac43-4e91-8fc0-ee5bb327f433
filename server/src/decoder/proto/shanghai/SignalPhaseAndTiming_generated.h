// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_SIGNALPHASEANDTIMING_MECDATA_H_
#define FLATBUFFERS_GENERATED_SIGNALPHASEANDTIMING_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "DebugTimeRecords_generated.h"
#include "IntersectionState_generated.h"

namespace MECData {

struct MSG_SignalPhaseAndTiming;
struct MSG_SignalPhaseAndTimingBuilder;

struct MSG_SignalPhaseAndTiming FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_SignalPhaseAndTimingBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MOY = 4,
    VT_TIMESTAMP = 6,
    VT_NAME = 8,
    VT_INTERSECTIONS = 10,
    VT_TIME_RECORDS = 12,
    VT_MSG_ID = 14
  };
  uint32_t moy() const {
    return GetField<uint32_t>(VT_MOY, 0);
  }
  uint16_t timeStamp() const {
    return GetField<uint16_t>(VT_TIMESTAMP, 0);
  }
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_IntersectionState>> *intersections() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_IntersectionState>> *>(VT_INTERSECTIONS);
  }
  const MECData::DF_DebugTimeRecords *time_records() const {
    return GetPointer<const MECData::DF_DebugTimeRecords *>(VT_TIME_RECORDS);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_MOY, 4) &&
           VerifyField<uint16_t>(verifier, VT_TIMESTAMP, 2) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffsetRequired(verifier, VT_INTERSECTIONS) &&
           verifier.VerifyVector(intersections()) &&
           verifier.VerifyVectorOfTables(intersections()) &&
           VerifyOffset(verifier, VT_TIME_RECORDS) &&
           verifier.VerifyTable(time_records()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct MSG_SignalPhaseAndTimingBuilder {
  typedef MSG_SignalPhaseAndTiming Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_moy(uint32_t moy) {
    fbb_.AddElement<uint32_t>(MSG_SignalPhaseAndTiming::VT_MOY, moy, 0);
  }
  void add_timeStamp(uint16_t timeStamp) {
    fbb_.AddElement<uint16_t>(MSG_SignalPhaseAndTiming::VT_TIMESTAMP, timeStamp, 0);
  }
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(MSG_SignalPhaseAndTiming::VT_NAME, name);
  }
  void add_intersections(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_IntersectionState>>> intersections) {
    fbb_.AddOffset(MSG_SignalPhaseAndTiming::VT_INTERSECTIONS, intersections);
  }
  void add_time_records(::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records) {
    fbb_.AddOffset(MSG_SignalPhaseAndTiming::VT_TIME_RECORDS, time_records);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_SignalPhaseAndTiming::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_SignalPhaseAndTimingBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_SignalPhaseAndTiming> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_SignalPhaseAndTiming>(end);
    fbb_.Required(o, MSG_SignalPhaseAndTiming::VT_INTERSECTIONS);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_SignalPhaseAndTiming> CreateMSG_SignalPhaseAndTiming(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t moy = 0,
    uint16_t timeStamp = 0,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_IntersectionState>>> intersections = 0,
    ::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records = 0,
    int64_t msg_id = 0) {
  MSG_SignalPhaseAndTimingBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_time_records(time_records);
  builder_.add_intersections(intersections);
  builder_.add_name(name);
  builder_.add_moy(moy);
  builder_.add_timeStamp(timeStamp);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_SignalPhaseAndTiming> CreateMSG_SignalPhaseAndTimingDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t moy = 0,
    uint16_t timeStamp = 0,
    const char *name = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_IntersectionState>> *intersections = nullptr,
    ::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records = 0,
    int64_t msg_id = 0) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto intersections__ = intersections ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_IntersectionState>>(*intersections) : 0;
  return MECData::CreateMSG_SignalPhaseAndTiming(
      _fbb,
      moy,
      timeStamp,
      name__,
      intersections__,
      time_records,
      msg_id);
}

inline const MECData::MSG_SignalPhaseAndTiming *GetMSG_SignalPhaseAndTiming(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_SignalPhaseAndTiming>(buf);
}

inline const MECData::MSG_SignalPhaseAndTiming *GetSizePrefixedMSG_SignalPhaseAndTiming(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_SignalPhaseAndTiming>(buf);
}

inline bool VerifyMSG_SignalPhaseAndTimingBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_SignalPhaseAndTiming>(nullptr);
}

inline bool VerifySizePrefixedMSG_SignalPhaseAndTimingBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_SignalPhaseAndTiming>(nullptr);
}

inline void FinishMSG_SignalPhaseAndTimingBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_SignalPhaseAndTiming> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_SignalPhaseAndTimingBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_SignalPhaseAndTiming> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_SIGNALPHASEANDTIMING_MECDATA_H_
