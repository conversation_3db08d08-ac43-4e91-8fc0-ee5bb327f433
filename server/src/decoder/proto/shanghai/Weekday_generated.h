// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_WEEKDAY_MECDATA_H_
#define FLATBUFFERS_GENERATED_WEEKDAY_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_Weekday : uint8_t {
  DE_Weekday_SUN = 0,
  DE_Weekday_MON = 1,
  DE_Weekday_TUE = 2,
  DE_Weekday_WED = 3,
  DE_Weekday_THUR = 4,
  DE_Weekday_FRI = 5,
  DE_Weekday_SAT = 6,
  DE_Weekday_MIN = DE_Weekday_SUN,
  DE_Weekday_MAX = DE_Weekday_SAT
};

inline const DE_Weekday (&EnumValuesDE_Weekday())[7] {
  static const DE_Weekday values[] = {
    DE_Weekday_SUN,
    DE_Weekday_MON,
    DE_Weekday_TUE,
    DE_Weekday_WED,
    DE_Weekday_THUR,
    DE_Weekday_FRI,
    DE_Weekday_SAT
  };
  return values;
}

inline const char * const *EnumNamesDE_Weekday() {
  static const char * const names[8] = {
    "SUN",
    "MON",
    "TUE",
    "WED",
    "THUR",
    "FRI",
    "SAT",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_Weekday(DE_Weekday e) {
  if (::flatbuffers::IsOutRange(e, DE_Weekday_SUN, DE_Weekday_SAT)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_Weekday()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_WEEKDAY_MECDATA_H_
