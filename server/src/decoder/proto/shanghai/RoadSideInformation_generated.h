// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_ROADSIDEINFORMATION_MECDATA_H_
#define FLATBUFFERS_GENERATED_ROADSIDEINFORMATION_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "DebugTimeRecords_generated.h"
#include "Position3D_generated.h"
#include "RTEData_generated.h"
#include "RTSData_generated.h"

namespace MECData {

struct MSG_RoadSideInformation;
struct MSG_RoadSideInformationBuilder;

struct MSG_RoadSideInformation FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_RoadSideInformationBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MOY = 4,
    VT_ID = 6,
    VT_REFPOS = 8,
    VT_RTES = 10,
    VT_RTSS = 12,
    VT_TIME_RECORDS = 14,
    VT_MSG_ID = 16,
    VT_RSU_ID = 18
  };
  uint32_t moy() const {
    return GetField<uint32_t>(VT_MOY, 0);
  }
  uint16_t id() const {
    return GetField<uint16_t>(VT_ID, 0);
  }
  const MECData::DF_Position3D *refPos() const {
    return GetPointer<const MECData::DF_Position3D *>(VT_REFPOS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RTEData>> *rtes() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RTEData>> *>(VT_RTES);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RTSData>> *rtss() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RTSData>> *>(VT_RTSS);
  }
  const MECData::DF_DebugTimeRecords *time_records() const {
    return GetPointer<const MECData::DF_DebugTimeRecords *>(VT_TIME_RECORDS);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  const ::flatbuffers::Vector<uint8_t> *rsu_id() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_RSU_ID);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_MOY, 4) &&
           VerifyField<uint16_t>(verifier, VT_ID, 2) &&
           VerifyOffsetRequired(verifier, VT_REFPOS) &&
           verifier.VerifyTable(refPos()) &&
           VerifyOffset(verifier, VT_RTES) &&
           verifier.VerifyVector(rtes()) &&
           verifier.VerifyVectorOfTables(rtes()) &&
           VerifyOffset(verifier, VT_RTSS) &&
           verifier.VerifyVector(rtss()) &&
           verifier.VerifyVectorOfTables(rtss()) &&
           VerifyOffset(verifier, VT_TIME_RECORDS) &&
           verifier.VerifyTable(time_records()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           VerifyOffset(verifier, VT_RSU_ID) &&
           verifier.VerifyVector(rsu_id()) &&
           verifier.EndTable();
  }
};

struct MSG_RoadSideInformationBuilder {
  typedef MSG_RoadSideInformation Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_moy(uint32_t moy) {
    fbb_.AddElement<uint32_t>(MSG_RoadSideInformation::VT_MOY, moy, 0);
  }
  void add_id(uint16_t id) {
    fbb_.AddElement<uint16_t>(MSG_RoadSideInformation::VT_ID, id, 0);
  }
  void add_refPos(::flatbuffers::Offset<MECData::DF_Position3D> refPos) {
    fbb_.AddOffset(MSG_RoadSideInformation::VT_REFPOS, refPos);
  }
  void add_rtes(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RTEData>>> rtes) {
    fbb_.AddOffset(MSG_RoadSideInformation::VT_RTES, rtes);
  }
  void add_rtss(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RTSData>>> rtss) {
    fbb_.AddOffset(MSG_RoadSideInformation::VT_RTSS, rtss);
  }
  void add_time_records(::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records) {
    fbb_.AddOffset(MSG_RoadSideInformation::VT_TIME_RECORDS, time_records);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_RoadSideInformation::VT_MSG_ID, msg_id, 0);
  }
  void add_rsu_id(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> rsu_id) {
    fbb_.AddOffset(MSG_RoadSideInformation::VT_RSU_ID, rsu_id);
  }
  explicit MSG_RoadSideInformationBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_RoadSideInformation> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_RoadSideInformation>(end);
    fbb_.Required(o, MSG_RoadSideInformation::VT_REFPOS);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_RoadSideInformation> CreateMSG_RoadSideInformation(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t moy = 0,
    uint16_t id = 0,
    ::flatbuffers::Offset<MECData::DF_Position3D> refPos = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RTEData>>> rtes = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RTSData>>> rtss = 0,
    ::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records = 0,
    int64_t msg_id = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> rsu_id = 0) {
  MSG_RoadSideInformationBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_rsu_id(rsu_id);
  builder_.add_time_records(time_records);
  builder_.add_rtss(rtss);
  builder_.add_rtes(rtes);
  builder_.add_refPos(refPos);
  builder_.add_moy(moy);
  builder_.add_id(id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_RoadSideInformation> CreateMSG_RoadSideInformationDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t moy = 0,
    uint16_t id = 0,
    ::flatbuffers::Offset<MECData::DF_Position3D> refPos = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_RTEData>> *rtes = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_RTSData>> *rtss = nullptr,
    ::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records = 0,
    int64_t msg_id = 0,
    const std::vector<uint8_t> *rsu_id = nullptr) {
  auto rtes__ = rtes ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_RTEData>>(*rtes) : 0;
  auto rtss__ = rtss ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_RTSData>>(*rtss) : 0;
  auto rsu_id__ = rsu_id ? _fbb.CreateVector<uint8_t>(*rsu_id) : 0;
  return MECData::CreateMSG_RoadSideInformation(
      _fbb,
      moy,
      id,
      refPos,
      rtes__,
      rtss__,
      time_records,
      msg_id,
      rsu_id__);
}

inline const MECData::MSG_RoadSideInformation *GetMSG_RoadSideInformation(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_RoadSideInformation>(buf);
}

inline const MECData::MSG_RoadSideInformation *GetSizePrefixedMSG_RoadSideInformation(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_RoadSideInformation>(buf);
}

inline bool VerifyMSG_RoadSideInformationBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_RoadSideInformation>(nullptr);
}

inline bool VerifySizePrefixedMSG_RoadSideInformationBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_RoadSideInformation>(nullptr);
}

inline void FinishMSG_RoadSideInformationBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_RoadSideInformation> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_RoadSideInformationBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_RoadSideInformation> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_ROADSIDEINFORMATION_MECDATA_H_
