// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_POSITIONOFFSETLL_MECDATA_H_
#define FLATBUFFERS_GENERATED_POSITIONOFFSETLL_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "PositionLL24B_generated.h"
#include "PositionLL28B_generated.h"
#include "PositionLL32B_generated.h"
#include "PositionLL36B_generated.h"
#include "PositionLL44B_generated.h"
#include "PositionLL48B_generated.h"
#include "PositionLLmD64b_generated.h"

namespace MECData {

enum DF_PositionOffsetLL : uint8_t {
  DF_PositionOffsetLL_NONE = 0,
  DF_PositionOffsetLL_DF_PositionLL24B = 1,
  DF_PositionOffsetLL_DF_PositionLL28B = 2,
  DF_PositionOffsetLL_DF_PositionLL32B = 3,
  DF_PositionOffsetLL_DF_PositionLL36B = 4,
  DF_PositionOffsetLL_DF_PositionLL44B = 5,
  DF_PositionOffsetLL_DF_PositionLL48B = 6,
  DF_PositionOffsetLL_DF_PositionLLmD64b = 7,
  DF_PositionOffsetLL_MIN = DF_PositionOffsetLL_NONE,
  DF_PositionOffsetLL_MAX = DF_PositionOffsetLL_DF_PositionLLmD64b
};

inline const DF_PositionOffsetLL (&EnumValuesDF_PositionOffsetLL())[8] {
  static const DF_PositionOffsetLL values[] = {
    DF_PositionOffsetLL_NONE,
    DF_PositionOffsetLL_DF_PositionLL24B,
    DF_PositionOffsetLL_DF_PositionLL28B,
    DF_PositionOffsetLL_DF_PositionLL32B,
    DF_PositionOffsetLL_DF_PositionLL36B,
    DF_PositionOffsetLL_DF_PositionLL44B,
    DF_PositionOffsetLL_DF_PositionLL48B,
    DF_PositionOffsetLL_DF_PositionLLmD64b
  };
  return values;
}

inline const char * const *EnumNamesDF_PositionOffsetLL() {
  static const char * const names[9] = {
    "NONE",
    "DF_PositionLL24B",
    "DF_PositionLL28B",
    "DF_PositionLL32B",
    "DF_PositionLL36B",
    "DF_PositionLL44B",
    "DF_PositionLL48B",
    "DF_PositionLLmD64b",
    nullptr
  };
  return names;
}

inline const char *EnumNameDF_PositionOffsetLL(DF_PositionOffsetLL e) {
  if (::flatbuffers::IsOutRange(e, DF_PositionOffsetLL_NONE, DF_PositionOffsetLL_DF_PositionLLmD64b)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDF_PositionOffsetLL()[index];
}

template<typename T> struct DF_PositionOffsetLLTraits {
  static const DF_PositionOffsetLL enum_value = DF_PositionOffsetLL_NONE;
};

template<> struct DF_PositionOffsetLLTraits<MECData::DF_PositionLL24B> {
  static const DF_PositionOffsetLL enum_value = DF_PositionOffsetLL_DF_PositionLL24B;
};

template<> struct DF_PositionOffsetLLTraits<MECData::DF_PositionLL28B> {
  static const DF_PositionOffsetLL enum_value = DF_PositionOffsetLL_DF_PositionLL28B;
};

template<> struct DF_PositionOffsetLLTraits<MECData::DF_PositionLL32B> {
  static const DF_PositionOffsetLL enum_value = DF_PositionOffsetLL_DF_PositionLL32B;
};

template<> struct DF_PositionOffsetLLTraits<MECData::DF_PositionLL36B> {
  static const DF_PositionOffsetLL enum_value = DF_PositionOffsetLL_DF_PositionLL36B;
};

template<> struct DF_PositionOffsetLLTraits<MECData::DF_PositionLL44B> {
  static const DF_PositionOffsetLL enum_value = DF_PositionOffsetLL_DF_PositionLL44B;
};

template<> struct DF_PositionOffsetLLTraits<MECData::DF_PositionLL48B> {
  static const DF_PositionOffsetLL enum_value = DF_PositionOffsetLL_DF_PositionLL48B;
};

template<> struct DF_PositionOffsetLLTraits<MECData::DF_PositionLLmD64b> {
  static const DF_PositionOffsetLL enum_value = DF_PositionOffsetLL_DF_PositionLLmD64b;
};

bool VerifyDF_PositionOffsetLL(::flatbuffers::Verifier &verifier, const void *obj, DF_PositionOffsetLL type);
bool VerifyDF_PositionOffsetLLVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types);

inline bool VerifyDF_PositionOffsetLL(::flatbuffers::Verifier &verifier, const void *obj, DF_PositionOffsetLL type) {
  switch (type) {
    case DF_PositionOffsetLL_NONE: {
      return true;
    }
    case DF_PositionOffsetLL_DF_PositionLL24B: {
      auto ptr = reinterpret_cast<const MECData::DF_PositionLL24B *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PositionOffsetLL_DF_PositionLL28B: {
      auto ptr = reinterpret_cast<const MECData::DF_PositionLL28B *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PositionOffsetLL_DF_PositionLL32B: {
      auto ptr = reinterpret_cast<const MECData::DF_PositionLL32B *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PositionOffsetLL_DF_PositionLL36B: {
      auto ptr = reinterpret_cast<const MECData::DF_PositionLL36B *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PositionOffsetLL_DF_PositionLL44B: {
      auto ptr = reinterpret_cast<const MECData::DF_PositionLL44B *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PositionOffsetLL_DF_PositionLL48B: {
      auto ptr = reinterpret_cast<const MECData::DF_PositionLL48B *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PositionOffsetLL_DF_PositionLLmD64b: {
      auto ptr = reinterpret_cast<const MECData::DF_PositionLLmD64b *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return true;
  }
}

inline bool VerifyDF_PositionOffsetLLVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (::flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyDF_PositionOffsetLL(
        verifier,  values->Get(i), types->GetEnum<DF_PositionOffsetLL>(i))) {
      return false;
    }
  }
  return true;
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_POSITIONOFFSETLL_MECDATA_H_
