// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_TEXTGB2312_MECDATA_H_
#define FLATBUFFERS_GENERATED_TEXTGB2312_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_TextGB2312;
struct DF_TextGB2312Builder;

struct DF_TextGB2312 FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_TextGB2312Builder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TEXTGB2312 = 4
  };
  const ::flatbuffers::String *textGB2312() const {
    return GetPointer<const ::flatbuffers::String *>(VT_TEXTGB2312);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_TEXTGB2312) &&
           verifier.VerifyString(textGB2312()) &&
           verifier.EndTable();
  }
};

struct DF_TextGB2312Builder {
  typedef DF_TextGB2312 Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_textGB2312(::flatbuffers::Offset<::flatbuffers::String> textGB2312) {
    fbb_.AddOffset(DF_TextGB2312::VT_TEXTGB2312, textGB2312);
  }
  explicit DF_TextGB2312Builder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_TextGB2312> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_TextGB2312>(end);
    fbb_.Required(o, DF_TextGB2312::VT_TEXTGB2312);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_TextGB2312> CreateDF_TextGB2312(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> textGB2312 = 0) {
  DF_TextGB2312Builder builder_(_fbb);
  builder_.add_textGB2312(textGB2312);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_TextGB2312> CreateDF_TextGB2312Direct(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *textGB2312 = nullptr) {
  auto textGB2312__ = textGB2312 ? _fbb.CreateString(textGB2312) : 0;
  return MECData::CreateDF_TextGB2312(
      _fbb,
      textGB2312__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_TEXTGB2312_MECDATA_H_
