// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_UTCTIMING_MECDATA_H_
#define FLATBUFFERS_GENERATED_UTCTIMING_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_UTCTiming;
struct DF_UTCTimingBuilder;

struct DF_UTCTiming FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_UTCTimingBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_STARTUTCTIME = 4,
    VT_MINENDUTCTIME = 6,
    VT_MAXENDUTCTIME = 8,
    VT_LIKELYENDUTCTIME = 10,
    VT_TIMECONFIDENCE = 12,
    VT_NEXTSTARTUTCTIME = 14,
    VT_NEXTENDUCTTIME = 16
  };
  uint16_t startUTCTime() const {
    return GetField<uint16_t>(VT_STARTUTCTIME, 65535);
  }
  uint16_t minEndUTCTime() const {
    return GetField<uint16_t>(VT_MINENDUTCTIME, 0);
  }
  uint16_t maxEndUTCTime() const {
    return GetField<uint16_t>(VT_MAXENDUTCTIME, 0);
  }
  uint16_t likelyEndUTCTime() const {
    return GetField<uint16_t>(VT_LIKELYENDUTCTIME, 65535);
  }
  uint16_t timeConfidence() const {
    return GetField<uint16_t>(VT_TIMECONFIDENCE, 0);
  }
  uint16_t nextStartUTCTime() const {
    return GetField<uint16_t>(VT_NEXTSTARTUTCTIME, 0);
  }
  uint16_t nextEndUCTTime() const {
    return GetField<uint16_t>(VT_NEXTENDUCTTIME, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_STARTUTCTIME, 2) &&
           VerifyField<uint16_t>(verifier, VT_MINENDUTCTIME, 2) &&
           VerifyField<uint16_t>(verifier, VT_MAXENDUTCTIME, 2) &&
           VerifyField<uint16_t>(verifier, VT_LIKELYENDUTCTIME, 2) &&
           VerifyField<uint16_t>(verifier, VT_TIMECONFIDENCE, 2) &&
           VerifyField<uint16_t>(verifier, VT_NEXTSTARTUTCTIME, 2) &&
           VerifyField<uint16_t>(verifier, VT_NEXTENDUCTTIME, 2) &&
           verifier.EndTable();
  }
};

struct DF_UTCTimingBuilder {
  typedef DF_UTCTiming Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_startUTCTime(uint16_t startUTCTime) {
    fbb_.AddElement<uint16_t>(DF_UTCTiming::VT_STARTUTCTIME, startUTCTime, 65535);
  }
  void add_minEndUTCTime(uint16_t minEndUTCTime) {
    fbb_.AddElement<uint16_t>(DF_UTCTiming::VT_MINENDUTCTIME, minEndUTCTime, 0);
  }
  void add_maxEndUTCTime(uint16_t maxEndUTCTime) {
    fbb_.AddElement<uint16_t>(DF_UTCTiming::VT_MAXENDUTCTIME, maxEndUTCTime, 0);
  }
  void add_likelyEndUTCTime(uint16_t likelyEndUTCTime) {
    fbb_.AddElement<uint16_t>(DF_UTCTiming::VT_LIKELYENDUTCTIME, likelyEndUTCTime, 65535);
  }
  void add_timeConfidence(uint16_t timeConfidence) {
    fbb_.AddElement<uint16_t>(DF_UTCTiming::VT_TIMECONFIDENCE, timeConfidence, 0);
  }
  void add_nextStartUTCTime(uint16_t nextStartUTCTime) {
    fbb_.AddElement<uint16_t>(DF_UTCTiming::VT_NEXTSTARTUTCTIME, nextStartUTCTime, 0);
  }
  void add_nextEndUCTTime(uint16_t nextEndUCTTime) {
    fbb_.AddElement<uint16_t>(DF_UTCTiming::VT_NEXTENDUCTTIME, nextEndUCTTime, 0);
  }
  explicit DF_UTCTimingBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_UTCTiming> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_UTCTiming>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_UTCTiming> CreateDF_UTCTiming(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t startUTCTime = 65535,
    uint16_t minEndUTCTime = 0,
    uint16_t maxEndUTCTime = 0,
    uint16_t likelyEndUTCTime = 65535,
    uint16_t timeConfidence = 0,
    uint16_t nextStartUTCTime = 0,
    uint16_t nextEndUCTTime = 0) {
  DF_UTCTimingBuilder builder_(_fbb);
  builder_.add_nextEndUCTTime(nextEndUCTTime);
  builder_.add_nextStartUTCTime(nextStartUTCTime);
  builder_.add_timeConfidence(timeConfidence);
  builder_.add_likelyEndUTCTime(likelyEndUTCTime);
  builder_.add_maxEndUTCTime(maxEndUTCTime);
  builder_.add_minEndUTCTime(minEndUTCTime);
  builder_.add_startUTCTime(startUTCTime);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_UTCTIMING_MECDATA_H_
