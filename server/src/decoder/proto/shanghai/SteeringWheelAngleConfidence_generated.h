// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_STEERINGWHEELANGLECONFIDENCE_MECDATA_H_
#define FLATBUFFERS_GENERATED_STEERINGWHEELANGLECONFIDENCE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_SteeringWheelAngleConfidence : int8_t {
  DE_SteeringWheelAngleConfidence_unavailable = 0,
  DE_SteeringWheelAngleConfidence_prec2deg = 1,
  DE_SteeringWheelAngleConfidence_prec1deg = 2,
  DE_SteeringWheelAngleConfidence_prec0_02deg = 3,
  DE_SteeringWheelAngleConfidence_MIN = DE_SteeringWheelAngleConfidence_unavailable,
  DE_SteeringWheelAngleConfidence_MAX = DE_SteeringWheelAngleConfidence_prec0_02deg
};

inline const DE_SteeringWheelAngleConfidence (&EnumValuesDE_SteeringWheelAngleConfidence())[4] {
  static const DE_SteeringWheelAngleConfidence values[] = {
    DE_SteeringWheelAngleConfidence_unavailable,
    DE_SteeringWheelAngleConfidence_prec2deg,
    DE_SteeringWheelAngleConfidence_prec1deg,
    DE_SteeringWheelAngleConfidence_prec0_02deg
  };
  return values;
}

inline const char * const *EnumNamesDE_SteeringWheelAngleConfidence() {
  static const char * const names[5] = {
    "unavailable",
    "prec2deg",
    "prec1deg",
    "prec0_02deg",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_SteeringWheelAngleConfidence(DE_SteeringWheelAngleConfidence e) {
  if (::flatbuffers::IsOutRange(e, DE_SteeringWheelAngleConfidence_unavailable, DE_SteeringWheelAngleConfidence_prec0_02deg)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_SteeringWheelAngleConfidence()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_STEERINGWHEELANGLECONFIDENCE_MECDATA_H_
