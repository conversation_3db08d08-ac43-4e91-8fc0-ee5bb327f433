// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_SIGNALPHASE_MECDATA_H_
#define FLATBUFFERS_GENERATED_SIGNALPHASE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_SignalPhase;
struct DF_SignalPhaseBuilder;

struct DF_SignalPhase FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SignalPhaseBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ID = 4,
    VT_PHASESTATUS = 6,
    VT_NEXTGREENTIME = 8
  };
  int32_t id() const {
    return GetField<int32_t>(VT_ID, 0);
  }
  uint16_t phaseStatus() const {
    return GetField<uint16_t>(VT_PHASESTATUS, 0);
  }
  uint16_t nextGreenTime() const {
    return GetField<uint16_t>(VT_NEXTGREENTIME, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_ID, 4) &&
           VerifyField<uint16_t>(verifier, VT_PHASESTATUS, 2) &&
           VerifyField<uint16_t>(verifier, VT_NEXTGREENTIME, 2) &&
           verifier.EndTable();
  }
};

struct DF_SignalPhaseBuilder {
  typedef DF_SignalPhase Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_id(int32_t id) {
    fbb_.AddElement<int32_t>(DF_SignalPhase::VT_ID, id, 0);
  }
  void add_phaseStatus(uint16_t phaseStatus) {
    fbb_.AddElement<uint16_t>(DF_SignalPhase::VT_PHASESTATUS, phaseStatus, 0);
  }
  void add_nextGreenTime(uint16_t nextGreenTime) {
    fbb_.AddElement<uint16_t>(DF_SignalPhase::VT_NEXTGREENTIME, nextGreenTime, 0);
  }
  explicit DF_SignalPhaseBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_SignalPhase> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_SignalPhase>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_SignalPhase> CreateDF_SignalPhase(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t id = 0,
    uint16_t phaseStatus = 0,
    uint16_t nextGreenTime = 0) {
  DF_SignalPhaseBuilder builder_(_fbb);
  builder_.add_id(id);
  builder_.add_nextGreenTime(nextGreenTime);
  builder_.add_phaseStatus(phaseStatus);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_SIGNALPHASE_MECDATA_H_
