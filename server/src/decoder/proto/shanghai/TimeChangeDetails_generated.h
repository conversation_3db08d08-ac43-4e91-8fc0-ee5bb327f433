// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_TIMECHANGEDETAILS_MECDATA_H_
#define FLATBUFFERS_GENERATED_TIMECHANGEDETAILS_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "TimeCountingDown_generated.h"
#include "UTCTiming_generated.h"

namespace MECData {

enum DF_TimeChangeDetails : uint8_t {
  DF_TimeChangeDetails_NONE = 0,
  DF_TimeChangeDetails_DF_TimeCountingDown = 1,
  DF_TimeChangeDetails_DF_UTCTiming = 2,
  DF_TimeChangeDetails_MIN = DF_TimeChangeDetails_NONE,
  DF_TimeChangeDetails_MAX = DF_TimeChangeDetails_DF_UTCTiming
};

inline const DF_TimeChangeDetails (&EnumValuesDF_TimeChangeDetails())[3] {
  static const DF_TimeChangeDetails values[] = {
    DF_TimeChangeDetails_NONE,
    DF_TimeChangeDetails_DF_TimeCountingDown,
    DF_TimeChangeDetails_DF_UTCTiming
  };
  return values;
}

inline const char * const *EnumNamesDF_TimeChangeDetails() {
  static const char * const names[4] = {
    "NONE",
    "DF_TimeCountingDown",
    "DF_UTCTiming",
    nullptr
  };
  return names;
}

inline const char *EnumNameDF_TimeChangeDetails(DF_TimeChangeDetails e) {
  if (::flatbuffers::IsOutRange(e, DF_TimeChangeDetails_NONE, DF_TimeChangeDetails_DF_UTCTiming)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDF_TimeChangeDetails()[index];
}

template<typename T> struct DF_TimeChangeDetailsTraits {
  static const DF_TimeChangeDetails enum_value = DF_TimeChangeDetails_NONE;
};

template<> struct DF_TimeChangeDetailsTraits<MECData::DF_TimeCountingDown> {
  static const DF_TimeChangeDetails enum_value = DF_TimeChangeDetails_DF_TimeCountingDown;
};

template<> struct DF_TimeChangeDetailsTraits<MECData::DF_UTCTiming> {
  static const DF_TimeChangeDetails enum_value = DF_TimeChangeDetails_DF_UTCTiming;
};

bool VerifyDF_TimeChangeDetails(::flatbuffers::Verifier &verifier, const void *obj, DF_TimeChangeDetails type);
bool VerifyDF_TimeChangeDetailsVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types);

inline bool VerifyDF_TimeChangeDetails(::flatbuffers::Verifier &verifier, const void *obj, DF_TimeChangeDetails type) {
  switch (type) {
    case DF_TimeChangeDetails_NONE: {
      return true;
    }
    case DF_TimeChangeDetails_DF_TimeCountingDown: {
      auto ptr = reinterpret_cast<const MECData::DF_TimeCountingDown *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_TimeChangeDetails_DF_UTCTiming: {
      auto ptr = reinterpret_cast<const MECData::DF_UTCTiming *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return true;
  }
}

inline bool VerifyDF_TimeChangeDetailsVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (::flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyDF_TimeChangeDetails(
        verifier,  values->Get(i), types->GetEnum<DF_TimeChangeDetails>(i))) {
      return false;
    }
  }
  return true;
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_TIMECHANGEDETAILS_MECDATA_H_
