// automatically generated by the <PERSON><PERSON><PERSON>ers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_TRANSMISSIONSTATE_MECDATA_H_
#define FLATBUFFERS_GENERATED_TRANSMISSIONSTATE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_TransmissionState : int8_t {
  DE_TransmissionState_neutral = 0,
  DE_TransmissionState_park = 1,
  DE_TransmissionState_forwardGears = 2,
  DE_TransmissionState_reverseGears = 3,
  DE_TransmissionState_reserved1 = 4,
  DE_TransmissionState_reserved2 = 5,
  DE_TransmissionState_reserved3 = 6,
  DE_TransmissionState_unavailable = 7,
  DE_TransmissionState_MIN = DE_TransmissionState_neutral,
  DE_TransmissionState_MAX = DE_TransmissionState_unavailable
};

inline const DE_TransmissionState (&EnumValuesDE_TransmissionState())[8] {
  static const DE_TransmissionState values[] = {
    DE_TransmissionState_neutral,
    DE_TransmissionState_park,
    DE_TransmissionState_forwardGears,
    DE_TransmissionState_reverseGears,
    DE_TransmissionState_reserved1,
    DE_TransmissionState_reserved2,
    DE_TransmissionState_reserved3,
    DE_TransmissionState_unavailable
  };
  return values;
}

inline const char * const *EnumNamesDE_TransmissionState() {
  static const char * const names[9] = {
    "neutral",
    "park",
    "forwardGears",
    "reverseGears",
    "reserved1",
    "reserved2",
    "reserved3",
    "unavailable",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_TransmissionState(DE_TransmissionState e) {
  if (::flatbuffers::IsOutRange(e, DE_TransmissionState_neutral, DE_TransmissionState_unavailable)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_TransmissionState()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_TRANSMISSIONSTATE_MECDATA_H_
