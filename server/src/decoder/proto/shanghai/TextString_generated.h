// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_TEXTSTRING_MECDATA_H_
#define FLATBUFFERS_GENERATED_TEXTSTRING_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_TextString;
struct DF_TextStringBuilder;

struct DF_TextString FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_TextStringBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TEXTSTRING = 4
  };
  const ::flatbuffers::String *textString() const {
    return GetPointer<const ::flatbuffers::String *>(VT_TEXTSTRING);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_TEXTSTRING) &&
           verifier.VerifyString(textString()) &&
           verifier.EndTable();
  }
};

struct DF_TextStringBuilder {
  typedef DF_TextString Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_textString(::flatbuffers::Offset<::flatbuffers::String> textString) {
    fbb_.AddOffset(DF_TextString::VT_TEXTSTRING, textString);
  }
  explicit DF_TextStringBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_TextString> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_TextString>(end);
    fbb_.Required(o, DF_TextString::VT_TEXTSTRING);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_TextString> CreateDF_TextString(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> textString = 0) {
  DF_TextStringBuilder builder_(_fbb);
  builder_.add_textString(textString);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_TextString> CreateDF_TextStringDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *textString = nullptr) {
  auto textString__ = textString ? _fbb.CreateString(textString) : 0;
  return MECData::CreateDF_TextString(
      _fbb,
      textString__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_TEXTSTRING_MECDATA_H_
