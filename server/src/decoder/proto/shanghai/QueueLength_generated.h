// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_QUEUELENGTH_MECDATA_H_
#define FLATBUFFERS_GENERATED_QUEUELENGTH_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "TrafficFlow_generated.h"

namespace MECData {

struct MSG_QueueLength;
struct MSG_QueueLengthBuilder;

enum DE_QLengthUnit : uint8_t {
  DE_QLengthUnit_CENTIMETERS = 0,
  DE_QLengthUnit_VEHICLES = 1,
  DE_QLengthUnit_MIN = DE_QLengthUnit_CENTIMETERS,
  DE_QLengthUnit_MAX = DE_QLengthUnit_VEHICLES
};

inline const DE_QLengthUnit (&EnumValuesDE_QLengthUnit())[2] {
  static const DE_QLengthUnit values[] = {
    DE_QLengthUnit_CENTIMETERS,
    DE_QLengthUnit_VEHICLES
  };
  return values;
}

inline const char * const *EnumNamesDE_QLengthUnit() {
  static const char * const names[3] = {
    "CENTIMETERS",
    "VEHICLES",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_QLengthUnit(DE_QLengthUnit e) {
  if (::flatbuffers::IsOutRange(e, DE_QLengthUnit_CENTIMETERS, DE_QLengthUnit_VEHICLES)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_QLengthUnit()[index];
}

struct MSG_QueueLength FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_QueueLengthBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_DETECTOR_AREA_ID = 4,
    VT_DETECTOR_ID = 6,
    VT_TIMESTAMP = 8,
    VT_CYCLE = 10,
    VT_UNIT = 12,
    VT_VALUE = 14,
    VT_MAP_ELEMENT_TYPE = 16,
    VT_MAP_ELEMENT = 18,
    VT_MSG_ID = 20
  };
  uint32_t detector_area_id() const {
    return GetField<uint32_t>(VT_DETECTOR_AREA_ID, 0);
  }
  uint16_t detector_id() const {
    return GetField<uint16_t>(VT_DETECTOR_ID, 0);
  }
  int64_t timestamp() const {
    return GetField<int64_t>(VT_TIMESTAMP, 0);
  }
  uint16_t cycle() const {
    return GetField<uint16_t>(VT_CYCLE, 65535);
  }
  MECData::DE_QLengthUnit unit() const {
    return static_cast<MECData::DE_QLengthUnit>(GetField<uint8_t>(VT_UNIT, 0));
  }
  uint32_t value() const {
    return GetField<uint32_t>(VT_VALUE, 0);
  }
  MECData::DE_TrafficFlowStatMapElement map_element_type() const {
    return static_cast<MECData::DE_TrafficFlowStatMapElement>(GetField<uint8_t>(VT_MAP_ELEMENT_TYPE, 0));
  }
  const void *map_element() const {
    return GetPointer<const void *>(VT_MAP_ELEMENT);
  }
  template<typename T> const T *map_element_as() const;
  const MECData::DE_DetectorAreaStatInfo *map_element_as_DE_DetectorAreaStatInfo() const {
    return map_element_type() == MECData::DE_TrafficFlowStatMapElement_DE_DetectorAreaStatInfo ? static_cast<const MECData::DE_DetectorAreaStatInfo *>(map_element()) : nullptr;
  }
  const MECData::DE_LaneStatInfo *map_element_as_DE_LaneStatInfo() const {
    return map_element_type() == MECData::DE_TrafficFlowStatMapElement_DE_LaneStatInfo ? static_cast<const MECData::DE_LaneStatInfo *>(map_element()) : nullptr;
  }
  const MECData::DE_SectionStatInfo *map_element_as_DE_SectionStatInfo() const {
    return map_element_type() == MECData::DE_TrafficFlowStatMapElement_DE_SectionStatInfo ? static_cast<const MECData::DE_SectionStatInfo *>(map_element()) : nullptr;
  }
  const MECData::DE_LinkStatInfo *map_element_as_DE_LinkStatInfo() const {
    return map_element_type() == MECData::DE_TrafficFlowStatMapElement_DE_LinkStatInfo ? static_cast<const MECData::DE_LinkStatInfo *>(map_element()) : nullptr;
  }
  const MECData::DE_ConnectingLaneStatInfo *map_element_as_DE_ConnectingLaneStatInfo() const {
    return map_element_type() == MECData::DE_TrafficFlowStatMapElement_DE_ConnectingLaneStatInfo ? static_cast<const MECData::DE_ConnectingLaneStatInfo *>(map_element()) : nullptr;
  }
  const MECData::DE_ConnectionStatInfo *map_element_as_DE_ConnectionStatInfo() const {
    return map_element_type() == MECData::DE_TrafficFlowStatMapElement_DE_ConnectionStatInfo ? static_cast<const MECData::DE_ConnectionStatInfo *>(map_element()) : nullptr;
  }
  const MECData::DE_MovementStatInfo *map_element_as_DE_MovementStatInfo() const {
    return map_element_type() == MECData::DE_TrafficFlowStatMapElement_DE_MovementStatInfo ? static_cast<const MECData::DE_MovementStatInfo *>(map_element()) : nullptr;
  }
  const MECData::DE_NodeStatInfo *map_element_as_DE_NodeStatInfo() const {
    return map_element_type() == MECData::DE_TrafficFlowStatMapElement_DE_NodeStatInfo ? static_cast<const MECData::DE_NodeStatInfo *>(map_element()) : nullptr;
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_DETECTOR_AREA_ID, 4) &&
           VerifyField<uint16_t>(verifier, VT_DETECTOR_ID, 2) &&
           VerifyField<int64_t>(verifier, VT_TIMESTAMP, 8) &&
           VerifyField<uint16_t>(verifier, VT_CYCLE, 2) &&
           VerifyField<uint8_t>(verifier, VT_UNIT, 1) &&
           VerifyField<uint32_t>(verifier, VT_VALUE, 4) &&
           VerifyField<uint8_t>(verifier, VT_MAP_ELEMENT_TYPE, 1) &&
           VerifyOffset(verifier, VT_MAP_ELEMENT) &&
           VerifyDE_TrafficFlowStatMapElement(verifier, map_element(), map_element_type()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

template<> inline const MECData::DE_DetectorAreaStatInfo *MSG_QueueLength::map_element_as<MECData::DE_DetectorAreaStatInfo>() const {
  return map_element_as_DE_DetectorAreaStatInfo();
}

template<> inline const MECData::DE_LaneStatInfo *MSG_QueueLength::map_element_as<MECData::DE_LaneStatInfo>() const {
  return map_element_as_DE_LaneStatInfo();
}

template<> inline const MECData::DE_SectionStatInfo *MSG_QueueLength::map_element_as<MECData::DE_SectionStatInfo>() const {
  return map_element_as_DE_SectionStatInfo();
}

template<> inline const MECData::DE_LinkStatInfo *MSG_QueueLength::map_element_as<MECData::DE_LinkStatInfo>() const {
  return map_element_as_DE_LinkStatInfo();
}

template<> inline const MECData::DE_ConnectingLaneStatInfo *MSG_QueueLength::map_element_as<MECData::DE_ConnectingLaneStatInfo>() const {
  return map_element_as_DE_ConnectingLaneStatInfo();
}

template<> inline const MECData::DE_ConnectionStatInfo *MSG_QueueLength::map_element_as<MECData::DE_ConnectionStatInfo>() const {
  return map_element_as_DE_ConnectionStatInfo();
}

template<> inline const MECData::DE_MovementStatInfo *MSG_QueueLength::map_element_as<MECData::DE_MovementStatInfo>() const {
  return map_element_as_DE_MovementStatInfo();
}

template<> inline const MECData::DE_NodeStatInfo *MSG_QueueLength::map_element_as<MECData::DE_NodeStatInfo>() const {
  return map_element_as_DE_NodeStatInfo();
}

struct MSG_QueueLengthBuilder {
  typedef MSG_QueueLength Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_detector_area_id(uint32_t detector_area_id) {
    fbb_.AddElement<uint32_t>(MSG_QueueLength::VT_DETECTOR_AREA_ID, detector_area_id, 0);
  }
  void add_detector_id(uint16_t detector_id) {
    fbb_.AddElement<uint16_t>(MSG_QueueLength::VT_DETECTOR_ID, detector_id, 0);
  }
  void add_timestamp(int64_t timestamp) {
    fbb_.AddElement<int64_t>(MSG_QueueLength::VT_TIMESTAMP, timestamp, 0);
  }
  void add_cycle(uint16_t cycle) {
    fbb_.AddElement<uint16_t>(MSG_QueueLength::VT_CYCLE, cycle, 65535);
  }
  void add_unit(MECData::DE_QLengthUnit unit) {
    fbb_.AddElement<uint8_t>(MSG_QueueLength::VT_UNIT, static_cast<uint8_t>(unit), 0);
  }
  void add_value(uint32_t value) {
    fbb_.AddElement<uint32_t>(MSG_QueueLength::VT_VALUE, value, 0);
  }
  void add_map_element_type(MECData::DE_TrafficFlowStatMapElement map_element_type) {
    fbb_.AddElement<uint8_t>(MSG_QueueLength::VT_MAP_ELEMENT_TYPE, static_cast<uint8_t>(map_element_type), 0);
  }
  void add_map_element(::flatbuffers::Offset<void> map_element) {
    fbb_.AddOffset(MSG_QueueLength::VT_MAP_ELEMENT, map_element);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_QueueLength::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_QueueLengthBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_QueueLength> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_QueueLength>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_QueueLength> CreateMSG_QueueLength(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t detector_area_id = 0,
    uint16_t detector_id = 0,
    int64_t timestamp = 0,
    uint16_t cycle = 65535,
    MECData::DE_QLengthUnit unit = MECData::DE_QLengthUnit_CENTIMETERS,
    uint32_t value = 0,
    MECData::DE_TrafficFlowStatMapElement map_element_type = MECData::DE_TrafficFlowStatMapElement_NONE,
    ::flatbuffers::Offset<void> map_element = 0,
    int64_t msg_id = 0) {
  MSG_QueueLengthBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_timestamp(timestamp);
  builder_.add_map_element(map_element);
  builder_.add_value(value);
  builder_.add_detector_area_id(detector_area_id);
  builder_.add_cycle(cycle);
  builder_.add_detector_id(detector_id);
  builder_.add_map_element_type(map_element_type);
  builder_.add_unit(unit);
  return builder_.Finish();
}

inline const MECData::MSG_QueueLength *GetMSG_QueueLength(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_QueueLength>(buf);
}

inline const MECData::MSG_QueueLength *GetSizePrefixedMSG_QueueLength(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_QueueLength>(buf);
}

inline bool VerifyMSG_QueueLengthBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_QueueLength>(nullptr);
}

inline bool VerifySizePrefixedMSG_QueueLengthBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_QueueLength>(nullptr);
}

inline void FinishMSG_QueueLengthBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_QueueLength> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_QueueLengthBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_QueueLength> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_QUEUELENGTH_MECDATA_H_
