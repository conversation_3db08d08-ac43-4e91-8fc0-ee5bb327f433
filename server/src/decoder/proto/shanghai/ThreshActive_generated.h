// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_THRESHACTIVE_MECDATA_H_
#define FLATBUFFERS_GENERATED_THRESHACTIVE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_ThreshActive;
struct DF_ThreshActiveBuilder;

enum DE_MapElementType : uint8_t {
  DE_MapElementType_Node = 0,
  DE_MapElementType_Link = 1,
  DE_MapElementType_Movement = 2,
  DE_MapElementType_Lane = 3,
  DE_MapElementType_Connection = 4,
  DE_MapElementType_MIN = DE_MapElementType_Node,
  DE_MapElementType_MAX = DE_MapElementType_Connection
};

inline const DE_MapElementType (&EnumValuesDE_MapElementType())[5] {
  static const DE_MapElementType values[] = {
    DE_MapElementType_Node,
    DE_MapElementType_Link,
    DE_MapElementType_Movement,
    DE_MapElementType_Lane,
    DE_MapElementType_Connection
  };
  return values;
}

inline const char * const *EnumNamesDE_MapElementType() {
  static const char * const names[6] = {
    "Node",
    "Link",
    "Movement",
    "Lane",
    "Connection",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_MapElementType(DE_MapElementType e) {
  if (::flatbuffers::IsOutRange(e, DE_MapElementType_Node, DE_MapElementType_Connection)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_MapElementType()[index];
}

enum DE_ThreshActiveType : uint8_t {
  DE_ThreshActiveType_QueueOverflow = 0,
  DE_ThreshActiveType_QueueImbalance = 1,
  DE_ThreshActiveType_LowGreenUtilization = 2,
  DE_ThreshActiveType_MainstreamOvercapacity = 3,
  DE_ThreshActiveType_MIN = DE_ThreshActiveType_QueueOverflow,
  DE_ThreshActiveType_MAX = DE_ThreshActiveType_MainstreamOvercapacity
};

inline const DE_ThreshActiveType (&EnumValuesDE_ThreshActiveType())[4] {
  static const DE_ThreshActiveType values[] = {
    DE_ThreshActiveType_QueueOverflow,
    DE_ThreshActiveType_QueueImbalance,
    DE_ThreshActiveType_LowGreenUtilization,
    DE_ThreshActiveType_MainstreamOvercapacity
  };
  return values;
}

inline const char * const *EnumNamesDE_ThreshActiveType() {
  static const char * const names[5] = {
    "QueueOverflow",
    "QueueImbalance",
    "LowGreenUtilization",
    "MainstreamOvercapacity",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_ThreshActiveType(DE_ThreshActiveType e) {
  if (::flatbuffers::IsOutRange(e, DE_ThreshActiveType_QueueOverflow, DE_ThreshActiveType_MainstreamOvercapacity)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_ThreshActiveType()[index];
}

struct DF_ThreshActive FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ThreshActiveBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MAP_ELEMENT_TYPE = 4,
    VT_MAP_ELEMENT_EXT_ID = 6,
    VT_THRESH_ACTIVE_TYPE = 8
  };
  MECData::DE_MapElementType map_element_type() const {
    return static_cast<MECData::DE_MapElementType>(GetField<uint8_t>(VT_MAP_ELEMENT_TYPE, 0));
  }
  const ::flatbuffers::String *map_element_ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_MAP_ELEMENT_EXT_ID);
  }
  MECData::DE_ThreshActiveType thresh_active_type() const {
    return static_cast<MECData::DE_ThreshActiveType>(GetField<uint8_t>(VT_THRESH_ACTIVE_TYPE, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_MAP_ELEMENT_TYPE, 1) &&
           VerifyOffsetRequired(verifier, VT_MAP_ELEMENT_EXT_ID) &&
           verifier.VerifyString(map_element_ext_id()) &&
           VerifyField<uint8_t>(verifier, VT_THRESH_ACTIVE_TYPE, 1) &&
           verifier.EndTable();
  }
};

struct DF_ThreshActiveBuilder {
  typedef DF_ThreshActive Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_map_element_type(MECData::DE_MapElementType map_element_type) {
    fbb_.AddElement<uint8_t>(DF_ThreshActive::VT_MAP_ELEMENT_TYPE, static_cast<uint8_t>(map_element_type), 0);
  }
  void add_map_element_ext_id(::flatbuffers::Offset<::flatbuffers::String> map_element_ext_id) {
    fbb_.AddOffset(DF_ThreshActive::VT_MAP_ELEMENT_EXT_ID, map_element_ext_id);
  }
  void add_thresh_active_type(MECData::DE_ThreshActiveType thresh_active_type) {
    fbb_.AddElement<uint8_t>(DF_ThreshActive::VT_THRESH_ACTIVE_TYPE, static_cast<uint8_t>(thresh_active_type), 0);
  }
  explicit DF_ThreshActiveBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ThreshActive> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ThreshActive>(end);
    fbb_.Required(o, DF_ThreshActive::VT_MAP_ELEMENT_EXT_ID);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ThreshActive> CreateDF_ThreshActive(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    MECData::DE_MapElementType map_element_type = MECData::DE_MapElementType_Node,
    ::flatbuffers::Offset<::flatbuffers::String> map_element_ext_id = 0,
    MECData::DE_ThreshActiveType thresh_active_type = MECData::DE_ThreshActiveType_QueueOverflow) {
  DF_ThreshActiveBuilder builder_(_fbb);
  builder_.add_map_element_ext_id(map_element_ext_id);
  builder_.add_thresh_active_type(thresh_active_type);
  builder_.add_map_element_type(map_element_type);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ThreshActive> CreateDF_ThreshActiveDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    MECData::DE_MapElementType map_element_type = MECData::DE_MapElementType_Node,
    const char *map_element_ext_id = nullptr,
    MECData::DE_ThreshActiveType thresh_active_type = MECData::DE_ThreshActiveType_QueueOverflow) {
  auto map_element_ext_id__ = map_element_ext_id ? _fbb.CreateString(map_element_ext_id) : 0;
  return MECData::CreateDF_ThreshActive(
      _fbb,
      map_element_type,
      map_element_ext_id__,
      thresh_active_type);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_THRESHACTIVE_MECDATA_H_
