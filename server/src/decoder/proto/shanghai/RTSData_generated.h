// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_RTSDATA_MECDATA_H_
#define FLATBUFFERS_GENERATED_RTSDATA_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "Description_generated.h"
#include "EventOperationType_generated.h"
#include "PositionOffsetLLV_generated.h"
#include "RSITimeDetails_generated.h"
#include "ReferenceLink_generated.h"
#include "ReferencePath_generated.h"

namespace MECData {

struct DF_RTSData;
struct DF_RTSDataBuilder;

struct DF_RTSData FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_RTSDataBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_RTSID = 4,
    VT_SIGNTYPE = 6,
    VT_PRIORITY = 8,
    VT_SIGNPOS = 10,
    VT_TIMEDETAILS = 12,
    VT_DESCRIPTION_TYPE = 14,
    VT_DESCRIPTION = 16,
    VT_REFERENCEPATHS = 18,
    VT_REFERENCELINKS = 20,
    VT_MSG_ID = 22,
    VT_SESSION_ID = 24,
    VT_OPERATION_TYPE = 26
  };
  uint8_t rtsId() const {
    return GetField<uint8_t>(VT_RTSID, 255);
  }
  uint16_t signType() const {
    return GetField<uint16_t>(VT_SIGNTYPE, 65535);
  }
  uint8_t priority() const {
    return GetField<uint8_t>(VT_PRIORITY, 0);
  }
  const MECData::DF_PositionOffsetLLV *signPos() const {
    return GetPointer<const MECData::DF_PositionOffsetLLV *>(VT_SIGNPOS);
  }
  const MECData::DF_RSITimeDetails *timeDetails() const {
    return GetPointer<const MECData::DF_RSITimeDetails *>(VT_TIMEDETAILS);
  }
  MECData::DF_Description description_type() const {
    return static_cast<MECData::DF_Description>(GetField<uint8_t>(VT_DESCRIPTION_TYPE, 0));
  }
  const void *description() const {
    return GetPointer<const void *>(VT_DESCRIPTION);
  }
  template<typename T> const T *description_as() const;
  const MECData::DF_TextString *description_as_DF_TextString() const {
    return description_type() == MECData::DF_Description_DF_TextString ? static_cast<const MECData::DF_TextString *>(description()) : nullptr;
  }
  const MECData::DF_TextGB2312 *description_as_DF_TextGB2312() const {
    return description_type() == MECData::DF_Description_DF_TextGB2312 ? static_cast<const MECData::DF_TextGB2312 *>(description()) : nullptr;
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferencePath>> *referencePaths() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferencePath>> *>(VT_REFERENCEPATHS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferenceLink>> *referenceLinks() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferenceLink>> *>(VT_REFERENCELINKS);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  int64_t session_id() const {
    return GetField<int64_t>(VT_SESSION_ID, 0);
  }
  MECData::DE_EventOperationType operation_type() const {
    return static_cast<MECData::DE_EventOperationType>(GetField<uint8_t>(VT_OPERATION_TYPE, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_RTSID, 1) &&
           VerifyField<uint16_t>(verifier, VT_SIGNTYPE, 2) &&
           VerifyField<uint8_t>(verifier, VT_PRIORITY, 1) &&
           VerifyOffset(verifier, VT_SIGNPOS) &&
           verifier.VerifyTable(signPos()) &&
           VerifyOffset(verifier, VT_TIMEDETAILS) &&
           verifier.VerifyTable(timeDetails()) &&
           VerifyField<uint8_t>(verifier, VT_DESCRIPTION_TYPE, 1) &&
           VerifyOffset(verifier, VT_DESCRIPTION) &&
           VerifyDF_Description(verifier, description(), description_type()) &&
           VerifyOffset(verifier, VT_REFERENCEPATHS) &&
           verifier.VerifyVector(referencePaths()) &&
           verifier.VerifyVectorOfTables(referencePaths()) &&
           VerifyOffset(verifier, VT_REFERENCELINKS) &&
           verifier.VerifyVector(referenceLinks()) &&
           verifier.VerifyVectorOfTables(referenceLinks()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           VerifyField<int64_t>(verifier, VT_SESSION_ID, 8) &&
           VerifyField<uint8_t>(verifier, VT_OPERATION_TYPE, 1) &&
           verifier.EndTable();
  }
};

template<> inline const MECData::DF_TextString *DF_RTSData::description_as<MECData::DF_TextString>() const {
  return description_as_DF_TextString();
}

template<> inline const MECData::DF_TextGB2312 *DF_RTSData::description_as<MECData::DF_TextGB2312>() const {
  return description_as_DF_TextGB2312();
}

struct DF_RTSDataBuilder {
  typedef DF_RTSData Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_rtsId(uint8_t rtsId) {
    fbb_.AddElement<uint8_t>(DF_RTSData::VT_RTSID, rtsId, 255);
  }
  void add_signType(uint16_t signType) {
    fbb_.AddElement<uint16_t>(DF_RTSData::VT_SIGNTYPE, signType, 65535);
  }
  void add_priority(uint8_t priority) {
    fbb_.AddElement<uint8_t>(DF_RTSData::VT_PRIORITY, priority, 0);
  }
  void add_signPos(::flatbuffers::Offset<MECData::DF_PositionOffsetLLV> signPos) {
    fbb_.AddOffset(DF_RTSData::VT_SIGNPOS, signPos);
  }
  void add_timeDetails(::flatbuffers::Offset<MECData::DF_RSITimeDetails> timeDetails) {
    fbb_.AddOffset(DF_RTSData::VT_TIMEDETAILS, timeDetails);
  }
  void add_description_type(MECData::DF_Description description_type) {
    fbb_.AddElement<uint8_t>(DF_RTSData::VT_DESCRIPTION_TYPE, static_cast<uint8_t>(description_type), 0);
  }
  void add_description(::flatbuffers::Offset<void> description) {
    fbb_.AddOffset(DF_RTSData::VT_DESCRIPTION, description);
  }
  void add_referencePaths(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferencePath>>> referencePaths) {
    fbb_.AddOffset(DF_RTSData::VT_REFERENCEPATHS, referencePaths);
  }
  void add_referenceLinks(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferenceLink>>> referenceLinks) {
    fbb_.AddOffset(DF_RTSData::VT_REFERENCELINKS, referenceLinks);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(DF_RTSData::VT_MSG_ID, msg_id, 0);
  }
  void add_session_id(int64_t session_id) {
    fbb_.AddElement<int64_t>(DF_RTSData::VT_SESSION_ID, session_id, 0);
  }
  void add_operation_type(MECData::DE_EventOperationType operation_type) {
    fbb_.AddElement<uint8_t>(DF_RTSData::VT_OPERATION_TYPE, static_cast<uint8_t>(operation_type), 0);
  }
  explicit DF_RTSDataBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_RTSData> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_RTSData>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_RTSData> CreateDF_RTSData(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t rtsId = 255,
    uint16_t signType = 65535,
    uint8_t priority = 0,
    ::flatbuffers::Offset<MECData::DF_PositionOffsetLLV> signPos = 0,
    ::flatbuffers::Offset<MECData::DF_RSITimeDetails> timeDetails = 0,
    MECData::DF_Description description_type = MECData::DF_Description_NONE,
    ::flatbuffers::Offset<void> description = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferencePath>>> referencePaths = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferenceLink>>> referenceLinks = 0,
    int64_t msg_id = 0,
    int64_t session_id = 0,
    MECData::DE_EventOperationType operation_type = MECData::DE_EventOperationType_AUTO_FORWARD) {
  DF_RTSDataBuilder builder_(_fbb);
  builder_.add_session_id(session_id);
  builder_.add_msg_id(msg_id);
  builder_.add_referenceLinks(referenceLinks);
  builder_.add_referencePaths(referencePaths);
  builder_.add_description(description);
  builder_.add_timeDetails(timeDetails);
  builder_.add_signPos(signPos);
  builder_.add_signType(signType);
  builder_.add_operation_type(operation_type);
  builder_.add_description_type(description_type);
  builder_.add_priority(priority);
  builder_.add_rtsId(rtsId);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_RTSData> CreateDF_RTSDataDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t rtsId = 255,
    uint16_t signType = 65535,
    uint8_t priority = 0,
    ::flatbuffers::Offset<MECData::DF_PositionOffsetLLV> signPos = 0,
    ::flatbuffers::Offset<MECData::DF_RSITimeDetails> timeDetails = 0,
    MECData::DF_Description description_type = MECData::DF_Description_NONE,
    ::flatbuffers::Offset<void> description = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_ReferencePath>> *referencePaths = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_ReferenceLink>> *referenceLinks = nullptr,
    int64_t msg_id = 0,
    int64_t session_id = 0,
    MECData::DE_EventOperationType operation_type = MECData::DE_EventOperationType_AUTO_FORWARD) {
  auto referencePaths__ = referencePaths ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_ReferencePath>>(*referencePaths) : 0;
  auto referenceLinks__ = referenceLinks ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_ReferenceLink>>(*referenceLinks) : 0;
  return MECData::CreateDF_RTSData(
      _fbb,
      rtsId,
      signType,
      priority,
      signPos,
      timeDetails,
      description_type,
      description,
      referencePaths__,
      referenceLinks__,
      msg_id,
      session_id,
      operation_type);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_RTSDATA_MECDATA_H_
