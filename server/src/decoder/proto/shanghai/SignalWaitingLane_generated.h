// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_SIGNALWAITINGLANE_MECDATA_H_
#define FLATBUFFERS_GENERATED_SIGNALWAITINGLANE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "RoadPoint_generated.h"

namespace MECData {

struct DF_SingleWaitingLane;
struct DF_SingleWaitingLaneBuilder;

struct DF_SingleWaitingLane FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SingleWaitingLaneBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_LANEWIDTH = 4,
    VT_POINTS = 6,
    VT_ALLOWEDPHASEIDS = 8
  };
  uint16_t laneWidth() const {
    return GetField<uint16_t>(VT_LANEWIDTH, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>> *points() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>> *>(VT_POINTS);
  }
  const ::flatbuffers::Vector<uint8_t> *allowedPhaseIDs() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_ALLOWEDPHASEIDS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_LANEWIDTH, 2) &&
           VerifyOffset(verifier, VT_POINTS) &&
           verifier.VerifyVector(points()) &&
           verifier.VerifyVectorOfTables(points()) &&
           VerifyOffset(verifier, VT_ALLOWEDPHASEIDS) &&
           verifier.VerifyVector(allowedPhaseIDs()) &&
           verifier.EndTable();
  }
};

struct DF_SingleWaitingLaneBuilder {
  typedef DF_SingleWaitingLane Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_laneWidth(uint16_t laneWidth) {
    fbb_.AddElement<uint16_t>(DF_SingleWaitingLane::VT_LANEWIDTH, laneWidth, 0);
  }
  void add_points(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>>> points) {
    fbb_.AddOffset(DF_SingleWaitingLane::VT_POINTS, points);
  }
  void add_allowedPhaseIDs(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> allowedPhaseIDs) {
    fbb_.AddOffset(DF_SingleWaitingLane::VT_ALLOWEDPHASEIDS, allowedPhaseIDs);
  }
  explicit DF_SingleWaitingLaneBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_SingleWaitingLane> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_SingleWaitingLane>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_SingleWaitingLane> CreateDF_SingleWaitingLane(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t laneWidth = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>>> points = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> allowedPhaseIDs = 0) {
  DF_SingleWaitingLaneBuilder builder_(_fbb);
  builder_.add_allowedPhaseIDs(allowedPhaseIDs);
  builder_.add_points(points);
  builder_.add_laneWidth(laneWidth);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_SingleWaitingLane> CreateDF_SingleWaitingLaneDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t laneWidth = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_RoadPoint>> *points = nullptr,
    const std::vector<uint8_t> *allowedPhaseIDs = nullptr) {
  auto points__ = points ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_RoadPoint>>(*points) : 0;
  auto allowedPhaseIDs__ = allowedPhaseIDs ? _fbb.CreateVector<uint8_t>(*allowedPhaseIDs) : 0;
  return MECData::CreateDF_SingleWaitingLane(
      _fbb,
      laneWidth,
      points__,
      allowedPhaseIDs__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_SIGNALWAITINGLANE_MECDATA_H_
