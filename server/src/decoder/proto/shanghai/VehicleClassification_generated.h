// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_VEHICLECLASSIFICATION_MECDATA_H_
#define FLATBUFFERS_GENERATED_VEHICLECLASSIFICATION_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "BasicVehicleClass_generated.h"

namespace MECData {

struct DF_VehicleClassification;
struct DF_VehicleClassificationBuilder;

struct DF_VehicleClassification FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_VehicleClassificationBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_CLASSIFICATION = 4,
    VT_FUELTYPE = 6
  };
  MECData::DE_BasicVehicleClass classification() const {
    return static_cast<MECData::DE_BasicVehicleClass>(GetField<uint8_t>(VT_CLASSIFICATION, 0));
  }
  uint8_t fuelType() const {
    return GetField<uint8_t>(VT_FUELTYPE, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_CLASSIFICATION, 1) &&
           VerifyField<uint8_t>(verifier, VT_FUELTYPE, 1) &&
           verifier.EndTable();
  }
};

struct DF_VehicleClassificationBuilder {
  typedef DF_VehicleClassification Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_classification(MECData::DE_BasicVehicleClass classification) {
    fbb_.AddElement<uint8_t>(DF_VehicleClassification::VT_CLASSIFICATION, static_cast<uint8_t>(classification), 0);
  }
  void add_fuelType(uint8_t fuelType) {
    fbb_.AddElement<uint8_t>(DF_VehicleClassification::VT_FUELTYPE, fuelType, 0);
  }
  explicit DF_VehicleClassificationBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_VehicleClassification> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_VehicleClassification>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_VehicleClassification> CreateDF_VehicleClassification(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    MECData::DE_BasicVehicleClass classification = MECData::DE_BasicVehicleClass_unknownVehicleClass,
    uint8_t fuelType = 0) {
  DF_VehicleClassificationBuilder builder_(_fbb);
  builder_.add_fuelType(fuelType);
  builder_.add_classification(classification);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_VEHICLECLASSIFICATION_MECDATA_H_
