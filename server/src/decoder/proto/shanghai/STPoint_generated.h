// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_STPOINT_MECDATA_H_
#define FLATBUFFERS_GENERATED_STPOINT_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_STPoint;
struct DF_STPointBuilder;

struct DF_STPoint FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_STPointBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_S_AXIS = 4,
    VT_T_AXIS = 6
  };
  int32_t s_axis() const {
    return GetField<int32_t>(VT_S_AXIS, 0);
  }
  int32_t t_axis() const {
    return GetField<int32_t>(VT_T_AXIS, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_S_AXIS, 4) &&
           VerifyField<int32_t>(verifier, VT_T_AXIS, 4) &&
           verifier.EndTable();
  }
};

struct DF_STPointBuilder {
  typedef DF_STPoint Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_s_axis(int32_t s_axis) {
    fbb_.AddElement<int32_t>(DF_STPoint::VT_S_AXIS, s_axis, 0);
  }
  void add_t_axis(int32_t t_axis) {
    fbb_.AddElement<int32_t>(DF_STPoint::VT_T_AXIS, t_axis, 0);
  }
  explicit DF_STPointBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_STPoint> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_STPoint>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_STPoint> CreateDF_STPoint(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t s_axis = 0,
    int32_t t_axis = 0) {
  DF_STPointBuilder builder_(_fbb);
  builder_.add_t_axis(t_axis);
  builder_.add_s_axis(s_axis);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_STPOINT_MECDATA_H_
