// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_SPEEDGUIDE_MECDATA_H_
#define FLATBUFFERS_GENERATED_SPEEDGUIDE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DE_GuideSpeedPointValue;
struct DE_GuideSpeedPointValueBuilder;

struct DE_GuideSpeedIntervalValue;
struct DE_GuideSpeedIntervalValueBuilder;

struct DF_SpeedGuideInfo;
struct DF_SpeedGuideInfoBuilder;

struct MSG_SpeedGuide;
struct MSG_SpeedGuideBuilder;

enum DF_GuideSpeedValue : uint8_t {
  DF_GuideSpeedValue_NONE = 0,
  DF_GuideSpeedValue_DE_GuideSpeedPointValue = 1,
  DF_GuideSpeedValue_DE_GuideSpeedIntervalValue = 2,
  DF_GuideSpeedValue_MIN = DF_GuideSpeedValue_NONE,
  DF_GuideSpeedValue_MAX = DF_GuideSpeedValue_DE_GuideSpeedIntervalValue
};

inline const DF_GuideSpeedValue (&EnumValuesDF_GuideSpeedValue())[3] {
  static const DF_GuideSpeedValue values[] = {
    DF_GuideSpeedValue_NONE,
    DF_GuideSpeedValue_DE_GuideSpeedPointValue,
    DF_GuideSpeedValue_DE_GuideSpeedIntervalValue
  };
  return values;
}

inline const char * const *EnumNamesDF_GuideSpeedValue() {
  static const char * const names[4] = {
    "NONE",
    "DE_GuideSpeedPointValue",
    "DE_GuideSpeedIntervalValue",
    nullptr
  };
  return names;
}

inline const char *EnumNameDF_GuideSpeedValue(DF_GuideSpeedValue e) {
  if (::flatbuffers::IsOutRange(e, DF_GuideSpeedValue_NONE, DF_GuideSpeedValue_DE_GuideSpeedIntervalValue)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDF_GuideSpeedValue()[index];
}

template<typename T> struct DF_GuideSpeedValueTraits {
  static const DF_GuideSpeedValue enum_value = DF_GuideSpeedValue_NONE;
};

template<> struct DF_GuideSpeedValueTraits<MECData::DE_GuideSpeedPointValue> {
  static const DF_GuideSpeedValue enum_value = DF_GuideSpeedValue_DE_GuideSpeedPointValue;
};

template<> struct DF_GuideSpeedValueTraits<MECData::DE_GuideSpeedIntervalValue> {
  static const DF_GuideSpeedValue enum_value = DF_GuideSpeedValue_DE_GuideSpeedIntervalValue;
};

bool VerifyDF_GuideSpeedValue(::flatbuffers::Verifier &verifier, const void *obj, DF_GuideSpeedValue type);
bool VerifyDF_GuideSpeedValueVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types);

struct DE_GuideSpeedPointValue FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_GuideSpeedPointValueBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SPEED = 4
  };
  int32_t speed() const {
    return GetField<int32_t>(VT_SPEED, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_SPEED, 4) &&
           verifier.EndTable();
  }
};

struct DE_GuideSpeedPointValueBuilder {
  typedef DE_GuideSpeedPointValue Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_speed(int32_t speed) {
    fbb_.AddElement<int32_t>(DE_GuideSpeedPointValue::VT_SPEED, speed, 0);
  }
  explicit DE_GuideSpeedPointValueBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_GuideSpeedPointValue> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_GuideSpeedPointValue>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_GuideSpeedPointValue> CreateDE_GuideSpeedPointValue(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t speed = 0) {
  DE_GuideSpeedPointValueBuilder builder_(_fbb);
  builder_.add_speed(speed);
  return builder_.Finish();
}

struct DE_GuideSpeedIntervalValue FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_GuideSpeedIntervalValueBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SPEED_LBOUND = 4,
    VT_SPEED_UBOUND = 6
  };
  int32_t speed_lbound() const {
    return GetField<int32_t>(VT_SPEED_LBOUND, 0);
  }
  int32_t speed_ubound() const {
    return GetField<int32_t>(VT_SPEED_UBOUND, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_SPEED_LBOUND, 4) &&
           VerifyField<int32_t>(verifier, VT_SPEED_UBOUND, 4) &&
           verifier.EndTable();
  }
};

struct DE_GuideSpeedIntervalValueBuilder {
  typedef DE_GuideSpeedIntervalValue Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_speed_lbound(int32_t speed_lbound) {
    fbb_.AddElement<int32_t>(DE_GuideSpeedIntervalValue::VT_SPEED_LBOUND, speed_lbound, 0);
  }
  void add_speed_ubound(int32_t speed_ubound) {
    fbb_.AddElement<int32_t>(DE_GuideSpeedIntervalValue::VT_SPEED_UBOUND, speed_ubound, 0);
  }
  explicit DE_GuideSpeedIntervalValueBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_GuideSpeedIntervalValue> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_GuideSpeedIntervalValue>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_GuideSpeedIntervalValue> CreateDE_GuideSpeedIntervalValue(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t speed_lbound = 0,
    int32_t speed_ubound = 0) {
  DE_GuideSpeedIntervalValueBuilder builder_(_fbb);
  builder_.add_speed_ubound(speed_ubound);
  builder_.add_speed_lbound(speed_lbound);
  return builder_.Finish();
}

struct DF_SpeedGuideInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SpeedGuideInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TIME = 4,
    VT_SPEED = 6,
    VT_GUIDE_TYPE = 8,
    VT_GUIDE = 10
  };
  int64_t time() const {
    return GetField<int64_t>(VT_TIME, 0);
  }
  int32_t speed() const {
    return GetField<int32_t>(VT_SPEED, 0);
  }
  MECData::DF_GuideSpeedValue guide_type() const {
    return static_cast<MECData::DF_GuideSpeedValue>(GetField<uint8_t>(VT_GUIDE_TYPE, 0));
  }
  const void *guide() const {
    return GetPointer<const void *>(VT_GUIDE);
  }
  template<typename T> const T *guide_as() const;
  const MECData::DE_GuideSpeedPointValue *guide_as_DE_GuideSpeedPointValue() const {
    return guide_type() == MECData::DF_GuideSpeedValue_DE_GuideSpeedPointValue ? static_cast<const MECData::DE_GuideSpeedPointValue *>(guide()) : nullptr;
  }
  const MECData::DE_GuideSpeedIntervalValue *guide_as_DE_GuideSpeedIntervalValue() const {
    return guide_type() == MECData::DF_GuideSpeedValue_DE_GuideSpeedIntervalValue ? static_cast<const MECData::DE_GuideSpeedIntervalValue *>(guide()) : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int64_t>(verifier, VT_TIME, 8) &&
           VerifyField<int32_t>(verifier, VT_SPEED, 4) &&
           VerifyField<uint8_t>(verifier, VT_GUIDE_TYPE, 1) &&
           VerifyOffset(verifier, VT_GUIDE) &&
           VerifyDF_GuideSpeedValue(verifier, guide(), guide_type()) &&
           verifier.EndTable();
  }
};

template<> inline const MECData::DE_GuideSpeedPointValue *DF_SpeedGuideInfo::guide_as<MECData::DE_GuideSpeedPointValue>() const {
  return guide_as_DE_GuideSpeedPointValue();
}

template<> inline const MECData::DE_GuideSpeedIntervalValue *DF_SpeedGuideInfo::guide_as<MECData::DE_GuideSpeedIntervalValue>() const {
  return guide_as_DE_GuideSpeedIntervalValue();
}

struct DF_SpeedGuideInfoBuilder {
  typedef DF_SpeedGuideInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_time(int64_t time) {
    fbb_.AddElement<int64_t>(DF_SpeedGuideInfo::VT_TIME, time, 0);
  }
  void add_speed(int32_t speed) {
    fbb_.AddElement<int32_t>(DF_SpeedGuideInfo::VT_SPEED, speed, 0);
  }
  void add_guide_type(MECData::DF_GuideSpeedValue guide_type) {
    fbb_.AddElement<uint8_t>(DF_SpeedGuideInfo::VT_GUIDE_TYPE, static_cast<uint8_t>(guide_type), 0);
  }
  void add_guide(::flatbuffers::Offset<void> guide) {
    fbb_.AddOffset(DF_SpeedGuideInfo::VT_GUIDE, guide);
  }
  explicit DF_SpeedGuideInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_SpeedGuideInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_SpeedGuideInfo>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_SpeedGuideInfo> CreateDF_SpeedGuideInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int64_t time = 0,
    int32_t speed = 0,
    MECData::DF_GuideSpeedValue guide_type = MECData::DF_GuideSpeedValue_NONE,
    ::flatbuffers::Offset<void> guide = 0) {
  DF_SpeedGuideInfoBuilder builder_(_fbb);
  builder_.add_time(time);
  builder_.add_guide(guide);
  builder_.add_speed(speed);
  builder_.add_guide_type(guide_type);
  return builder_.Finish();
}

struct MSG_SpeedGuide FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_SpeedGuideBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MEC_ID = 4,
    VT_VEH_ID = 6,
    VT_VEH_ID_EXT = 8,
    VT_TIME = 10,
    VT_GUIDE_INFO = 12,
    VT_MSG_ID = 14
  };
  const ::flatbuffers::String *mec_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_MEC_ID);
  }
  uint16_t veh_id() const {
    return GetField<uint16_t>(VT_VEH_ID, 0);
  }
  const ::flatbuffers::String *veh_id_ext() const {
    return GetPointer<const ::flatbuffers::String *>(VT_VEH_ID_EXT);
  }
  int64_t time() const {
    return GetField<int64_t>(VT_TIME, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SpeedGuideInfo>> *guide_info() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SpeedGuideInfo>> *>(VT_GUIDE_INFO);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_MEC_ID) &&
           verifier.VerifyString(mec_id()) &&
           VerifyField<uint16_t>(verifier, VT_VEH_ID, 2) &&
           VerifyOffset(verifier, VT_VEH_ID_EXT) &&
           verifier.VerifyString(veh_id_ext()) &&
           VerifyField<int64_t>(verifier, VT_TIME, 8) &&
           VerifyOffset(verifier, VT_GUIDE_INFO) &&
           verifier.VerifyVector(guide_info()) &&
           verifier.VerifyVectorOfTables(guide_info()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct MSG_SpeedGuideBuilder {
  typedef MSG_SpeedGuide Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_mec_id(::flatbuffers::Offset<::flatbuffers::String> mec_id) {
    fbb_.AddOffset(MSG_SpeedGuide::VT_MEC_ID, mec_id);
  }
  void add_veh_id(uint16_t veh_id) {
    fbb_.AddElement<uint16_t>(MSG_SpeedGuide::VT_VEH_ID, veh_id, 0);
  }
  void add_veh_id_ext(::flatbuffers::Offset<::flatbuffers::String> veh_id_ext) {
    fbb_.AddOffset(MSG_SpeedGuide::VT_VEH_ID_EXT, veh_id_ext);
  }
  void add_time(int64_t time) {
    fbb_.AddElement<int64_t>(MSG_SpeedGuide::VT_TIME, time, 0);
  }
  void add_guide_info(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SpeedGuideInfo>>> guide_info) {
    fbb_.AddOffset(MSG_SpeedGuide::VT_GUIDE_INFO, guide_info);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_SpeedGuide::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_SpeedGuideBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_SpeedGuide> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_SpeedGuide>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_SpeedGuide> CreateMSG_SpeedGuide(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> mec_id = 0,
    uint16_t veh_id = 0,
    ::flatbuffers::Offset<::flatbuffers::String> veh_id_ext = 0,
    int64_t time = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SpeedGuideInfo>>> guide_info = 0,
    int64_t msg_id = 0) {
  MSG_SpeedGuideBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_time(time);
  builder_.add_guide_info(guide_info);
  builder_.add_veh_id_ext(veh_id_ext);
  builder_.add_mec_id(mec_id);
  builder_.add_veh_id(veh_id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_SpeedGuide> CreateMSG_SpeedGuideDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *mec_id = nullptr,
    uint16_t veh_id = 0,
    const char *veh_id_ext = nullptr,
    int64_t time = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_SpeedGuideInfo>> *guide_info = nullptr,
    int64_t msg_id = 0) {
  auto mec_id__ = mec_id ? _fbb.CreateString(mec_id) : 0;
  auto veh_id_ext__ = veh_id_ext ? _fbb.CreateString(veh_id_ext) : 0;
  auto guide_info__ = guide_info ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_SpeedGuideInfo>>(*guide_info) : 0;
  return MECData::CreateMSG_SpeedGuide(
      _fbb,
      mec_id__,
      veh_id,
      veh_id_ext__,
      time,
      guide_info__,
      msg_id);
}

inline bool VerifyDF_GuideSpeedValue(::flatbuffers::Verifier &verifier, const void *obj, DF_GuideSpeedValue type) {
  switch (type) {
    case DF_GuideSpeedValue_NONE: {
      return true;
    }
    case DF_GuideSpeedValue_DE_GuideSpeedPointValue: {
      auto ptr = reinterpret_cast<const MECData::DE_GuideSpeedPointValue *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_GuideSpeedValue_DE_GuideSpeedIntervalValue: {
      auto ptr = reinterpret_cast<const MECData::DE_GuideSpeedIntervalValue *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return true;
  }
}

inline bool VerifyDF_GuideSpeedValueVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (::flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyDF_GuideSpeedValue(
        verifier,  values->Get(i), types->GetEnum<DF_GuideSpeedValue>(i))) {
      return false;
    }
  }
  return true;
}

inline const MECData::MSG_SpeedGuide *GetMSG_SpeedGuide(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_SpeedGuide>(buf);
}

inline const MECData::MSG_SpeedGuide *GetSizePrefixedMSG_SpeedGuide(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_SpeedGuide>(buf);
}

inline bool VerifyMSG_SpeedGuideBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_SpeedGuide>(nullptr);
}

inline bool VerifySizePrefixedMSG_SpeedGuideBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_SpeedGuide>(nullptr);
}

inline void FinishMSG_SpeedGuideBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_SpeedGuide> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_SpeedGuideBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_SpeedGuide> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_SPEEDGUIDE_MECDATA_H_
