// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_SIGNALCONTROLLERCONFIG_MECDATA_H_
#define FLATBUFFERS_GENERATED_SIGNALCONTROLLERCONFIG_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "LocalTimePoint_generated.h"
#include "Month_generated.h"
#include "NodeReferenceID_generated.h"
#include "PhaseCharacteristics_generated.h"
#include "PhasicConstraint_generated.h"
#include "SignalControlMode_generated.h"
#include "Weekday_generated.h"

namespace MECData {

struct DF_GreenConflicts;
struct DF_GreenConflictsBuilder;

struct DF_GreenInterval;
struct DF_GreenIntervalBuilder;

struct DF_SCSchedule;
struct DF_SCScheduleBuilder;

struct DF_DayPlanAction;
struct DF_DayPlanActionBuilder;

struct DF_DayPlan;
struct DF_DayPlanBuilder;

struct DF_SignalControllerConfig;
struct DF_SignalControllerConfigBuilder;

struct DF_SignalSchemeConstraint;
struct DF_SignalSchemeConstraintBuilder;

struct DF_GreenConflicts FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_GreenConflictsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PHASE_ID = 4,
    VT_CONFLICT_PHASE_IDS = 6
  };
  uint8_t phase_id() const {
    return GetField<uint8_t>(VT_PHASE_ID, 0);
  }
  const ::flatbuffers::Vector<uint8_t> *conflict_phase_ids() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_CONFLICT_PHASE_IDS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_PHASE_ID, 1) &&
           VerifyOffset(verifier, VT_CONFLICT_PHASE_IDS) &&
           verifier.VerifyVector(conflict_phase_ids()) &&
           verifier.EndTable();
  }
};

struct DF_GreenConflictsBuilder {
  typedef DF_GreenConflicts Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_phase_id(uint8_t phase_id) {
    fbb_.AddElement<uint8_t>(DF_GreenConflicts::VT_PHASE_ID, phase_id, 0);
  }
  void add_conflict_phase_ids(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> conflict_phase_ids) {
    fbb_.AddOffset(DF_GreenConflicts::VT_CONFLICT_PHASE_IDS, conflict_phase_ids);
  }
  explicit DF_GreenConflictsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_GreenConflicts> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_GreenConflicts>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_GreenConflicts> CreateDF_GreenConflicts(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t phase_id = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> conflict_phase_ids = 0) {
  DF_GreenConflictsBuilder builder_(_fbb);
  builder_.add_conflict_phase_ids(conflict_phase_ids);
  builder_.add_phase_id(phase_id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_GreenConflicts> CreateDF_GreenConflictsDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t phase_id = 0,
    const std::vector<uint8_t> *conflict_phase_ids = nullptr) {
  auto conflict_phase_ids__ = conflict_phase_ids ? _fbb.CreateVector<uint8_t>(*conflict_phase_ids) : 0;
  return MECData::CreateDF_GreenConflicts(
      _fbb,
      phase_id,
      conflict_phase_ids__);
}

struct DF_GreenInterval FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_GreenIntervalBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PHASE_ID_END = 4,
    VT_PHASE_ID_START = 6,
    VT_INTERVAL = 8
  };
  uint8_t phase_id_end() const {
    return GetField<uint8_t>(VT_PHASE_ID_END, 0);
  }
  uint8_t phase_id_start() const {
    return GetField<uint8_t>(VT_PHASE_ID_START, 0);
  }
  int16_t interval() const {
    return GetField<int16_t>(VT_INTERVAL, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_PHASE_ID_END, 1) &&
           VerifyField<uint8_t>(verifier, VT_PHASE_ID_START, 1) &&
           VerifyField<int16_t>(verifier, VT_INTERVAL, 2) &&
           verifier.EndTable();
  }
};

struct DF_GreenIntervalBuilder {
  typedef DF_GreenInterval Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_phase_id_end(uint8_t phase_id_end) {
    fbb_.AddElement<uint8_t>(DF_GreenInterval::VT_PHASE_ID_END, phase_id_end, 0);
  }
  void add_phase_id_start(uint8_t phase_id_start) {
    fbb_.AddElement<uint8_t>(DF_GreenInterval::VT_PHASE_ID_START, phase_id_start, 0);
  }
  void add_interval(int16_t interval) {
    fbb_.AddElement<int16_t>(DF_GreenInterval::VT_INTERVAL, interval, 0);
  }
  explicit DF_GreenIntervalBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_GreenInterval> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_GreenInterval>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_GreenInterval> CreateDF_GreenInterval(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t phase_id_end = 0,
    uint8_t phase_id_start = 0,
    int16_t interval = 0) {
  DF_GreenIntervalBuilder builder_(_fbb);
  builder_.add_interval(interval);
  builder_.add_phase_id_start(phase_id_start);
  builder_.add_phase_id_end(phase_id_end);
  return builder_.Finish();
}

struct DF_SCSchedule FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SCScheduleBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SCHEDULE_ID = 4,
    VT_MONTH_FILTER = 6,
    VT_DAY_FILTER = 8,
    VT_WEEKDAY_FILTER = 10,
    VT_PRIORITY = 12,
    VT_DAY_PLAN_ID = 14
  };
  uint8_t schedule_id() const {
    return GetField<uint8_t>(VT_SCHEDULE_ID, 0);
  }
  const ::flatbuffers::Vector<uint8_t> *month_filter() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_MONTH_FILTER);
  }
  const ::flatbuffers::Vector<uint8_t> *day_filter() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_DAY_FILTER);
  }
  const ::flatbuffers::Vector<uint8_t> *weekday_filter() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_WEEKDAY_FILTER);
  }
  uint8_t priority() const {
    return GetField<uint8_t>(VT_PRIORITY, 0);
  }
  uint8_t day_plan_id() const {
    return GetField<uint8_t>(VT_DAY_PLAN_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_SCHEDULE_ID, 1) &&
           VerifyOffset(verifier, VT_MONTH_FILTER) &&
           verifier.VerifyVector(month_filter()) &&
           VerifyOffset(verifier, VT_DAY_FILTER) &&
           verifier.VerifyVector(day_filter()) &&
           VerifyOffset(verifier, VT_WEEKDAY_FILTER) &&
           verifier.VerifyVector(weekday_filter()) &&
           VerifyField<uint8_t>(verifier, VT_PRIORITY, 1) &&
           VerifyField<uint8_t>(verifier, VT_DAY_PLAN_ID, 1) &&
           verifier.EndTable();
  }
};

struct DF_SCScheduleBuilder {
  typedef DF_SCSchedule Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_schedule_id(uint8_t schedule_id) {
    fbb_.AddElement<uint8_t>(DF_SCSchedule::VT_SCHEDULE_ID, schedule_id, 0);
  }
  void add_month_filter(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> month_filter) {
    fbb_.AddOffset(DF_SCSchedule::VT_MONTH_FILTER, month_filter);
  }
  void add_day_filter(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> day_filter) {
    fbb_.AddOffset(DF_SCSchedule::VT_DAY_FILTER, day_filter);
  }
  void add_weekday_filter(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> weekday_filter) {
    fbb_.AddOffset(DF_SCSchedule::VT_WEEKDAY_FILTER, weekday_filter);
  }
  void add_priority(uint8_t priority) {
    fbb_.AddElement<uint8_t>(DF_SCSchedule::VT_PRIORITY, priority, 0);
  }
  void add_day_plan_id(uint8_t day_plan_id) {
    fbb_.AddElement<uint8_t>(DF_SCSchedule::VT_DAY_PLAN_ID, day_plan_id, 0);
  }
  explicit DF_SCScheduleBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_SCSchedule> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_SCSchedule>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_SCSchedule> CreateDF_SCSchedule(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t schedule_id = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> month_filter = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> day_filter = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> weekday_filter = 0,
    uint8_t priority = 0,
    uint8_t day_plan_id = 0) {
  DF_SCScheduleBuilder builder_(_fbb);
  builder_.add_weekday_filter(weekday_filter);
  builder_.add_day_filter(day_filter);
  builder_.add_month_filter(month_filter);
  builder_.add_day_plan_id(day_plan_id);
  builder_.add_priority(priority);
  builder_.add_schedule_id(schedule_id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_SCSchedule> CreateDF_SCScheduleDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t schedule_id = 0,
    const std::vector<uint8_t> *month_filter = nullptr,
    const std::vector<uint8_t> *day_filter = nullptr,
    const std::vector<uint8_t> *weekday_filter = nullptr,
    uint8_t priority = 0,
    uint8_t day_plan_id = 0) {
  auto month_filter__ = month_filter ? _fbb.CreateVector<uint8_t>(*month_filter) : 0;
  auto day_filter__ = day_filter ? _fbb.CreateVector<uint8_t>(*day_filter) : 0;
  auto weekday_filter__ = weekday_filter ? _fbb.CreateVector<uint8_t>(*weekday_filter) : 0;
  return MECData::CreateDF_SCSchedule(
      _fbb,
      schedule_id,
      month_filter__,
      day_filter__,
      weekday_filter__,
      priority,
      day_plan_id);
}

struct DF_DayPlanAction FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_DayPlanActionBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_START_TIME = 4,
    VT_SCHEME_ID = 6,
    VT_MODE = 8
  };
  const MECData::DF_LocalTimePoint *start_time() const {
    return GetPointer<const MECData::DF_LocalTimePoint *>(VT_START_TIME);
  }
  int32_t scheme_id() const {
    return GetField<int32_t>(VT_SCHEME_ID, 0);
  }
  MECData::DE_SignalControlMode mode() const {
    return static_cast<MECData::DE_SignalControlMode>(GetField<uint8_t>(VT_MODE, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_START_TIME) &&
           verifier.VerifyTable(start_time()) &&
           VerifyField<int32_t>(verifier, VT_SCHEME_ID, 4) &&
           VerifyField<uint8_t>(verifier, VT_MODE, 1) &&
           verifier.EndTable();
  }
};

struct DF_DayPlanActionBuilder {
  typedef DF_DayPlanAction Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_start_time(::flatbuffers::Offset<MECData::DF_LocalTimePoint> start_time) {
    fbb_.AddOffset(DF_DayPlanAction::VT_START_TIME, start_time);
  }
  void add_scheme_id(int32_t scheme_id) {
    fbb_.AddElement<int32_t>(DF_DayPlanAction::VT_SCHEME_ID, scheme_id, 0);
  }
  void add_mode(MECData::DE_SignalControlMode mode) {
    fbb_.AddElement<uint8_t>(DF_DayPlanAction::VT_MODE, static_cast<uint8_t>(mode), 0);
  }
  explicit DF_DayPlanActionBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_DayPlanAction> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_DayPlanAction>(end);
    fbb_.Required(o, DF_DayPlanAction::VT_START_TIME);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_DayPlanAction> CreateDF_DayPlanAction(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_LocalTimePoint> start_time = 0,
    int32_t scheme_id = 0,
    MECData::DE_SignalControlMode mode = MECData::DE_SignalControlMode_CYCLIC_FIXED) {
  DF_DayPlanActionBuilder builder_(_fbb);
  builder_.add_scheme_id(scheme_id);
  builder_.add_start_time(start_time);
  builder_.add_mode(mode);
  return builder_.Finish();
}

struct DF_DayPlan FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_DayPlanBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_DAYPLAN_ID = 4,
    VT_ACTIONS = 6
  };
  uint8_t dayplan_id() const {
    return GetField<uint8_t>(VT_DAYPLAN_ID, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_DayPlanAction>> *actions() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_DayPlanAction>> *>(VT_ACTIONS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_DAYPLAN_ID, 1) &&
           VerifyOffset(verifier, VT_ACTIONS) &&
           verifier.VerifyVector(actions()) &&
           verifier.VerifyVectorOfTables(actions()) &&
           verifier.EndTable();
  }
};

struct DF_DayPlanBuilder {
  typedef DF_DayPlan Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_dayplan_id(uint8_t dayplan_id) {
    fbb_.AddElement<uint8_t>(DF_DayPlan::VT_DAYPLAN_ID, dayplan_id, 0);
  }
  void add_actions(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_DayPlanAction>>> actions) {
    fbb_.AddOffset(DF_DayPlan::VT_ACTIONS, actions);
  }
  explicit DF_DayPlanBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_DayPlan> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_DayPlan>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_DayPlan> CreateDF_DayPlan(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t dayplan_id = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_DayPlanAction>>> actions = 0) {
  DF_DayPlanBuilder builder_(_fbb);
  builder_.add_actions(actions);
  builder_.add_dayplan_id(dayplan_id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_DayPlan> CreateDF_DayPlanDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t dayplan_id = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_DayPlanAction>> *actions = nullptr) {
  auto actions__ = actions ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_DayPlanAction>>(*actions) : 0;
  return MECData::CreateDF_DayPlan(
      _fbb,
      dayplan_id,
      actions__);
}

struct DF_SignalControllerConfig FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SignalControllerConfigBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PHASES = 4,
    VT_GREEN_CONFLICTS = 6,
    VT_GREEN_INTERVALS = 8,
    VT_SCHEDULES = 10,
    VT_DAY_PLANS = 12
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhaseCharacteristics>> *phases() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhaseCharacteristics>> *>(VT_PHASES);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_GreenConflicts>> *green_conflicts() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_GreenConflicts>> *>(VT_GREEN_CONFLICTS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_GreenInterval>> *green_intervals() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_GreenInterval>> *>(VT_GREEN_INTERVALS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SCSchedule>> *schedules() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SCSchedule>> *>(VT_SCHEDULES);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_DayPlan>> *day_plans() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_DayPlan>> *>(VT_DAY_PLANS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PHASES) &&
           verifier.VerifyVector(phases()) &&
           verifier.VerifyVectorOfTables(phases()) &&
           VerifyOffset(verifier, VT_GREEN_CONFLICTS) &&
           verifier.VerifyVector(green_conflicts()) &&
           verifier.VerifyVectorOfTables(green_conflicts()) &&
           VerifyOffset(verifier, VT_GREEN_INTERVALS) &&
           verifier.VerifyVector(green_intervals()) &&
           verifier.VerifyVectorOfTables(green_intervals()) &&
           VerifyOffset(verifier, VT_SCHEDULES) &&
           verifier.VerifyVector(schedules()) &&
           verifier.VerifyVectorOfTables(schedules()) &&
           VerifyOffset(verifier, VT_DAY_PLANS) &&
           verifier.VerifyVector(day_plans()) &&
           verifier.VerifyVectorOfTables(day_plans()) &&
           verifier.EndTable();
  }
};

struct DF_SignalControllerConfigBuilder {
  typedef DF_SignalControllerConfig Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_phases(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhaseCharacteristics>>> phases) {
    fbb_.AddOffset(DF_SignalControllerConfig::VT_PHASES, phases);
  }
  void add_green_conflicts(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_GreenConflicts>>> green_conflicts) {
    fbb_.AddOffset(DF_SignalControllerConfig::VT_GREEN_CONFLICTS, green_conflicts);
  }
  void add_green_intervals(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_GreenInterval>>> green_intervals) {
    fbb_.AddOffset(DF_SignalControllerConfig::VT_GREEN_INTERVALS, green_intervals);
  }
  void add_schedules(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SCSchedule>>> schedules) {
    fbb_.AddOffset(DF_SignalControllerConfig::VT_SCHEDULES, schedules);
  }
  void add_day_plans(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_DayPlan>>> day_plans) {
    fbb_.AddOffset(DF_SignalControllerConfig::VT_DAY_PLANS, day_plans);
  }
  explicit DF_SignalControllerConfigBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_SignalControllerConfig> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_SignalControllerConfig>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_SignalControllerConfig> CreateDF_SignalControllerConfig(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhaseCharacteristics>>> phases = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_GreenConflicts>>> green_conflicts = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_GreenInterval>>> green_intervals = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SCSchedule>>> schedules = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_DayPlan>>> day_plans = 0) {
  DF_SignalControllerConfigBuilder builder_(_fbb);
  builder_.add_day_plans(day_plans);
  builder_.add_schedules(schedules);
  builder_.add_green_intervals(green_intervals);
  builder_.add_green_conflicts(green_conflicts);
  builder_.add_phases(phases);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_SignalControllerConfig> CreateDF_SignalControllerConfigDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<MECData::DF_PhaseCharacteristics>> *phases = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_GreenConflicts>> *green_conflicts = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_GreenInterval>> *green_intervals = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_SCSchedule>> *schedules = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_DayPlan>> *day_plans = nullptr) {
  auto phases__ = phases ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_PhaseCharacteristics>>(*phases) : 0;
  auto green_conflicts__ = green_conflicts ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_GreenConflicts>>(*green_conflicts) : 0;
  auto green_intervals__ = green_intervals ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_GreenInterval>>(*green_intervals) : 0;
  auto schedules__ = schedules ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_SCSchedule>>(*schedules) : 0;
  auto day_plans__ = day_plans ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_DayPlan>>(*day_plans) : 0;
  return MECData::CreateDF_SignalControllerConfig(
      _fbb,
      phases__,
      green_conflicts__,
      green_intervals__,
      schedules__,
      day_plans__);
}

struct DF_SignalSchemeConstraint FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SignalSchemeConstraintBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SCHEME_ID = 4,
    VT_NODE_ID = 6,
    VT_MIN_CYCLE = 8,
    VT_MAX_CYCLE = 10,
    VT_OFFSET = 12,
    VT_PHASIC_CONSTRAINTS = 14
  };
  int32_t scheme_id() const {
    return GetField<int32_t>(VT_SCHEME_ID, 0);
  }
  const MECData::DF_NodeReferenceID *node_id() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_NODE_ID);
  }
  uint16_t min_cycle() const {
    return GetField<uint16_t>(VT_MIN_CYCLE, 0);
  }
  uint16_t max_cycle() const {
    return GetField<uint16_t>(VT_MAX_CYCLE, 0);
  }
  int16_t offset() const {
    return GetField<int16_t>(VT_OFFSET, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhasicConstraint>> *phasic_constraints() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhasicConstraint>> *>(VT_PHASIC_CONSTRAINTS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_SCHEME_ID, 4) &&
           VerifyOffset(verifier, VT_NODE_ID) &&
           verifier.VerifyTable(node_id()) &&
           VerifyField<uint16_t>(verifier, VT_MIN_CYCLE, 2) &&
           VerifyField<uint16_t>(verifier, VT_MAX_CYCLE, 2) &&
           VerifyField<int16_t>(verifier, VT_OFFSET, 2) &&
           VerifyOffset(verifier, VT_PHASIC_CONSTRAINTS) &&
           verifier.VerifyVector(phasic_constraints()) &&
           verifier.VerifyVectorOfTables(phasic_constraints()) &&
           verifier.EndTable();
  }
};

struct DF_SignalSchemeConstraintBuilder {
  typedef DF_SignalSchemeConstraint Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_scheme_id(int32_t scheme_id) {
    fbb_.AddElement<int32_t>(DF_SignalSchemeConstraint::VT_SCHEME_ID, scheme_id, 0);
  }
  void add_node_id(::flatbuffers::Offset<MECData::DF_NodeReferenceID> node_id) {
    fbb_.AddOffset(DF_SignalSchemeConstraint::VT_NODE_ID, node_id);
  }
  void add_min_cycle(uint16_t min_cycle) {
    fbb_.AddElement<uint16_t>(DF_SignalSchemeConstraint::VT_MIN_CYCLE, min_cycle, 0);
  }
  void add_max_cycle(uint16_t max_cycle) {
    fbb_.AddElement<uint16_t>(DF_SignalSchemeConstraint::VT_MAX_CYCLE, max_cycle, 0);
  }
  void add_offset(int16_t offset) {
    fbb_.AddElement<int16_t>(DF_SignalSchemeConstraint::VT_OFFSET, offset, 0);
  }
  void add_phasic_constraints(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhasicConstraint>>> phasic_constraints) {
    fbb_.AddOffset(DF_SignalSchemeConstraint::VT_PHASIC_CONSTRAINTS, phasic_constraints);
  }
  explicit DF_SignalSchemeConstraintBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_SignalSchemeConstraint> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_SignalSchemeConstraint>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_SignalSchemeConstraint> CreateDF_SignalSchemeConstraint(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t scheme_id = 0,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> node_id = 0,
    uint16_t min_cycle = 0,
    uint16_t max_cycle = 0,
    int16_t offset = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhasicConstraint>>> phasic_constraints = 0) {
  DF_SignalSchemeConstraintBuilder builder_(_fbb);
  builder_.add_phasic_constraints(phasic_constraints);
  builder_.add_node_id(node_id);
  builder_.add_scheme_id(scheme_id);
  builder_.add_offset(offset);
  builder_.add_max_cycle(max_cycle);
  builder_.add_min_cycle(min_cycle);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_SignalSchemeConstraint> CreateDF_SignalSchemeConstraintDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t scheme_id = 0,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> node_id = 0,
    uint16_t min_cycle = 0,
    uint16_t max_cycle = 0,
    int16_t offset = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_PhasicConstraint>> *phasic_constraints = nullptr) {
  auto phasic_constraints__ = phasic_constraints ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_PhasicConstraint>>(*phasic_constraints) : 0;
  return MECData::CreateDF_SignalSchemeConstraint(
      _fbb,
      scheme_id,
      node_id,
      min_cycle,
      max_cycle,
      offset,
      phasic_constraints__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_SIGNALCONTROLLERCONFIG_MECDATA_H_
