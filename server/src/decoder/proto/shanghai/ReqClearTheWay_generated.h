// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_REQCLEARTHEWAY_MECDATA_H_
#define FLATBUFFERS_GENERATED_REQCLEARTHEWAY_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "DDateTime_generated.h"
#include "NodeReferenceID_generated.h"
#include "ReferencePath_generated.h"

namespace MECData {

struct DF_ReqClearTheWay;
struct DF_ReqClearTheWayBuilder;

struct DF_ReqClearTheWay FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ReqClearTheWayBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_UPSTREAMNODE = 4,
    VT_DOWNSTREAMNODE = 6,
    VT_TARGETLANE = 8,
    VT_RELATEDPATH = 10,
    VT_TBEGIN = 12,
    VT_TEND = 14,
    VT_TARGET_LANE_EXT_ID = 16
  };
  const MECData::DF_NodeReferenceID *upstreamNode() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_UPSTREAMNODE);
  }
  const MECData::DF_NodeReferenceID *downstreamNode() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_DOWNSTREAMNODE);
  }
  uint8_t targetLane() const {
    return GetField<uint8_t>(VT_TARGETLANE, 255);
  }
  const MECData::DF_ReferencePath *relatedPath() const {
    return GetPointer<const MECData::DF_ReferencePath *>(VT_RELATEDPATH);
  }
  const MECData::DF_DDateTime *tBegin() const {
    return GetPointer<const MECData::DF_DDateTime *>(VT_TBEGIN);
  }
  const MECData::DF_DDateTime *tEnd() const {
    return GetPointer<const MECData::DF_DDateTime *>(VT_TEND);
  }
  const ::flatbuffers::String *target_lane_ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_TARGET_LANE_EXT_ID);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_UPSTREAMNODE) &&
           verifier.VerifyTable(upstreamNode()) &&
           VerifyOffsetRequired(verifier, VT_DOWNSTREAMNODE) &&
           verifier.VerifyTable(downstreamNode()) &&
           VerifyField<uint8_t>(verifier, VT_TARGETLANE, 1) &&
           VerifyOffset(verifier, VT_RELATEDPATH) &&
           verifier.VerifyTable(relatedPath()) &&
           VerifyOffset(verifier, VT_TBEGIN) &&
           verifier.VerifyTable(tBegin()) &&
           VerifyOffset(verifier, VT_TEND) &&
           verifier.VerifyTable(tEnd()) &&
           VerifyOffset(verifier, VT_TARGET_LANE_EXT_ID) &&
           verifier.VerifyString(target_lane_ext_id()) &&
           verifier.EndTable();
  }
};

struct DF_ReqClearTheWayBuilder {
  typedef DF_ReqClearTheWay Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_upstreamNode(::flatbuffers::Offset<MECData::DF_NodeReferenceID> upstreamNode) {
    fbb_.AddOffset(DF_ReqClearTheWay::VT_UPSTREAMNODE, upstreamNode);
  }
  void add_downstreamNode(::flatbuffers::Offset<MECData::DF_NodeReferenceID> downstreamNode) {
    fbb_.AddOffset(DF_ReqClearTheWay::VT_DOWNSTREAMNODE, downstreamNode);
  }
  void add_targetLane(uint8_t targetLane) {
    fbb_.AddElement<uint8_t>(DF_ReqClearTheWay::VT_TARGETLANE, targetLane, 255);
  }
  void add_relatedPath(::flatbuffers::Offset<MECData::DF_ReferencePath> relatedPath) {
    fbb_.AddOffset(DF_ReqClearTheWay::VT_RELATEDPATH, relatedPath);
  }
  void add_tBegin(::flatbuffers::Offset<MECData::DF_DDateTime> tBegin) {
    fbb_.AddOffset(DF_ReqClearTheWay::VT_TBEGIN, tBegin);
  }
  void add_tEnd(::flatbuffers::Offset<MECData::DF_DDateTime> tEnd) {
    fbb_.AddOffset(DF_ReqClearTheWay::VT_TEND, tEnd);
  }
  void add_target_lane_ext_id(::flatbuffers::Offset<::flatbuffers::String> target_lane_ext_id) {
    fbb_.AddOffset(DF_ReqClearTheWay::VT_TARGET_LANE_EXT_ID, target_lane_ext_id);
  }
  explicit DF_ReqClearTheWayBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ReqClearTheWay> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ReqClearTheWay>(end);
    fbb_.Required(o, DF_ReqClearTheWay::VT_UPSTREAMNODE);
    fbb_.Required(o, DF_ReqClearTheWay::VT_DOWNSTREAMNODE);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ReqClearTheWay> CreateDF_ReqClearTheWay(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> upstreamNode = 0,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> downstreamNode = 0,
    uint8_t targetLane = 255,
    ::flatbuffers::Offset<MECData::DF_ReferencePath> relatedPath = 0,
    ::flatbuffers::Offset<MECData::DF_DDateTime> tBegin = 0,
    ::flatbuffers::Offset<MECData::DF_DDateTime> tEnd = 0,
    ::flatbuffers::Offset<::flatbuffers::String> target_lane_ext_id = 0) {
  DF_ReqClearTheWayBuilder builder_(_fbb);
  builder_.add_target_lane_ext_id(target_lane_ext_id);
  builder_.add_tEnd(tEnd);
  builder_.add_tBegin(tBegin);
  builder_.add_relatedPath(relatedPath);
  builder_.add_downstreamNode(downstreamNode);
  builder_.add_upstreamNode(upstreamNode);
  builder_.add_targetLane(targetLane);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ReqClearTheWay> CreateDF_ReqClearTheWayDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> upstreamNode = 0,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> downstreamNode = 0,
    uint8_t targetLane = 255,
    ::flatbuffers::Offset<MECData::DF_ReferencePath> relatedPath = 0,
    ::flatbuffers::Offset<MECData::DF_DDateTime> tBegin = 0,
    ::flatbuffers::Offset<MECData::DF_DDateTime> tEnd = 0,
    const char *target_lane_ext_id = nullptr) {
  auto target_lane_ext_id__ = target_lane_ext_id ? _fbb.CreateString(target_lane_ext_id) : 0;
  return MECData::CreateDF_ReqClearTheWay(
      _fbb,
      upstreamNode,
      downstreamNode,
      targetLane,
      relatedPath,
      tBegin,
      tEnd,
      target_lane_ext_id__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_REQCLEARTHEWAY_MECDATA_H_
