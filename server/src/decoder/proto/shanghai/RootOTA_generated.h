// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_ROOTOTA_MECDATA_H_
#define FLATBUFFERS_GENERATED_ROOTOTA_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct MSG_RootOTA;
struct MSG_RootOTABuilder;

struct MSG_RootOTA FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_RootOTABuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VERSION_MAJOR = 4,
    VT_VERSION_MINOR = 6,
    VT_VERSION_PATCH = 8,
    VT_VERSION_PRE_RELEASE = 10,
    VT_BUILD_DATE = 12,
    VT_SHA_SHORT_8 = 14,
    VT_CHECKSUM_SHA256 = 16,
    VT_DOWNLOAD = 18,
    VT_MSG_ID = 20
  };
  uint16_t version_major() const {
    return GetField<uint16_t>(VT_VERSION_MAJOR, 0);
  }
  uint16_t version_minor() const {
    return GetField<uint16_t>(VT_VERSION_MINOR, 0);
  }
  uint16_t version_patch() const {
    return GetField<uint16_t>(VT_VERSION_PATCH, 0);
  }
  const ::flatbuffers::String *version_pre_release() const {
    return GetPointer<const ::flatbuffers::String *>(VT_VERSION_PRE_RELEASE);
  }
  uint32_t build_date() const {
    return GetField<uint32_t>(VT_BUILD_DATE, 0);
  }
  uint32_t sha_short_8() const {
    return GetField<uint32_t>(VT_SHA_SHORT_8, 0);
  }
  const ::flatbuffers::String *checksum_sha256() const {
    return GetPointer<const ::flatbuffers::String *>(VT_CHECKSUM_SHA256);
  }
  bool download() const {
    return GetField<uint8_t>(VT_DOWNLOAD, 1) != 0;
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_VERSION_MAJOR, 2) &&
           VerifyField<uint16_t>(verifier, VT_VERSION_MINOR, 2) &&
           VerifyField<uint16_t>(verifier, VT_VERSION_PATCH, 2) &&
           VerifyOffset(verifier, VT_VERSION_PRE_RELEASE) &&
           verifier.VerifyString(version_pre_release()) &&
           VerifyField<uint32_t>(verifier, VT_BUILD_DATE, 4) &&
           VerifyField<uint32_t>(verifier, VT_SHA_SHORT_8, 4) &&
           VerifyOffset(verifier, VT_CHECKSUM_SHA256) &&
           verifier.VerifyString(checksum_sha256()) &&
           VerifyField<uint8_t>(verifier, VT_DOWNLOAD, 1) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct MSG_RootOTABuilder {
  typedef MSG_RootOTA Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_version_major(uint16_t version_major) {
    fbb_.AddElement<uint16_t>(MSG_RootOTA::VT_VERSION_MAJOR, version_major, 0);
  }
  void add_version_minor(uint16_t version_minor) {
    fbb_.AddElement<uint16_t>(MSG_RootOTA::VT_VERSION_MINOR, version_minor, 0);
  }
  void add_version_patch(uint16_t version_patch) {
    fbb_.AddElement<uint16_t>(MSG_RootOTA::VT_VERSION_PATCH, version_patch, 0);
  }
  void add_version_pre_release(::flatbuffers::Offset<::flatbuffers::String> version_pre_release) {
    fbb_.AddOffset(MSG_RootOTA::VT_VERSION_PRE_RELEASE, version_pre_release);
  }
  void add_build_date(uint32_t build_date) {
    fbb_.AddElement<uint32_t>(MSG_RootOTA::VT_BUILD_DATE, build_date, 0);
  }
  void add_sha_short_8(uint32_t sha_short_8) {
    fbb_.AddElement<uint32_t>(MSG_RootOTA::VT_SHA_SHORT_8, sha_short_8, 0);
  }
  void add_checksum_sha256(::flatbuffers::Offset<::flatbuffers::String> checksum_sha256) {
    fbb_.AddOffset(MSG_RootOTA::VT_CHECKSUM_SHA256, checksum_sha256);
  }
  void add_download(bool download) {
    fbb_.AddElement<uint8_t>(MSG_RootOTA::VT_DOWNLOAD, static_cast<uint8_t>(download), 1);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_RootOTA::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_RootOTABuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_RootOTA> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_RootOTA>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_RootOTA> CreateMSG_RootOTA(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t version_major = 0,
    uint16_t version_minor = 0,
    uint16_t version_patch = 0,
    ::flatbuffers::Offset<::flatbuffers::String> version_pre_release = 0,
    uint32_t build_date = 0,
    uint32_t sha_short_8 = 0,
    ::flatbuffers::Offset<::flatbuffers::String> checksum_sha256 = 0,
    bool download = true,
    int64_t msg_id = 0) {
  MSG_RootOTABuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_checksum_sha256(checksum_sha256);
  builder_.add_sha_short_8(sha_short_8);
  builder_.add_build_date(build_date);
  builder_.add_version_pre_release(version_pre_release);
  builder_.add_version_patch(version_patch);
  builder_.add_version_minor(version_minor);
  builder_.add_version_major(version_major);
  builder_.add_download(download);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_RootOTA> CreateMSG_RootOTADirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t version_major = 0,
    uint16_t version_minor = 0,
    uint16_t version_patch = 0,
    const char *version_pre_release = nullptr,
    uint32_t build_date = 0,
    uint32_t sha_short_8 = 0,
    const char *checksum_sha256 = nullptr,
    bool download = true,
    int64_t msg_id = 0) {
  auto version_pre_release__ = version_pre_release ? _fbb.CreateString(version_pre_release) : 0;
  auto checksum_sha256__ = checksum_sha256 ? _fbb.CreateString(checksum_sha256) : 0;
  return MECData::CreateMSG_RootOTA(
      _fbb,
      version_major,
      version_minor,
      version_patch,
      version_pre_release__,
      build_date,
      sha_short_8,
      checksum_sha256__,
      download,
      msg_id);
}

inline const MECData::MSG_RootOTA *GetMSG_RootOTA(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_RootOTA>(buf);
}

inline const MECData::MSG_RootOTA *GetSizePrefixedMSG_RootOTA(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_RootOTA>(buf);
}

inline bool VerifyMSG_RootOTABuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_RootOTA>(nullptr);
}

inline bool VerifySizePrefixedMSG_RootOTABuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_RootOTA>(nullptr);
}

inline void FinishMSG_RootOTABuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_RootOTA> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_RootOTABuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_RootOTA> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_ROOTOTA_MECDATA_H_
