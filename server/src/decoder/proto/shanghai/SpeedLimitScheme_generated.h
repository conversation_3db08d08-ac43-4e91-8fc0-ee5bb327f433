// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_SPEEDLIMITSCHEME_MECDATA_H_
#define FLATBUFFERS_GENERATED_SPEEDLIMITSCHEME_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "BasicVehicleClass_generated.h"
#include "NodeReferenceID_generated.h"
#include "Position3D_generated.h"

namespace MECData {

struct DF_SpeedLimitScheme;
struct DF_SpeedLimitSchemeBuilder;

struct DF_SpeedLimitScheme FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SpeedLimitSchemeBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SCHEME_ID = 4,
    VT_INTERSECTION = 6,
    VT_CLASSES = 8,
    VT_SECTION = 10,
    VT_LANE_REF_ID = 12,
    VT_VMS_EXT_ID = 14,
    VT_LANE_EXT_ID = 16,
    VT_REF_POSITION = 18,
    VT_RADIUS = 20,
    VT_SPEED_LIMIT = 22,
    VT_EXPIRES_IN = 24
  };
  int32_t scheme_id() const {
    return GetField<int32_t>(VT_SCHEME_ID, 2147483647);
  }
  const MECData::DF_NodeReferenceID *intersection() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_INTERSECTION);
  }
  const ::flatbuffers::Vector<uint8_t> *classes() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_CLASSES);
  }
  uint8_t section() const {
    return GetField<uint8_t>(VT_SECTION, 0);
  }
  int8_t lane_ref_id() const {
    return GetField<int8_t>(VT_LANE_REF_ID, 0);
  }
  const ::flatbuffers::String *vms_ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_VMS_EXT_ID);
  }
  const ::flatbuffers::String *lane_ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_LANE_EXT_ID);
  }
  const MECData::DF_Position3D *ref_position() const {
    return GetPointer<const MECData::DF_Position3D *>(VT_REF_POSITION);
  }
  uint16_t radius() const {
    return GetField<uint16_t>(VT_RADIUS, 0);
  }
  uint16_t speed_limit() const {
    return GetField<uint16_t>(VT_SPEED_LIMIT, 32767);
  }
  uint16_t expires_in() const {
    return GetField<uint16_t>(VT_EXPIRES_IN, 65535);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_SCHEME_ID, 4) &&
           VerifyOffset(verifier, VT_INTERSECTION) &&
           verifier.VerifyTable(intersection()) &&
           VerifyOffset(verifier, VT_CLASSES) &&
           verifier.VerifyVector(classes()) &&
           VerifyField<uint8_t>(verifier, VT_SECTION, 1) &&
           VerifyField<int8_t>(verifier, VT_LANE_REF_ID, 1) &&
           VerifyOffset(verifier, VT_VMS_EXT_ID) &&
           verifier.VerifyString(vms_ext_id()) &&
           VerifyOffset(verifier, VT_LANE_EXT_ID) &&
           verifier.VerifyString(lane_ext_id()) &&
           VerifyOffset(verifier, VT_REF_POSITION) &&
           verifier.VerifyTable(ref_position()) &&
           VerifyField<uint16_t>(verifier, VT_RADIUS, 2) &&
           VerifyField<uint16_t>(verifier, VT_SPEED_LIMIT, 2) &&
           VerifyField<uint16_t>(verifier, VT_EXPIRES_IN, 2) &&
           verifier.EndTable();
  }
};

struct DF_SpeedLimitSchemeBuilder {
  typedef DF_SpeedLimitScheme Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_scheme_id(int32_t scheme_id) {
    fbb_.AddElement<int32_t>(DF_SpeedLimitScheme::VT_SCHEME_ID, scheme_id, 2147483647);
  }
  void add_intersection(::flatbuffers::Offset<MECData::DF_NodeReferenceID> intersection) {
    fbb_.AddOffset(DF_SpeedLimitScheme::VT_INTERSECTION, intersection);
  }
  void add_classes(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> classes) {
    fbb_.AddOffset(DF_SpeedLimitScheme::VT_CLASSES, classes);
  }
  void add_section(uint8_t section) {
    fbb_.AddElement<uint8_t>(DF_SpeedLimitScheme::VT_SECTION, section, 0);
  }
  void add_lane_ref_id(int8_t lane_ref_id) {
    fbb_.AddElement<int8_t>(DF_SpeedLimitScheme::VT_LANE_REF_ID, lane_ref_id, 0);
  }
  void add_vms_ext_id(::flatbuffers::Offset<::flatbuffers::String> vms_ext_id) {
    fbb_.AddOffset(DF_SpeedLimitScheme::VT_VMS_EXT_ID, vms_ext_id);
  }
  void add_lane_ext_id(::flatbuffers::Offset<::flatbuffers::String> lane_ext_id) {
    fbb_.AddOffset(DF_SpeedLimitScheme::VT_LANE_EXT_ID, lane_ext_id);
  }
  void add_ref_position(::flatbuffers::Offset<MECData::DF_Position3D> ref_position) {
    fbb_.AddOffset(DF_SpeedLimitScheme::VT_REF_POSITION, ref_position);
  }
  void add_radius(uint16_t radius) {
    fbb_.AddElement<uint16_t>(DF_SpeedLimitScheme::VT_RADIUS, radius, 0);
  }
  void add_speed_limit(uint16_t speed_limit) {
    fbb_.AddElement<uint16_t>(DF_SpeedLimitScheme::VT_SPEED_LIMIT, speed_limit, 32767);
  }
  void add_expires_in(uint16_t expires_in) {
    fbb_.AddElement<uint16_t>(DF_SpeedLimitScheme::VT_EXPIRES_IN, expires_in, 65535);
  }
  explicit DF_SpeedLimitSchemeBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_SpeedLimitScheme> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_SpeedLimitScheme>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_SpeedLimitScheme> CreateDF_SpeedLimitScheme(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t scheme_id = 2147483647,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> intersection = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> classes = 0,
    uint8_t section = 0,
    int8_t lane_ref_id = 0,
    ::flatbuffers::Offset<::flatbuffers::String> vms_ext_id = 0,
    ::flatbuffers::Offset<::flatbuffers::String> lane_ext_id = 0,
    ::flatbuffers::Offset<MECData::DF_Position3D> ref_position = 0,
    uint16_t radius = 0,
    uint16_t speed_limit = 32767,
    uint16_t expires_in = 65535) {
  DF_SpeedLimitSchemeBuilder builder_(_fbb);
  builder_.add_ref_position(ref_position);
  builder_.add_lane_ext_id(lane_ext_id);
  builder_.add_vms_ext_id(vms_ext_id);
  builder_.add_classes(classes);
  builder_.add_intersection(intersection);
  builder_.add_scheme_id(scheme_id);
  builder_.add_expires_in(expires_in);
  builder_.add_speed_limit(speed_limit);
  builder_.add_radius(radius);
  builder_.add_lane_ref_id(lane_ref_id);
  builder_.add_section(section);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_SpeedLimitScheme> CreateDF_SpeedLimitSchemeDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t scheme_id = 2147483647,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> intersection = 0,
    const std::vector<uint8_t> *classes = nullptr,
    uint8_t section = 0,
    int8_t lane_ref_id = 0,
    const char *vms_ext_id = nullptr,
    const char *lane_ext_id = nullptr,
    ::flatbuffers::Offset<MECData::DF_Position3D> ref_position = 0,
    uint16_t radius = 0,
    uint16_t speed_limit = 32767,
    uint16_t expires_in = 65535) {
  auto classes__ = classes ? _fbb.CreateVector<uint8_t>(*classes) : 0;
  auto vms_ext_id__ = vms_ext_id ? _fbb.CreateString(vms_ext_id) : 0;
  auto lane_ext_id__ = lane_ext_id ? _fbb.CreateString(lane_ext_id) : 0;
  return MECData::CreateDF_SpeedLimitScheme(
      _fbb,
      scheme_id,
      intersection,
      classes__,
      section,
      lane_ref_id,
      vms_ext_id__,
      lane_ext_id__,
      ref_position,
      radius,
      speed_limit,
      expires_in);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_SPEEDLIMITSCHEME_MECDATA_H_
