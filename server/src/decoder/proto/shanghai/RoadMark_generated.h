// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_ROADMARK_MECDATA_H_
#define FLATBUFFERS_GENERATED_ROADMARK_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_RoadMarkLine;
struct DF_RoadMarkLineBuilder;

struct DF_RoadMark;
struct DF_RoadMarkBuilder;

enum DE_RoadMarkColor : uint8_t {
  DE_RoadMarkColor_WHITE = 0,
  DE_RoadMarkColor_BLUE = 1,
  DE_RoadMarkColor_YELLOW = 2,
  DE_RoadMarkColor_GREEN = 3,
  DE_RoadMarkColor_ORANGE = 4,
  DE_RoadMarkColor_RED = 5,
  DE_RoadMarkColor_MIN = DE_RoadMarkColor_WHITE,
  DE_RoadMarkColor_MAX = DE_RoadMarkColor_RED
};

inline const DE_RoadMarkColor (&EnumValuesDE_RoadMarkColor())[6] {
  static const DE_RoadMarkColor values[] = {
    DE_RoadMarkColor_WHITE,
    DE_RoadMarkColor_BLUE,
    DE_RoadMarkColor_YELLOW,
    DE_RoadMarkColor_GREEN,
    DE_RoadMarkColor_ORANGE,
    DE_RoadMarkColor_RED
  };
  return values;
}

inline const char * const *EnumNamesDE_RoadMarkColor() {
  static const char * const names[7] = {
    "WHITE",
    "BLUE",
    "YELLOW",
    "GREEN",
    "ORANGE",
    "RED",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_RoadMarkColor(DE_RoadMarkColor e) {
  if (::flatbuffers::IsOutRange(e, DE_RoadMarkColor_WHITE, DE_RoadMarkColor_RED)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_RoadMarkColor()[index];
}

enum DE_RoadMarkType : uint8_t {
  DE_RoadMarkType_NONE = 0,
  DE_RoadMarkType_SOLID = 1,
  DE_RoadMarkType_BROKEN = 2,
  DE_RoadMarkType_SOLID_SOLID = 3,
  DE_RoadMarkType_SOLID_BROKEN = 4,
  DE_RoadMarkType_BROKEN_SOLID = 5,
  DE_RoadMarkType_BROKEN_BROKEN = 6,
  DE_RoadMarkType_BOTTS_DOTS = 7,
  DE_RoadMarkType_GRASS = 8,
  DE_RoadMarkType_CURB = 9,
  DE_RoadMarkType_EDGE = 10,
  DE_RoadMarkType_MIN = DE_RoadMarkType_NONE,
  DE_RoadMarkType_MAX = DE_RoadMarkType_EDGE
};

inline const DE_RoadMarkType (&EnumValuesDE_RoadMarkType())[11] {
  static const DE_RoadMarkType values[] = {
    DE_RoadMarkType_NONE,
    DE_RoadMarkType_SOLID,
    DE_RoadMarkType_BROKEN,
    DE_RoadMarkType_SOLID_SOLID,
    DE_RoadMarkType_SOLID_BROKEN,
    DE_RoadMarkType_BROKEN_SOLID,
    DE_RoadMarkType_BROKEN_BROKEN,
    DE_RoadMarkType_BOTTS_DOTS,
    DE_RoadMarkType_GRASS,
    DE_RoadMarkType_CURB,
    DE_RoadMarkType_EDGE
  };
  return values;
}

inline const char * const *EnumNamesDE_RoadMarkType() {
  static const char * const names[12] = {
    "NONE",
    "SOLID",
    "BROKEN",
    "SOLID_SOLID",
    "SOLID_BROKEN",
    "BROKEN_SOLID",
    "BROKEN_BROKEN",
    "BOTTS_DOTS",
    "GRASS",
    "CURB",
    "EDGE",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_RoadMarkType(DE_RoadMarkType e) {
  if (::flatbuffers::IsOutRange(e, DE_RoadMarkType_NONE, DE_RoadMarkType_EDGE)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_RoadMarkType()[index];
}

enum DE_RoadMarkCrossRule : uint8_t {
  DE_RoadMarkCrossRule_NO_PASSING = 0,
  DE_RoadMarkCrossRule_CAUTION = 1,
  DE_RoadMarkCrossRule_NONE = 2,
  DE_RoadMarkCrossRule_MIN = DE_RoadMarkCrossRule_NO_PASSING,
  DE_RoadMarkCrossRule_MAX = DE_RoadMarkCrossRule_NONE
};

inline const DE_RoadMarkCrossRule (&EnumValuesDE_RoadMarkCrossRule())[3] {
  static const DE_RoadMarkCrossRule values[] = {
    DE_RoadMarkCrossRule_NO_PASSING,
    DE_RoadMarkCrossRule_CAUTION,
    DE_RoadMarkCrossRule_NONE
  };
  return values;
}

inline const char * const *EnumNamesDE_RoadMarkCrossRule() {
  static const char * const names[4] = {
    "NO_PASSING",
    "CAUTION",
    "NONE",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_RoadMarkCrossRule(DE_RoadMarkCrossRule e) {
  if (::flatbuffers::IsOutRange(e, DE_RoadMarkCrossRule_NO_PASSING, DE_RoadMarkCrossRule_NONE)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_RoadMarkCrossRule()[index];
}

struct DF_RoadMarkLine FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_RoadMarkLineBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_LENGTH = 4,
    VT_SPACE = 6,
    VT_T_OFFSET = 8,
    VT_S_OFFSET = 10,
    VT_WIDTH = 12,
    VT_COLOR = 14
  };
  uint32_t length() const {
    return GetField<uint32_t>(VT_LENGTH, 0);
  }
  uint32_t space() const {
    return GetField<uint32_t>(VT_SPACE, 0);
  }
  int32_t t_offset() const {
    return GetField<int32_t>(VT_T_OFFSET, 0);
  }
  uint32_t s_offset() const {
    return GetField<uint32_t>(VT_S_OFFSET, 0);
  }
  uint32_t width() const {
    return GetField<uint32_t>(VT_WIDTH, 0);
  }
  MECData::DE_RoadMarkColor color() const {
    return static_cast<MECData::DE_RoadMarkColor>(GetField<uint8_t>(VT_COLOR, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_LENGTH, 4) &&
           VerifyField<uint32_t>(verifier, VT_SPACE, 4) &&
           VerifyField<int32_t>(verifier, VT_T_OFFSET, 4) &&
           VerifyField<uint32_t>(verifier, VT_S_OFFSET, 4) &&
           VerifyField<uint32_t>(verifier, VT_WIDTH, 4) &&
           VerifyField<uint8_t>(verifier, VT_COLOR, 1) &&
           verifier.EndTable();
  }
};

struct DF_RoadMarkLineBuilder {
  typedef DF_RoadMarkLine Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_length(uint32_t length) {
    fbb_.AddElement<uint32_t>(DF_RoadMarkLine::VT_LENGTH, length, 0);
  }
  void add_space(uint32_t space) {
    fbb_.AddElement<uint32_t>(DF_RoadMarkLine::VT_SPACE, space, 0);
  }
  void add_t_offset(int32_t t_offset) {
    fbb_.AddElement<int32_t>(DF_RoadMarkLine::VT_T_OFFSET, t_offset, 0);
  }
  void add_s_offset(uint32_t s_offset) {
    fbb_.AddElement<uint32_t>(DF_RoadMarkLine::VT_S_OFFSET, s_offset, 0);
  }
  void add_width(uint32_t width) {
    fbb_.AddElement<uint32_t>(DF_RoadMarkLine::VT_WIDTH, width, 0);
  }
  void add_color(MECData::DE_RoadMarkColor color) {
    fbb_.AddElement<uint8_t>(DF_RoadMarkLine::VT_COLOR, static_cast<uint8_t>(color), 0);
  }
  explicit DF_RoadMarkLineBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_RoadMarkLine> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_RoadMarkLine>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_RoadMarkLine> CreateDF_RoadMarkLine(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t length = 0,
    uint32_t space = 0,
    int32_t t_offset = 0,
    uint32_t s_offset = 0,
    uint32_t width = 0,
    MECData::DE_RoadMarkColor color = MECData::DE_RoadMarkColor_WHITE) {
  DF_RoadMarkLineBuilder builder_(_fbb);
  builder_.add_width(width);
  builder_.add_s_offset(s_offset);
  builder_.add_t_offset(t_offset);
  builder_.add_space(space);
  builder_.add_length(length);
  builder_.add_color(color);
  return builder_.Finish();
}

struct DF_RoadMark FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_RoadMarkBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_S_OFFSET = 4,
    VT_MARK_TYPE = 6,
    VT_COLOR = 8,
    VT_LANE_CHANGE_TO_LEFT = 10,
    VT_LANE_CHANGE_TO_RIGHT = 12,
    VT_WIDTH = 14,
    VT_HEIGHT = 16,
    VT_LINES = 18
  };
  uint32_t s_offset() const {
    return GetField<uint32_t>(VT_S_OFFSET, 0);
  }
  MECData::DE_RoadMarkType mark_type() const {
    return static_cast<MECData::DE_RoadMarkType>(GetField<uint8_t>(VT_MARK_TYPE, 0));
  }
  MECData::DE_RoadMarkColor color() const {
    return static_cast<MECData::DE_RoadMarkColor>(GetField<uint8_t>(VT_COLOR, 0));
  }
  bool lane_change_to_left() const {
    return GetField<uint8_t>(VT_LANE_CHANGE_TO_LEFT, 0) != 0;
  }
  bool lane_change_to_right() const {
    return GetField<uint8_t>(VT_LANE_CHANGE_TO_RIGHT, 0) != 0;
  }
  uint8_t width() const {
    return GetField<uint8_t>(VT_WIDTH, 0);
  }
  uint8_t height() const {
    return GetField<uint8_t>(VT_HEIGHT, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadMarkLine>> *lines() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadMarkLine>> *>(VT_LINES);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_S_OFFSET, 4) &&
           VerifyField<uint8_t>(verifier, VT_MARK_TYPE, 1) &&
           VerifyField<uint8_t>(verifier, VT_COLOR, 1) &&
           VerifyField<uint8_t>(verifier, VT_LANE_CHANGE_TO_LEFT, 1) &&
           VerifyField<uint8_t>(verifier, VT_LANE_CHANGE_TO_RIGHT, 1) &&
           VerifyField<uint8_t>(verifier, VT_WIDTH, 1) &&
           VerifyField<uint8_t>(verifier, VT_HEIGHT, 1) &&
           VerifyOffset(verifier, VT_LINES) &&
           verifier.VerifyVector(lines()) &&
           verifier.VerifyVectorOfTables(lines()) &&
           verifier.EndTable();
  }
};

struct DF_RoadMarkBuilder {
  typedef DF_RoadMark Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_s_offset(uint32_t s_offset) {
    fbb_.AddElement<uint32_t>(DF_RoadMark::VT_S_OFFSET, s_offset, 0);
  }
  void add_mark_type(MECData::DE_RoadMarkType mark_type) {
    fbb_.AddElement<uint8_t>(DF_RoadMark::VT_MARK_TYPE, static_cast<uint8_t>(mark_type), 0);
  }
  void add_color(MECData::DE_RoadMarkColor color) {
    fbb_.AddElement<uint8_t>(DF_RoadMark::VT_COLOR, static_cast<uint8_t>(color), 0);
  }
  void add_lane_change_to_left(bool lane_change_to_left) {
    fbb_.AddElement<uint8_t>(DF_RoadMark::VT_LANE_CHANGE_TO_LEFT, static_cast<uint8_t>(lane_change_to_left), 0);
  }
  void add_lane_change_to_right(bool lane_change_to_right) {
    fbb_.AddElement<uint8_t>(DF_RoadMark::VT_LANE_CHANGE_TO_RIGHT, static_cast<uint8_t>(lane_change_to_right), 0);
  }
  void add_width(uint8_t width) {
    fbb_.AddElement<uint8_t>(DF_RoadMark::VT_WIDTH, width, 0);
  }
  void add_height(uint8_t height) {
    fbb_.AddElement<uint8_t>(DF_RoadMark::VT_HEIGHT, height, 0);
  }
  void add_lines(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadMarkLine>>> lines) {
    fbb_.AddOffset(DF_RoadMark::VT_LINES, lines);
  }
  explicit DF_RoadMarkBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_RoadMark> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_RoadMark>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_RoadMark> CreateDF_RoadMark(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t s_offset = 0,
    MECData::DE_RoadMarkType mark_type = MECData::DE_RoadMarkType_NONE,
    MECData::DE_RoadMarkColor color = MECData::DE_RoadMarkColor_WHITE,
    bool lane_change_to_left = false,
    bool lane_change_to_right = false,
    uint8_t width = 0,
    uint8_t height = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadMarkLine>>> lines = 0) {
  DF_RoadMarkBuilder builder_(_fbb);
  builder_.add_lines(lines);
  builder_.add_s_offset(s_offset);
  builder_.add_height(height);
  builder_.add_width(width);
  builder_.add_lane_change_to_right(lane_change_to_right);
  builder_.add_lane_change_to_left(lane_change_to_left);
  builder_.add_color(color);
  builder_.add_mark_type(mark_type);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_RoadMark> CreateDF_RoadMarkDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t s_offset = 0,
    MECData::DE_RoadMarkType mark_type = MECData::DE_RoadMarkType_NONE,
    MECData::DE_RoadMarkColor color = MECData::DE_RoadMarkColor_WHITE,
    bool lane_change_to_left = false,
    bool lane_change_to_right = false,
    uint8_t width = 0,
    uint8_t height = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_RoadMarkLine>> *lines = nullptr) {
  auto lines__ = lines ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_RoadMarkLine>>(*lines) : 0;
  return MECData::CreateDF_RoadMark(
      _fbb,
      s_offset,
      mark_type,
      color,
      lane_change_to_left,
      lane_change_to_right,
      width,
      height,
      lines__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_ROADMARK_MECDATA_H_
