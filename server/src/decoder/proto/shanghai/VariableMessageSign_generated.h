// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_VARIABLEMESSAGESIGN_MECDATA_H_
#define FLATBUFFERS_GENERATED_VARIABLEMESSAGESIGN_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_VMSPredefined;
struct DF_VMSPredefinedBuilder;

struct DF_VMSText;
struct DF_VMSTextBuilder;

struct MSG_VariableMessageSign;
struct MSG_VariableMessageSignBuilder;

enum DF_VMSContent : uint8_t {
  DF_VMSContent_NONE = 0,
  DF_VMSContent_DF_VMSPredefined = 1,
  DF_VMSContent_DF_VMSText = 2,
  DF_VMSContent_MIN = DF_VMSContent_NONE,
  DF_VMSContent_MAX = DF_VMSContent_DF_VMSText
};

inline const DF_VMSContent (&EnumValuesDF_VMSContent())[3] {
  static const DF_VMSContent values[] = {
    DF_VMSContent_NONE,
    DF_VMSContent_DF_VMSPredefined,
    DF_VMSContent_DF_VMSText
  };
  return values;
}

inline const char * const *EnumNamesDF_VMSContent() {
  static const char * const names[4] = {
    "NONE",
    "DF_VMSPredefined",
    "DF_VMSText",
    nullptr
  };
  return names;
}

inline const char *EnumNameDF_VMSContent(DF_VMSContent e) {
  if (::flatbuffers::IsOutRange(e, DF_VMSContent_NONE, DF_VMSContent_DF_VMSText)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDF_VMSContent()[index];
}

template<typename T> struct DF_VMSContentTraits {
  static const DF_VMSContent enum_value = DF_VMSContent_NONE;
};

template<> struct DF_VMSContentTraits<MECData::DF_VMSPredefined> {
  static const DF_VMSContent enum_value = DF_VMSContent_DF_VMSPredefined;
};

template<> struct DF_VMSContentTraits<MECData::DF_VMSText> {
  static const DF_VMSContent enum_value = DF_VMSContent_DF_VMSText;
};

bool VerifyDF_VMSContent(::flatbuffers::Verifier &verifier, const void *obj, DF_VMSContent type);
bool VerifyDF_VMSContentVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types);

struct DF_VMSPredefined FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_VMSPredefinedBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PREDEFINED_ID = 4
  };
  uint8_t predefined_id() const {
    return GetField<uint8_t>(VT_PREDEFINED_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_PREDEFINED_ID, 1) &&
           verifier.EndTable();
  }
};

struct DF_VMSPredefinedBuilder {
  typedef DF_VMSPredefined Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_predefined_id(uint8_t predefined_id) {
    fbb_.AddElement<uint8_t>(DF_VMSPredefined::VT_PREDEFINED_ID, predefined_id, 0);
  }
  explicit DF_VMSPredefinedBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_VMSPredefined> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_VMSPredefined>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_VMSPredefined> CreateDF_VMSPredefined(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t predefined_id = 0) {
  DF_VMSPredefinedBuilder builder_(_fbb);
  builder_.add_predefined_id(predefined_id);
  return builder_.Finish();
}

struct DF_VMSText FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_VMSTextBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TEXT = 4
  };
  const ::flatbuffers::String *text() const {
    return GetPointer<const ::flatbuffers::String *>(VT_TEXT);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_TEXT) &&
           verifier.VerifyString(text()) &&
           verifier.EndTable();
  }
};

struct DF_VMSTextBuilder {
  typedef DF_VMSText Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_text(::flatbuffers::Offset<::flatbuffers::String> text) {
    fbb_.AddOffset(DF_VMSText::VT_TEXT, text);
  }
  explicit DF_VMSTextBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_VMSText> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_VMSText>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_VMSText> CreateDF_VMSText(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> text = 0) {
  DF_VMSTextBuilder builder_(_fbb);
  builder_.add_text(text);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_VMSText> CreateDF_VMSTextDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *text = nullptr) {
  auto text__ = text ? _fbb.CreateString(text) : 0;
  return MECData::CreateDF_VMSText(
      _fbb,
      text__);
}

struct MSG_VariableMessageSign FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_VariableMessageSignBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_DEV_ID = 4,
    VT_CONTENT_TYPE = 6,
    VT_CONTENT = 8,
    VT_EXT_ID = 10,
    VT_MSG_ID = 12
  };
  uint16_t dev_id() const {
    return GetField<uint16_t>(VT_DEV_ID, 0);
  }
  MECData::DF_VMSContent content_type() const {
    return static_cast<MECData::DF_VMSContent>(GetField<uint8_t>(VT_CONTENT_TYPE, 0));
  }
  const void *content() const {
    return GetPointer<const void *>(VT_CONTENT);
  }
  template<typename T> const T *content_as() const;
  const MECData::DF_VMSPredefined *content_as_DF_VMSPredefined() const {
    return content_type() == MECData::DF_VMSContent_DF_VMSPredefined ? static_cast<const MECData::DF_VMSPredefined *>(content()) : nullptr;
  }
  const MECData::DF_VMSText *content_as_DF_VMSText() const {
    return content_type() == MECData::DF_VMSContent_DF_VMSText ? static_cast<const MECData::DF_VMSText *>(content()) : nullptr;
  }
  const ::flatbuffers::String *ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_EXT_ID);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_DEV_ID, 2) &&
           VerifyField<uint8_t>(verifier, VT_CONTENT_TYPE, 1) &&
           VerifyOffsetRequired(verifier, VT_CONTENT) &&
           VerifyDF_VMSContent(verifier, content(), content_type()) &&
           VerifyOffset(verifier, VT_EXT_ID) &&
           verifier.VerifyString(ext_id()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

template<> inline const MECData::DF_VMSPredefined *MSG_VariableMessageSign::content_as<MECData::DF_VMSPredefined>() const {
  return content_as_DF_VMSPredefined();
}

template<> inline const MECData::DF_VMSText *MSG_VariableMessageSign::content_as<MECData::DF_VMSText>() const {
  return content_as_DF_VMSText();
}

struct MSG_VariableMessageSignBuilder {
  typedef MSG_VariableMessageSign Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_dev_id(uint16_t dev_id) {
    fbb_.AddElement<uint16_t>(MSG_VariableMessageSign::VT_DEV_ID, dev_id, 0);
  }
  void add_content_type(MECData::DF_VMSContent content_type) {
    fbb_.AddElement<uint8_t>(MSG_VariableMessageSign::VT_CONTENT_TYPE, static_cast<uint8_t>(content_type), 0);
  }
  void add_content(::flatbuffers::Offset<void> content) {
    fbb_.AddOffset(MSG_VariableMessageSign::VT_CONTENT, content);
  }
  void add_ext_id(::flatbuffers::Offset<::flatbuffers::String> ext_id) {
    fbb_.AddOffset(MSG_VariableMessageSign::VT_EXT_ID, ext_id);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_VariableMessageSign::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_VariableMessageSignBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_VariableMessageSign> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_VariableMessageSign>(end);
    fbb_.Required(o, MSG_VariableMessageSign::VT_CONTENT);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_VariableMessageSign> CreateMSG_VariableMessageSign(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t dev_id = 0,
    MECData::DF_VMSContent content_type = MECData::DF_VMSContent_NONE,
    ::flatbuffers::Offset<void> content = 0,
    ::flatbuffers::Offset<::flatbuffers::String> ext_id = 0,
    int64_t msg_id = 0) {
  MSG_VariableMessageSignBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_ext_id(ext_id);
  builder_.add_content(content);
  builder_.add_dev_id(dev_id);
  builder_.add_content_type(content_type);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_VariableMessageSign> CreateMSG_VariableMessageSignDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t dev_id = 0,
    MECData::DF_VMSContent content_type = MECData::DF_VMSContent_NONE,
    ::flatbuffers::Offset<void> content = 0,
    const char *ext_id = nullptr,
    int64_t msg_id = 0) {
  auto ext_id__ = ext_id ? _fbb.CreateString(ext_id) : 0;
  return MECData::CreateMSG_VariableMessageSign(
      _fbb,
      dev_id,
      content_type,
      content,
      ext_id__,
      msg_id);
}

inline bool VerifyDF_VMSContent(::flatbuffers::Verifier &verifier, const void *obj, DF_VMSContent type) {
  switch (type) {
    case DF_VMSContent_NONE: {
      return true;
    }
    case DF_VMSContent_DF_VMSPredefined: {
      auto ptr = reinterpret_cast<const MECData::DF_VMSPredefined *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_VMSContent_DF_VMSText: {
      auto ptr = reinterpret_cast<const MECData::DF_VMSText *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return true;
  }
}

inline bool VerifyDF_VMSContentVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (::flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyDF_VMSContent(
        verifier,  values->Get(i), types->GetEnum<DF_VMSContent>(i))) {
      return false;
    }
  }
  return true;
}

inline const MECData::MSG_VariableMessageSign *GetMSG_VariableMessageSign(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_VariableMessageSign>(buf);
}

inline const MECData::MSG_VariableMessageSign *GetSizePrefixedMSG_VariableMessageSign(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_VariableMessageSign>(buf);
}

inline bool VerifyMSG_VariableMessageSignBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_VariableMessageSign>(nullptr);
}

inline bool VerifySizePrefixedMSG_VariableMessageSignBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_VariableMessageSign>(nullptr);
}

inline void FinishMSG_VariableMessageSignBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_VariableMessageSign> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_VariableMessageSignBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_VariableMessageSign> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_VARIABLEMESSAGESIGN_MECDATA_H_
