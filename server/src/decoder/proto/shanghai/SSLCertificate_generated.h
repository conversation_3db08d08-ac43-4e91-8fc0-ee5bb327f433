// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_SSLCERTIFICATE_MECDATA_H_
#define FLATBUFFERS_GENERATED_SSLCERTIFICATE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DE_CertificateUsage;
struct DE_CertificateUsageBuilder;

struct DF_SSLCertificate;
struct DF_SSLCertificateBuilder;

enum DE_VerificationSetting : int8_t {
  DE_VerificationSetting_CLIENT_VERIFY_NONE = 0,
  DE_VerificationSetting_CLIENT_VERIFY_REQUIRED = 1,
  DE_VerificationSetting_CLIENT_VERIFY_OPTIONAL = 2,
  DE_VerificationSetting_MIN = DE_VerificationSetting_CLIENT_VERIFY_NONE,
  DE_VerificationSetting_MAX = DE_VerificationSetting_CLIENT_VERIFY_OPTIONAL
};

inline const DE_VerificationSetting (&EnumValuesDE_VerificationSetting())[3] {
  static const DE_VerificationSetting values[] = {
    DE_VerificationSetting_CLIENT_VERIFY_NONE,
    DE_VerificationSetting_CLIENT_VERIFY_REQUIRED,
    DE_VerificationSetting_CLIENT_VERIFY_OPTIONAL
  };
  return values;
}

inline const char * const *EnumNamesDE_VerificationSetting() {
  static const char * const names[4] = {
    "CLIENT_VERIFY_NONE",
    "CLIENT_VERIFY_REQUIRED",
    "CLIENT_VERIFY_OPTIONAL",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_VerificationSetting(DE_VerificationSetting e) {
  if (::flatbuffers::IsOutRange(e, DE_VerificationSetting_CLIENT_VERIFY_NONE, DE_VerificationSetting_CLIENT_VERIFY_OPTIONAL)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_VerificationSetting()[index];
}

struct DE_CertificateUsage FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_CertificateUsageBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FOR_CLIENT = 4,
    VT_FOR_SERVER = 6
  };
  bool for_client() const {
    return GetField<uint8_t>(VT_FOR_CLIENT, 0) != 0;
  }
  bool for_server() const {
    return GetField<uint8_t>(VT_FOR_SERVER, 0) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_FOR_CLIENT, 1) &&
           VerifyField<uint8_t>(verifier, VT_FOR_SERVER, 1) &&
           verifier.EndTable();
  }
};

struct DE_CertificateUsageBuilder {
  typedef DE_CertificateUsage Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_for_client(bool for_client) {
    fbb_.AddElement<uint8_t>(DE_CertificateUsage::VT_FOR_CLIENT, static_cast<uint8_t>(for_client), 0);
  }
  void add_for_server(bool for_server) {
    fbb_.AddElement<uint8_t>(DE_CertificateUsage::VT_FOR_SERVER, static_cast<uint8_t>(for_server), 0);
  }
  explicit DE_CertificateUsageBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_CertificateUsage> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_CertificateUsage>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_CertificateUsage> CreateDE_CertificateUsage(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    bool for_client = false,
    bool for_server = false) {
  DE_CertificateUsageBuilder builder_(_fbb);
  builder_.add_for_server(for_server);
  builder_.add_for_client(for_client);
  return builder_.Finish();
}

struct DF_SSLCertificate FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SSLCertificateBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NAME = 4,
    VT_USAGE = 6,
    VT_CERTIFICATE = 8,
    VT_PRIVATE_KEY = 10,
    VT_CA_CERTIFICATE = 12,
    VT_VERIFICATION_SETTING = 14
  };
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  const MECData::DE_CertificateUsage *usage() const {
    return GetPointer<const MECData::DE_CertificateUsage *>(VT_USAGE);
  }
  const ::flatbuffers::String *certificate() const {
    return GetPointer<const ::flatbuffers::String *>(VT_CERTIFICATE);
  }
  const ::flatbuffers::String *private_key() const {
    return GetPointer<const ::flatbuffers::String *>(VT_PRIVATE_KEY);
  }
  const ::flatbuffers::String *ca_certificate() const {
    return GetPointer<const ::flatbuffers::String *>(VT_CA_CERTIFICATE);
  }
  MECData::DE_VerificationSetting verification_setting() const {
    return static_cast<MECData::DE_VerificationSetting>(GetField<int8_t>(VT_VERIFICATION_SETTING, 1));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffsetRequired(verifier, VT_USAGE) &&
           verifier.VerifyTable(usage()) &&
           VerifyOffsetRequired(verifier, VT_CERTIFICATE) &&
           verifier.VerifyString(certificate()) &&
           VerifyOffsetRequired(verifier, VT_PRIVATE_KEY) &&
           verifier.VerifyString(private_key()) &&
           VerifyOffset(verifier, VT_CA_CERTIFICATE) &&
           verifier.VerifyString(ca_certificate()) &&
           VerifyField<int8_t>(verifier, VT_VERIFICATION_SETTING, 1) &&
           verifier.EndTable();
  }
};

struct DF_SSLCertificateBuilder {
  typedef DF_SSLCertificate Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(DF_SSLCertificate::VT_NAME, name);
  }
  void add_usage(::flatbuffers::Offset<MECData::DE_CertificateUsage> usage) {
    fbb_.AddOffset(DF_SSLCertificate::VT_USAGE, usage);
  }
  void add_certificate(::flatbuffers::Offset<::flatbuffers::String> certificate) {
    fbb_.AddOffset(DF_SSLCertificate::VT_CERTIFICATE, certificate);
  }
  void add_private_key(::flatbuffers::Offset<::flatbuffers::String> private_key) {
    fbb_.AddOffset(DF_SSLCertificate::VT_PRIVATE_KEY, private_key);
  }
  void add_ca_certificate(::flatbuffers::Offset<::flatbuffers::String> ca_certificate) {
    fbb_.AddOffset(DF_SSLCertificate::VT_CA_CERTIFICATE, ca_certificate);
  }
  void add_verification_setting(MECData::DE_VerificationSetting verification_setting) {
    fbb_.AddElement<int8_t>(DF_SSLCertificate::VT_VERIFICATION_SETTING, static_cast<int8_t>(verification_setting), 1);
  }
  explicit DF_SSLCertificateBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_SSLCertificate> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_SSLCertificate>(end);
    fbb_.Required(o, DF_SSLCertificate::VT_USAGE);
    fbb_.Required(o, DF_SSLCertificate::VT_CERTIFICATE);
    fbb_.Required(o, DF_SSLCertificate::VT_PRIVATE_KEY);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_SSLCertificate> CreateDF_SSLCertificate(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    ::flatbuffers::Offset<MECData::DE_CertificateUsage> usage = 0,
    ::flatbuffers::Offset<::flatbuffers::String> certificate = 0,
    ::flatbuffers::Offset<::flatbuffers::String> private_key = 0,
    ::flatbuffers::Offset<::flatbuffers::String> ca_certificate = 0,
    MECData::DE_VerificationSetting verification_setting = MECData::DE_VerificationSetting_CLIENT_VERIFY_REQUIRED) {
  DF_SSLCertificateBuilder builder_(_fbb);
  builder_.add_ca_certificate(ca_certificate);
  builder_.add_private_key(private_key);
  builder_.add_certificate(certificate);
  builder_.add_usage(usage);
  builder_.add_name(name);
  builder_.add_verification_setting(verification_setting);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_SSLCertificate> CreateDF_SSLCertificateDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *name = nullptr,
    ::flatbuffers::Offset<MECData::DE_CertificateUsage> usage = 0,
    const char *certificate = nullptr,
    const char *private_key = nullptr,
    const char *ca_certificate = nullptr,
    MECData::DE_VerificationSetting verification_setting = MECData::DE_VerificationSetting_CLIENT_VERIFY_REQUIRED) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto certificate__ = certificate ? _fbb.CreateString(certificate) : 0;
  auto private_key__ = private_key ? _fbb.CreateString(private_key) : 0;
  auto ca_certificate__ = ca_certificate ? _fbb.CreateString(ca_certificate) : 0;
  return MECData::CreateDF_SSLCertificate(
      _fbb,
      name__,
      usage,
      certificate__,
      private_key__,
      ca_certificate__,
      verification_setting);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_SSLCERTIFICATE_MECDATA_H_
