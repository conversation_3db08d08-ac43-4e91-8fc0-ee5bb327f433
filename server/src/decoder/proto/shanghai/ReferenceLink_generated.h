// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_REFERENCELINK_MECDATA_H_
#define FLATBUFFERS_GENERATED_REFERENCELINK_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "NodeReferenceID_generated.h"
#include "ReferenceLanes_generated.h"

namespace MECData {

struct DF_ReferenceLink;
struct DF_ReferenceLinkBuilder;

struct DF_ReferenceLink FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ReferenceLinkBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_UPSTREAMNODEID = 4,
    VT_DOWNSTREAMNODEID = 6,
    VT_REFERENCELANES = 8
  };
  const MECData::DF_NodeReferenceID *upstreamNodeId() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_UPSTREAMNODEID);
  }
  const MECData::DF_NodeReferenceID *downstreamNodeId() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_DOWNSTREAMNODEID);
  }
  const MECData::DF_ReferenceLanes *referenceLanes() const {
    return GetPointer<const MECData::DF_ReferenceLanes *>(VT_REFERENCELANES);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_UPSTREAMNODEID) &&
           verifier.VerifyTable(upstreamNodeId()) &&
           VerifyOffsetRequired(verifier, VT_DOWNSTREAMNODEID) &&
           verifier.VerifyTable(downstreamNodeId()) &&
           VerifyOffset(verifier, VT_REFERENCELANES) &&
           verifier.VerifyTable(referenceLanes()) &&
           verifier.EndTable();
  }
};

struct DF_ReferenceLinkBuilder {
  typedef DF_ReferenceLink Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_upstreamNodeId(::flatbuffers::Offset<MECData::DF_NodeReferenceID> upstreamNodeId) {
    fbb_.AddOffset(DF_ReferenceLink::VT_UPSTREAMNODEID, upstreamNodeId);
  }
  void add_downstreamNodeId(::flatbuffers::Offset<MECData::DF_NodeReferenceID> downstreamNodeId) {
    fbb_.AddOffset(DF_ReferenceLink::VT_DOWNSTREAMNODEID, downstreamNodeId);
  }
  void add_referenceLanes(::flatbuffers::Offset<MECData::DF_ReferenceLanes> referenceLanes) {
    fbb_.AddOffset(DF_ReferenceLink::VT_REFERENCELANES, referenceLanes);
  }
  explicit DF_ReferenceLinkBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ReferenceLink> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ReferenceLink>(end);
    fbb_.Required(o, DF_ReferenceLink::VT_UPSTREAMNODEID);
    fbb_.Required(o, DF_ReferenceLink::VT_DOWNSTREAMNODEID);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ReferenceLink> CreateDF_ReferenceLink(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> upstreamNodeId = 0,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> downstreamNodeId = 0,
    ::flatbuffers::Offset<MECData::DF_ReferenceLanes> referenceLanes = 0) {
  DF_ReferenceLinkBuilder builder_(_fbb);
  builder_.add_referenceLanes(referenceLanes);
  builder_.add_downstreamNodeId(downstreamNodeId);
  builder_.add_upstreamNodeId(upstreamNodeId);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_REFERENCELINK_MECDATA_H_
