// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_TICKET_MECDATA_H_
#define FLATBUFFERS_GENERATED_TICKET_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct MSG_Ticket;
struct MSG_TicketBuilder;

enum DF_TicketAction : uint8_t {
  DF_TicketAction_create = 0,
  DF_TicketAction_process = 1,
  DF_TicketAction_hold = 2,
  DF_TicketAction_resume = 3,
  DF_TicketAction_done = 4,
  DF_TicketAction_failed = 5,
  DF_TicketAction_close = 255,
  DF_TicketAction_MIN = DF_TicketAction_create,
  DF_TicketAction_MAX = DF_TicketAction_close
};

inline const DF_TicketAction (&EnumValuesDF_TicketAction())[7] {
  static const DF_TicketAction values[] = {
    DF_TicketAction_create,
    DF_TicketAction_process,
    DF_TicketAction_hold,
    DF_TicketAction_resume,
    DF_TicketAction_done,
    DF_TicketAction_failed,
    DF_TicketAction_close
  };
  return values;
}

inline const char *EnumNameDF_TicketAction(DF_TicketAction e) {
  switch (e) {
    case DF_TicketAction_create: return "create";
    case DF_TicketAction_process: return "process";
    case DF_TicketAction_hold: return "hold";
    case DF_TicketAction_resume: return "resume";
    case DF_TicketAction_done: return "done";
    case DF_TicketAction_failed: return "failed";
    case DF_TicketAction_close: return "close";
    default: return "";
  }
}

struct MSG_Ticket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_TicketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_APP_ID = 4,
    VT_TICKET_ID = 6,
    VT_TICKET_TYPE = 8,
    VT_TICKET_SUBTYPE = 10,
    VT_ACTION = 12,
    VT_TIMESTAMP = 14,
    VT_DATA_TYPE_CODE = 16,
    VT_DATA = 18,
    VT_MSG_ID = 20
  };
  uint16_t app_id() const {
    return GetField<uint16_t>(VT_APP_ID, 0);
  }
  const ::flatbuffers::String *ticket_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_TICKET_ID);
  }
  uint8_t ticket_type() const {
    return GetField<uint8_t>(VT_TICKET_TYPE, 0);
  }
  uint8_t ticket_subtype() const {
    return GetField<uint8_t>(VT_TICKET_SUBTYPE, 0);
  }
  MECData::DF_TicketAction action() const {
    return static_cast<MECData::DF_TicketAction>(GetField<uint8_t>(VT_ACTION, 0));
  }
  int64_t timestamp() const {
    return GetField<int64_t>(VT_TIMESTAMP, 0);
  }
  uint16_t data_type_code() const {
    return GetField<uint16_t>(VT_DATA_TYPE_CODE, 0);
  }
  const ::flatbuffers::Vector<uint8_t> *data() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_DATA);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_APP_ID, 2) &&
           VerifyOffset(verifier, VT_TICKET_ID) &&
           verifier.VerifyString(ticket_id()) &&
           VerifyField<uint8_t>(verifier, VT_TICKET_TYPE, 1) &&
           VerifyField<uint8_t>(verifier, VT_TICKET_SUBTYPE, 1) &&
           VerifyField<uint8_t>(verifier, VT_ACTION, 1) &&
           VerifyField<int64_t>(verifier, VT_TIMESTAMP, 8) &&
           VerifyField<uint16_t>(verifier, VT_DATA_TYPE_CODE, 2) &&
           VerifyOffset(verifier, VT_DATA) &&
           verifier.VerifyVector(data()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct MSG_TicketBuilder {
  typedef MSG_Ticket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_app_id(uint16_t app_id) {
    fbb_.AddElement<uint16_t>(MSG_Ticket::VT_APP_ID, app_id, 0);
  }
  void add_ticket_id(::flatbuffers::Offset<::flatbuffers::String> ticket_id) {
    fbb_.AddOffset(MSG_Ticket::VT_TICKET_ID, ticket_id);
  }
  void add_ticket_type(uint8_t ticket_type) {
    fbb_.AddElement<uint8_t>(MSG_Ticket::VT_TICKET_TYPE, ticket_type, 0);
  }
  void add_ticket_subtype(uint8_t ticket_subtype) {
    fbb_.AddElement<uint8_t>(MSG_Ticket::VT_TICKET_SUBTYPE, ticket_subtype, 0);
  }
  void add_action(MECData::DF_TicketAction action) {
    fbb_.AddElement<uint8_t>(MSG_Ticket::VT_ACTION, static_cast<uint8_t>(action), 0);
  }
  void add_timestamp(int64_t timestamp) {
    fbb_.AddElement<int64_t>(MSG_Ticket::VT_TIMESTAMP, timestamp, 0);
  }
  void add_data_type_code(uint16_t data_type_code) {
    fbb_.AddElement<uint16_t>(MSG_Ticket::VT_DATA_TYPE_CODE, data_type_code, 0);
  }
  void add_data(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> data) {
    fbb_.AddOffset(MSG_Ticket::VT_DATA, data);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_Ticket::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_TicketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_Ticket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_Ticket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_Ticket> CreateMSG_Ticket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t app_id = 0,
    ::flatbuffers::Offset<::flatbuffers::String> ticket_id = 0,
    uint8_t ticket_type = 0,
    uint8_t ticket_subtype = 0,
    MECData::DF_TicketAction action = MECData::DF_TicketAction_create,
    int64_t timestamp = 0,
    uint16_t data_type_code = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> data = 0,
    int64_t msg_id = 0) {
  MSG_TicketBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_timestamp(timestamp);
  builder_.add_data(data);
  builder_.add_ticket_id(ticket_id);
  builder_.add_data_type_code(data_type_code);
  builder_.add_app_id(app_id);
  builder_.add_action(action);
  builder_.add_ticket_subtype(ticket_subtype);
  builder_.add_ticket_type(ticket_type);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_Ticket> CreateMSG_TicketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t app_id = 0,
    const char *ticket_id = nullptr,
    uint8_t ticket_type = 0,
    uint8_t ticket_subtype = 0,
    MECData::DF_TicketAction action = MECData::DF_TicketAction_create,
    int64_t timestamp = 0,
    uint16_t data_type_code = 0,
    const std::vector<uint8_t> *data = nullptr,
    int64_t msg_id = 0) {
  auto ticket_id__ = ticket_id ? _fbb.CreateString(ticket_id) : 0;
  auto data__ = data ? _fbb.CreateVector<uint8_t>(*data) : 0;
  return MECData::CreateMSG_Ticket(
      _fbb,
      app_id,
      ticket_id__,
      ticket_type,
      ticket_subtype,
      action,
      timestamp,
      data_type_code,
      data__,
      msg_id);
}

inline const MECData::MSG_Ticket *GetMSG_Ticket(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_Ticket>(buf);
}

inline const MECData::MSG_Ticket *GetSizePrefixedMSG_Ticket(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_Ticket>(buf);
}

inline bool VerifyMSG_TicketBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_Ticket>(nullptr);
}

inline bool VerifySizePrefixedMSG_TicketBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_Ticket>(nullptr);
}

inline void FinishMSG_TicketBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_Ticket> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_TicketBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_Ticket> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_TICKET_MECDATA_H_
