// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_REQPARKINGAREA_MECDATA_H_
#define FLATBUFFERS_GENERATED_REQPARKINGAREA_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "ParkingRequest_generated.h"
#include "ParkingType_generated.h"
#include "VehicleClassification_generated.h"

namespace MECData {

struct DF_ReqParkingArea;
struct DF_ReqParkingAreaBuilder;

struct DF_ReqParkingArea FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ReqParkingAreaBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VEHICLECLASS = 4,
    VT_REQ = 6,
    VT_PARKINGTYPE = 8,
    VT_EXPECTEDPARKINGSLOTID = 10
  };
  const MECData::DF_VehicleClassification *vehicleClass() const {
    return GetPointer<const MECData::DF_VehicleClassification *>(VT_VEHICLECLASS);
  }
  const MECData::DE_ParkingRequest *req() const {
    return GetPointer<const MECData::DE_ParkingRequest *>(VT_REQ);
  }
  const MECData::DE_ParkingType *parkingType() const {
    return GetPointer<const MECData::DE_ParkingType *>(VT_PARKINGTYPE);
  }
  uint16_t expectedParkingSlotID() const {
    return GetField<uint16_t>(VT_EXPECTEDPARKINGSLOTID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_VEHICLECLASS) &&
           verifier.VerifyTable(vehicleClass()) &&
           VerifyOffsetRequired(verifier, VT_REQ) &&
           verifier.VerifyTable(req()) &&
           VerifyOffset(verifier, VT_PARKINGTYPE) &&
           verifier.VerifyTable(parkingType()) &&
           VerifyField<uint16_t>(verifier, VT_EXPECTEDPARKINGSLOTID, 2) &&
           verifier.EndTable();
  }
};

struct DF_ReqParkingAreaBuilder {
  typedef DF_ReqParkingArea Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_vehicleClass(::flatbuffers::Offset<MECData::DF_VehicleClassification> vehicleClass) {
    fbb_.AddOffset(DF_ReqParkingArea::VT_VEHICLECLASS, vehicleClass);
  }
  void add_req(::flatbuffers::Offset<MECData::DE_ParkingRequest> req) {
    fbb_.AddOffset(DF_ReqParkingArea::VT_REQ, req);
  }
  void add_parkingType(::flatbuffers::Offset<MECData::DE_ParkingType> parkingType) {
    fbb_.AddOffset(DF_ReqParkingArea::VT_PARKINGTYPE, parkingType);
  }
  void add_expectedParkingSlotID(uint16_t expectedParkingSlotID) {
    fbb_.AddElement<uint16_t>(DF_ReqParkingArea::VT_EXPECTEDPARKINGSLOTID, expectedParkingSlotID, 0);
  }
  explicit DF_ReqParkingAreaBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ReqParkingArea> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ReqParkingArea>(end);
    fbb_.Required(o, DF_ReqParkingArea::VT_VEHICLECLASS);
    fbb_.Required(o, DF_ReqParkingArea::VT_REQ);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ReqParkingArea> CreateDF_ReqParkingArea(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_VehicleClassification> vehicleClass = 0,
    ::flatbuffers::Offset<MECData::DE_ParkingRequest> req = 0,
    ::flatbuffers::Offset<MECData::DE_ParkingType> parkingType = 0,
    uint16_t expectedParkingSlotID = 0) {
  DF_ReqParkingAreaBuilder builder_(_fbb);
  builder_.add_parkingType(parkingType);
  builder_.add_req(req);
  builder_.add_vehicleClass(vehicleClass);
  builder_.add_expectedParkingSlotID(expectedParkingSlotID);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_REQPARKINGAREA_MECDATA_H_
