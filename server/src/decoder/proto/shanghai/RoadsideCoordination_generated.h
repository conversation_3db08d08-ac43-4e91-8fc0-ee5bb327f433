// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_ROADSIDECOORDINATION_MECDATA_H_
#define FLATBUFFERS_GENERATED_ROADSIDECOORDINATION_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "DebugTimeRecords_generated.h"
#include "LaneCoordination_generated.h"
#include "Position3D_generated.h"
#include "VehicleCoordination_generated.h"

namespace MECData {

struct MSG_RoadSideCoordination;
struct MSG_RoadSideCoordinationBuilder;

struct MSG_RoadSideCoordination FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_RoadSideCoordinationBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ID = 4,
    VT_SECMARK = 6,
    VT_REFPOS = 8,
    VT_COORDINATES = 10,
    VT_LANECOORDINATES = 12,
    VT_TIME_RECORDS = 14,
    VT_MSG_ID = 16,
    VT_RSU_ID = 18
  };
  uint16_t id() const {
    return GetField<uint16_t>(VT_ID, 0);
  }
  uint16_t secMark() const {
    return GetField<uint16_t>(VT_SECMARK, 65535);
  }
  const MECData::DF_Position3D *refPos() const {
    return GetPointer<const MECData::DF_Position3D *>(VT_REFPOS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_VehicleCoordination>> *coordinates() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_VehicleCoordination>> *>(VT_COORDINATES);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_LaneCoordination>> *laneCoordinates() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_LaneCoordination>> *>(VT_LANECOORDINATES);
  }
  const MECData::DF_DebugTimeRecords *time_records() const {
    return GetPointer<const MECData::DF_DebugTimeRecords *>(VT_TIME_RECORDS);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  const ::flatbuffers::Vector<uint8_t> *rsu_id() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_RSU_ID);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_ID, 2) &&
           VerifyField<uint16_t>(verifier, VT_SECMARK, 2) &&
           VerifyOffsetRequired(verifier, VT_REFPOS) &&
           verifier.VerifyTable(refPos()) &&
           VerifyOffset(verifier, VT_COORDINATES) &&
           verifier.VerifyVector(coordinates()) &&
           verifier.VerifyVectorOfTables(coordinates()) &&
           VerifyOffset(verifier, VT_LANECOORDINATES) &&
           verifier.VerifyVector(laneCoordinates()) &&
           verifier.VerifyVectorOfTables(laneCoordinates()) &&
           VerifyOffset(verifier, VT_TIME_RECORDS) &&
           verifier.VerifyTable(time_records()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           VerifyOffset(verifier, VT_RSU_ID) &&
           verifier.VerifyVector(rsu_id()) &&
           verifier.EndTable();
  }
};

struct MSG_RoadSideCoordinationBuilder {
  typedef MSG_RoadSideCoordination Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_id(uint16_t id) {
    fbb_.AddElement<uint16_t>(MSG_RoadSideCoordination::VT_ID, id, 0);
  }
  void add_secMark(uint16_t secMark) {
    fbb_.AddElement<uint16_t>(MSG_RoadSideCoordination::VT_SECMARK, secMark, 65535);
  }
  void add_refPos(::flatbuffers::Offset<MECData::DF_Position3D> refPos) {
    fbb_.AddOffset(MSG_RoadSideCoordination::VT_REFPOS, refPos);
  }
  void add_coordinates(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_VehicleCoordination>>> coordinates) {
    fbb_.AddOffset(MSG_RoadSideCoordination::VT_COORDINATES, coordinates);
  }
  void add_laneCoordinates(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_LaneCoordination>>> laneCoordinates) {
    fbb_.AddOffset(MSG_RoadSideCoordination::VT_LANECOORDINATES, laneCoordinates);
  }
  void add_time_records(::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records) {
    fbb_.AddOffset(MSG_RoadSideCoordination::VT_TIME_RECORDS, time_records);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_RoadSideCoordination::VT_MSG_ID, msg_id, 0);
  }
  void add_rsu_id(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> rsu_id) {
    fbb_.AddOffset(MSG_RoadSideCoordination::VT_RSU_ID, rsu_id);
  }
  explicit MSG_RoadSideCoordinationBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_RoadSideCoordination> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_RoadSideCoordination>(end);
    fbb_.Required(o, MSG_RoadSideCoordination::VT_REFPOS);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_RoadSideCoordination> CreateMSG_RoadSideCoordination(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t id = 0,
    uint16_t secMark = 65535,
    ::flatbuffers::Offset<MECData::DF_Position3D> refPos = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_VehicleCoordination>>> coordinates = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_LaneCoordination>>> laneCoordinates = 0,
    ::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records = 0,
    int64_t msg_id = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> rsu_id = 0) {
  MSG_RoadSideCoordinationBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_rsu_id(rsu_id);
  builder_.add_time_records(time_records);
  builder_.add_laneCoordinates(laneCoordinates);
  builder_.add_coordinates(coordinates);
  builder_.add_refPos(refPos);
  builder_.add_secMark(secMark);
  builder_.add_id(id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_RoadSideCoordination> CreateMSG_RoadSideCoordinationDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t id = 0,
    uint16_t secMark = 65535,
    ::flatbuffers::Offset<MECData::DF_Position3D> refPos = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_VehicleCoordination>> *coordinates = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_LaneCoordination>> *laneCoordinates = nullptr,
    ::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records = 0,
    int64_t msg_id = 0,
    const std::vector<uint8_t> *rsu_id = nullptr) {
  auto coordinates__ = coordinates ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_VehicleCoordination>>(*coordinates) : 0;
  auto laneCoordinates__ = laneCoordinates ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_LaneCoordination>>(*laneCoordinates) : 0;
  auto rsu_id__ = rsu_id ? _fbb.CreateVector<uint8_t>(*rsu_id) : 0;
  return MECData::CreateMSG_RoadSideCoordination(
      _fbb,
      id,
      secMark,
      refPos,
      coordinates__,
      laneCoordinates__,
      time_records,
      msg_id,
      rsu_id__);
}

inline const MECData::MSG_RoadSideCoordination *GetMSG_RoadSideCoordination(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_RoadSideCoordination>(buf);
}

inline const MECData::MSG_RoadSideCoordination *GetSizePrefixedMSG_RoadSideCoordination(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_RoadSideCoordination>(buf);
}

inline bool VerifyMSG_RoadSideCoordinationBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_RoadSideCoordination>(nullptr);
}

inline bool VerifySizePrefixedMSG_RoadSideCoordinationBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_RoadSideCoordination>(nullptr);
}

inline void FinishMSG_RoadSideCoordinationBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_RoadSideCoordination> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_RoadSideCoordinationBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_RoadSideCoordination> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_ROADSIDECOORDINATION_MECDATA_H_
