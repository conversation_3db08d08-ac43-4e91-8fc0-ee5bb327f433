// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_TIMECOUNTINGDOWN_MECDATA_H_
#define FLATBUFFERS_GENERATED_TIMECOUNTINGDOWN_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_TimeCountingDown;
struct DF_TimeCountingDownBuilder;

struct DF_TimeCountingDown FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_TimeCountingDownBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_STARTTIME = 4,
    VT_MINENDTIME = 6,
    VT_MAXENDTIME = 8,
    VT_LIKELYENDTIME = 10,
    VT_TIMECONFIDENCE = 12,
    VT_NEXTSTARTTIME = 14,
    VT_NEXTDURATION = 16
  };
  uint16_t startTime() const {
    return GetField<uint16_t>(VT_STARTTIME, 65535);
  }
  uint16_t minEndTime() const {
    return GetField<uint16_t>(VT_MINENDTIME, 0);
  }
  uint16_t maxEndTime() const {
    return GetField<uint16_t>(VT_MAXENDTIME, 0);
  }
  uint16_t likelyEndTime() const {
    return GetField<uint16_t>(VT_LIKELYENDTIME, 65535);
  }
  uint16_t timeConfidence() const {
    return GetField<uint16_t>(VT_TIMECONFIDENCE, 0);
  }
  uint16_t nextStartTime() const {
    return GetField<uint16_t>(VT_NEXTSTARTTIME, 0);
  }
  uint16_t nextDuration() const {
    return GetField<uint16_t>(VT_NEXTDURATION, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_STARTTIME, 2) &&
           VerifyField<uint16_t>(verifier, VT_MINENDTIME, 2) &&
           VerifyField<uint16_t>(verifier, VT_MAXENDTIME, 2) &&
           VerifyField<uint16_t>(verifier, VT_LIKELYENDTIME, 2) &&
           VerifyField<uint16_t>(verifier, VT_TIMECONFIDENCE, 2) &&
           VerifyField<uint16_t>(verifier, VT_NEXTSTARTTIME, 2) &&
           VerifyField<uint16_t>(verifier, VT_NEXTDURATION, 2) &&
           verifier.EndTable();
  }
};

struct DF_TimeCountingDownBuilder {
  typedef DF_TimeCountingDown Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_startTime(uint16_t startTime) {
    fbb_.AddElement<uint16_t>(DF_TimeCountingDown::VT_STARTTIME, startTime, 65535);
  }
  void add_minEndTime(uint16_t minEndTime) {
    fbb_.AddElement<uint16_t>(DF_TimeCountingDown::VT_MINENDTIME, minEndTime, 0);
  }
  void add_maxEndTime(uint16_t maxEndTime) {
    fbb_.AddElement<uint16_t>(DF_TimeCountingDown::VT_MAXENDTIME, maxEndTime, 0);
  }
  void add_likelyEndTime(uint16_t likelyEndTime) {
    fbb_.AddElement<uint16_t>(DF_TimeCountingDown::VT_LIKELYENDTIME, likelyEndTime, 65535);
  }
  void add_timeConfidence(uint16_t timeConfidence) {
    fbb_.AddElement<uint16_t>(DF_TimeCountingDown::VT_TIMECONFIDENCE, timeConfidence, 0);
  }
  void add_nextStartTime(uint16_t nextStartTime) {
    fbb_.AddElement<uint16_t>(DF_TimeCountingDown::VT_NEXTSTARTTIME, nextStartTime, 0);
  }
  void add_nextDuration(uint16_t nextDuration) {
    fbb_.AddElement<uint16_t>(DF_TimeCountingDown::VT_NEXTDURATION, nextDuration, 0);
  }
  explicit DF_TimeCountingDownBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_TimeCountingDown> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_TimeCountingDown>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_TimeCountingDown> CreateDF_TimeCountingDown(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t startTime = 65535,
    uint16_t minEndTime = 0,
    uint16_t maxEndTime = 0,
    uint16_t likelyEndTime = 65535,
    uint16_t timeConfidence = 0,
    uint16_t nextStartTime = 0,
    uint16_t nextDuration = 0) {
  DF_TimeCountingDownBuilder builder_(_fbb);
  builder_.add_nextDuration(nextDuration);
  builder_.add_nextStartTime(nextStartTime);
  builder_.add_timeConfidence(timeConfidence);
  builder_.add_likelyEndTime(likelyEndTime);
  builder_.add_maxEndTime(maxEndTime);
  builder_.add_minEndTime(minEndTime);
  builder_.add_startTime(startTime);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_TIMECOUNTINGDOWN_MECDATA_H_
