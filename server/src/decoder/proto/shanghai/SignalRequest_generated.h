// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_SIGNALREQUEST_MECDATA_H_
#define FLATBUFFERS_GENERATED_SIGNALREQUEST_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "DebugTimeRecords_generated.h"
#include "SignalControlMode_generated.h"
#include "SignalControllerConfig_generated.h"
#include "SignalScheme_generated.h"

namespace MECData {

struct DF_SignalRequestByScheme;
struct DF_SignalRequestBySchemeBuilder;

struct DF_SignalRequestByPhase;
struct DF_SignalRequestByPhaseBuilder;

struct DF_SignalRequestPhaseActivate;
struct DF_SignalRequestPhaseActivateBuilder;

struct DF_SignalRequestByConfig;
struct DF_SignalRequestByConfigBuilder;

struct DF_SignalRequestBySchemeConstraint;
struct DF_SignalRequestBySchemeConstraintBuilder;

struct MSG_SignalRequest;
struct MSG_SignalRequestBuilder;

enum DF_SignalRequestDetails : uint8_t {
  DF_SignalRequestDetails_NONE = 0,
  DF_SignalRequestDetails_DF_SignalRequestByScheme = 1,
  DF_SignalRequestDetails_DF_SignalRequestByPhase = 2,
  DF_SignalRequestDetails_DF_SignalRequestPhaseActivate = 3,
  DF_SignalRequestDetails_DF_SignalRequestByConfig = 4,
  DF_SignalRequestDetails_DF_SignalRequestBySchemeConstraint = 5,
  DF_SignalRequestDetails_MIN = DF_SignalRequestDetails_NONE,
  DF_SignalRequestDetails_MAX = DF_SignalRequestDetails_DF_SignalRequestBySchemeConstraint
};

inline const DF_SignalRequestDetails (&EnumValuesDF_SignalRequestDetails())[6] {
  static const DF_SignalRequestDetails values[] = {
    DF_SignalRequestDetails_NONE,
    DF_SignalRequestDetails_DF_SignalRequestByScheme,
    DF_SignalRequestDetails_DF_SignalRequestByPhase,
    DF_SignalRequestDetails_DF_SignalRequestPhaseActivate,
    DF_SignalRequestDetails_DF_SignalRequestByConfig,
    DF_SignalRequestDetails_DF_SignalRequestBySchemeConstraint
  };
  return values;
}

inline const char * const *EnumNamesDF_SignalRequestDetails() {
  static const char * const names[7] = {
    "NONE",
    "DF_SignalRequestByScheme",
    "DF_SignalRequestByPhase",
    "DF_SignalRequestPhaseActivate",
    "DF_SignalRequestByConfig",
    "DF_SignalRequestBySchemeConstraint",
    nullptr
  };
  return names;
}

inline const char *EnumNameDF_SignalRequestDetails(DF_SignalRequestDetails e) {
  if (::flatbuffers::IsOutRange(e, DF_SignalRequestDetails_NONE, DF_SignalRequestDetails_DF_SignalRequestBySchemeConstraint)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDF_SignalRequestDetails()[index];
}

template<typename T> struct DF_SignalRequestDetailsTraits {
  static const DF_SignalRequestDetails enum_value = DF_SignalRequestDetails_NONE;
};

template<> struct DF_SignalRequestDetailsTraits<MECData::DF_SignalRequestByScheme> {
  static const DF_SignalRequestDetails enum_value = DF_SignalRequestDetails_DF_SignalRequestByScheme;
};

template<> struct DF_SignalRequestDetailsTraits<MECData::DF_SignalRequestByPhase> {
  static const DF_SignalRequestDetails enum_value = DF_SignalRequestDetails_DF_SignalRequestByPhase;
};

template<> struct DF_SignalRequestDetailsTraits<MECData::DF_SignalRequestPhaseActivate> {
  static const DF_SignalRequestDetails enum_value = DF_SignalRequestDetails_DF_SignalRequestPhaseActivate;
};

template<> struct DF_SignalRequestDetailsTraits<MECData::DF_SignalRequestByConfig> {
  static const DF_SignalRequestDetails enum_value = DF_SignalRequestDetails_DF_SignalRequestByConfig;
};

template<> struct DF_SignalRequestDetailsTraits<MECData::DF_SignalRequestBySchemeConstraint> {
  static const DF_SignalRequestDetails enum_value = DF_SignalRequestDetails_DF_SignalRequestBySchemeConstraint;
};

bool VerifyDF_SignalRequestDetails(::flatbuffers::Verifier &verifier, const void *obj, DF_SignalRequestDetails type);
bool VerifyDF_SignalRequestDetailsVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types);

enum DE_SignalRequestSource : uint8_t {
  DE_SignalRequestSource_UNKNOWN = 0,
  DE_SignalRequestSource_LOCAL_ALGORITHM = 1,
  DE_SignalRequestSource_LOCAL_DASHBOARD = 2,
  DE_SignalRequestSource_DEVICE_INPUT = 3,
  DE_SignalRequestSource_CLOUD_ALGORITHM = 4,
  DE_SignalRequestSource_CLOUD_DASHBOARD = 5,
  DE_SignalRequestSource_MIN = DE_SignalRequestSource_UNKNOWN,
  DE_SignalRequestSource_MAX = DE_SignalRequestSource_CLOUD_DASHBOARD
};

inline const DE_SignalRequestSource (&EnumValuesDE_SignalRequestSource())[6] {
  static const DE_SignalRequestSource values[] = {
    DE_SignalRequestSource_UNKNOWN,
    DE_SignalRequestSource_LOCAL_ALGORITHM,
    DE_SignalRequestSource_LOCAL_DASHBOARD,
    DE_SignalRequestSource_DEVICE_INPUT,
    DE_SignalRequestSource_CLOUD_ALGORITHM,
    DE_SignalRequestSource_CLOUD_DASHBOARD
  };
  return values;
}

inline const char * const *EnumNamesDE_SignalRequestSource() {
  static const char * const names[7] = {
    "UNKNOWN",
    "LOCAL_ALGORITHM",
    "LOCAL_DASHBOARD",
    "DEVICE_INPUT",
    "CLOUD_ALGORITHM",
    "CLOUD_DASHBOARD",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_SignalRequestSource(DE_SignalRequestSource e) {
  if (::flatbuffers::IsOutRange(e, DE_SignalRequestSource_UNKNOWN, DE_SignalRequestSource_CLOUD_DASHBOARD)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_SignalRequestSource()[index];
}

struct DF_SignalRequestByScheme FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SignalRequestBySchemeBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SIGNAL_SCHEME = 4
  };
  const ::flatbuffers::Vector<uint8_t> *signal_scheme() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_SIGNAL_SCHEME);
  }
  const MECData::MSG_SignalScheme *signal_scheme_nested_root() const {
    const auto _f = signal_scheme();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_SignalScheme>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_SIGNAL_SCHEME) &&
           verifier.VerifyVector(signal_scheme()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_SignalScheme>(signal_scheme(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_SignalRequestBySchemeBuilder {
  typedef DF_SignalRequestByScheme Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_signal_scheme(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> signal_scheme) {
    fbb_.AddOffset(DF_SignalRequestByScheme::VT_SIGNAL_SCHEME, signal_scheme);
  }
  explicit DF_SignalRequestBySchemeBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_SignalRequestByScheme> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_SignalRequestByScheme>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_SignalRequestByScheme> CreateDF_SignalRequestByScheme(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> signal_scheme = 0) {
  DF_SignalRequestBySchemeBuilder builder_(_fbb);
  builder_.add_signal_scheme(signal_scheme);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_SignalRequestByScheme> CreateDF_SignalRequestBySchemeDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *signal_scheme = nullptr) {
  auto signal_scheme__ = signal_scheme ? _fbb.CreateVector<uint8_t>(*signal_scheme) : 0;
  return MECData::CreateDF_SignalRequestByScheme(
      _fbb,
      signal_scheme__);
}

struct DF_SignalRequestByPhase FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SignalRequestByPhaseBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NEXT_PHASIC_ID = 4,
    VT_EFFECTIVE_TIME = 6
  };
  int32_t next_phasic_id() const {
    return GetField<int32_t>(VT_NEXT_PHASIC_ID, 0);
  }
  uint8_t effective_time() const {
    return GetField<uint8_t>(VT_EFFECTIVE_TIME, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_NEXT_PHASIC_ID, 4) &&
           VerifyField<uint8_t>(verifier, VT_EFFECTIVE_TIME, 1) &&
           verifier.EndTable();
  }
};

struct DF_SignalRequestByPhaseBuilder {
  typedef DF_SignalRequestByPhase Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_next_phasic_id(int32_t next_phasic_id) {
    fbb_.AddElement<int32_t>(DF_SignalRequestByPhase::VT_NEXT_PHASIC_ID, next_phasic_id, 0);
  }
  void add_effective_time(uint8_t effective_time) {
    fbb_.AddElement<uint8_t>(DF_SignalRequestByPhase::VT_EFFECTIVE_TIME, effective_time, 0);
  }
  explicit DF_SignalRequestByPhaseBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_SignalRequestByPhase> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_SignalRequestByPhase>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_SignalRequestByPhase> CreateDF_SignalRequestByPhase(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t next_phasic_id = 0,
    uint8_t effective_time = 0) {
  DF_SignalRequestByPhaseBuilder builder_(_fbb);
  builder_.add_next_phasic_id(next_phasic_id);
  builder_.add_effective_time(effective_time);
  return builder_.Finish();
}

struct DF_SignalRequestPhaseActivate FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SignalRequestPhaseActivateBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PHASIC_ID = 4
  };
  int32_t phasic_id() const {
    return GetField<int32_t>(VT_PHASIC_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_PHASIC_ID, 4) &&
           verifier.EndTable();
  }
};

struct DF_SignalRequestPhaseActivateBuilder {
  typedef DF_SignalRequestPhaseActivate Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_phasic_id(int32_t phasic_id) {
    fbb_.AddElement<int32_t>(DF_SignalRequestPhaseActivate::VT_PHASIC_ID, phasic_id, 0);
  }
  explicit DF_SignalRequestPhaseActivateBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_SignalRequestPhaseActivate> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_SignalRequestPhaseActivate>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_SignalRequestPhaseActivate> CreateDF_SignalRequestPhaseActivate(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t phasic_id = 0) {
  DF_SignalRequestPhaseActivateBuilder builder_(_fbb);
  builder_.add_phasic_id(phasic_id);
  return builder_.Finish();
}

struct DF_SignalRequestByConfig FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SignalRequestByConfigBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_CONFIG = 4
  };
  const MECData::DF_SignalControllerConfig *config() const {
    return GetPointer<const MECData::DF_SignalControllerConfig *>(VT_CONFIG);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_CONFIG) &&
           verifier.VerifyTable(config()) &&
           verifier.EndTable();
  }
};

struct DF_SignalRequestByConfigBuilder {
  typedef DF_SignalRequestByConfig Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_config(::flatbuffers::Offset<MECData::DF_SignalControllerConfig> config) {
    fbb_.AddOffset(DF_SignalRequestByConfig::VT_CONFIG, config);
  }
  explicit DF_SignalRequestByConfigBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_SignalRequestByConfig> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_SignalRequestByConfig>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_SignalRequestByConfig> CreateDF_SignalRequestByConfig(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_SignalControllerConfig> config = 0) {
  DF_SignalRequestByConfigBuilder builder_(_fbb);
  builder_.add_config(config);
  return builder_.Finish();
}

struct DF_SignalRequestBySchemeConstraint FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SignalRequestBySchemeConstraintBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SCHEME_CONSTRAINTS = 4
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SignalSchemeConstraint>> *scheme_constraints() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SignalSchemeConstraint>> *>(VT_SCHEME_CONSTRAINTS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_SCHEME_CONSTRAINTS) &&
           verifier.VerifyVector(scheme_constraints()) &&
           verifier.VerifyVectorOfTables(scheme_constraints()) &&
           verifier.EndTable();
  }
};

struct DF_SignalRequestBySchemeConstraintBuilder {
  typedef DF_SignalRequestBySchemeConstraint Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_scheme_constraints(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SignalSchemeConstraint>>> scheme_constraints) {
    fbb_.AddOffset(DF_SignalRequestBySchemeConstraint::VT_SCHEME_CONSTRAINTS, scheme_constraints);
  }
  explicit DF_SignalRequestBySchemeConstraintBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_SignalRequestBySchemeConstraint> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_SignalRequestBySchemeConstraint>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_SignalRequestBySchemeConstraint> CreateDF_SignalRequestBySchemeConstraint(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SignalSchemeConstraint>>> scheme_constraints = 0) {
  DF_SignalRequestBySchemeConstraintBuilder builder_(_fbb);
  builder_.add_scheme_constraints(scheme_constraints);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_SignalRequestBySchemeConstraint> CreateDF_SignalRequestBySchemeConstraintDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<MECData::DF_SignalSchemeConstraint>> *scheme_constraints = nullptr) {
  auto scheme_constraints__ = scheme_constraints ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_SignalSchemeConstraint>>(*scheme_constraints) : 0;
  return MECData::CreateDF_SignalRequestBySchemeConstraint(
      _fbb,
      scheme_constraints__);
}

struct MSG_SignalRequest FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_SignalRequestBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_CONTROL_MODE = 4,
    VT_DETAILS_TYPE = 6,
    VT_DETAILS = 8,
    VT_TIME_RECORDS = 10,
    VT_MSG_ID = 12,
    VT_SOURCE = 14
  };
  MECData::DE_SignalControlMode control_mode() const {
    return static_cast<MECData::DE_SignalControlMode>(GetField<uint8_t>(VT_CONTROL_MODE, 0));
  }
  MECData::DF_SignalRequestDetails details_type() const {
    return static_cast<MECData::DF_SignalRequestDetails>(GetField<uint8_t>(VT_DETAILS_TYPE, 0));
  }
  const void *details() const {
    return GetPointer<const void *>(VT_DETAILS);
  }
  template<typename T> const T *details_as() const;
  const MECData::DF_SignalRequestByScheme *details_as_DF_SignalRequestByScheme() const {
    return details_type() == MECData::DF_SignalRequestDetails_DF_SignalRequestByScheme ? static_cast<const MECData::DF_SignalRequestByScheme *>(details()) : nullptr;
  }
  const MECData::DF_SignalRequestByPhase *details_as_DF_SignalRequestByPhase() const {
    return details_type() == MECData::DF_SignalRequestDetails_DF_SignalRequestByPhase ? static_cast<const MECData::DF_SignalRequestByPhase *>(details()) : nullptr;
  }
  const MECData::DF_SignalRequestPhaseActivate *details_as_DF_SignalRequestPhaseActivate() const {
    return details_type() == MECData::DF_SignalRequestDetails_DF_SignalRequestPhaseActivate ? static_cast<const MECData::DF_SignalRequestPhaseActivate *>(details()) : nullptr;
  }
  const MECData::DF_SignalRequestByConfig *details_as_DF_SignalRequestByConfig() const {
    return details_type() == MECData::DF_SignalRequestDetails_DF_SignalRequestByConfig ? static_cast<const MECData::DF_SignalRequestByConfig *>(details()) : nullptr;
  }
  const MECData::DF_SignalRequestBySchemeConstraint *details_as_DF_SignalRequestBySchemeConstraint() const {
    return details_type() == MECData::DF_SignalRequestDetails_DF_SignalRequestBySchemeConstraint ? static_cast<const MECData::DF_SignalRequestBySchemeConstraint *>(details()) : nullptr;
  }
  const MECData::DF_DebugTimeRecords *time_records() const {
    return GetPointer<const MECData::DF_DebugTimeRecords *>(VT_TIME_RECORDS);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  MECData::DE_SignalRequestSource source() const {
    return static_cast<MECData::DE_SignalRequestSource>(GetField<uint8_t>(VT_SOURCE, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_CONTROL_MODE, 1) &&
           VerifyField<uint8_t>(verifier, VT_DETAILS_TYPE, 1) &&
           VerifyOffset(verifier, VT_DETAILS) &&
           VerifyDF_SignalRequestDetails(verifier, details(), details_type()) &&
           VerifyOffset(verifier, VT_TIME_RECORDS) &&
           verifier.VerifyTable(time_records()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           VerifyField<uint8_t>(verifier, VT_SOURCE, 1) &&
           verifier.EndTable();
  }
};

template<> inline const MECData::DF_SignalRequestByScheme *MSG_SignalRequest::details_as<MECData::DF_SignalRequestByScheme>() const {
  return details_as_DF_SignalRequestByScheme();
}

template<> inline const MECData::DF_SignalRequestByPhase *MSG_SignalRequest::details_as<MECData::DF_SignalRequestByPhase>() const {
  return details_as_DF_SignalRequestByPhase();
}

template<> inline const MECData::DF_SignalRequestPhaseActivate *MSG_SignalRequest::details_as<MECData::DF_SignalRequestPhaseActivate>() const {
  return details_as_DF_SignalRequestPhaseActivate();
}

template<> inline const MECData::DF_SignalRequestByConfig *MSG_SignalRequest::details_as<MECData::DF_SignalRequestByConfig>() const {
  return details_as_DF_SignalRequestByConfig();
}

template<> inline const MECData::DF_SignalRequestBySchemeConstraint *MSG_SignalRequest::details_as<MECData::DF_SignalRequestBySchemeConstraint>() const {
  return details_as_DF_SignalRequestBySchemeConstraint();
}

struct MSG_SignalRequestBuilder {
  typedef MSG_SignalRequest Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_control_mode(MECData::DE_SignalControlMode control_mode) {
    fbb_.AddElement<uint8_t>(MSG_SignalRequest::VT_CONTROL_MODE, static_cast<uint8_t>(control_mode), 0);
  }
  void add_details_type(MECData::DF_SignalRequestDetails details_type) {
    fbb_.AddElement<uint8_t>(MSG_SignalRequest::VT_DETAILS_TYPE, static_cast<uint8_t>(details_type), 0);
  }
  void add_details(::flatbuffers::Offset<void> details) {
    fbb_.AddOffset(MSG_SignalRequest::VT_DETAILS, details);
  }
  void add_time_records(::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records) {
    fbb_.AddOffset(MSG_SignalRequest::VT_TIME_RECORDS, time_records);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_SignalRequest::VT_MSG_ID, msg_id, 0);
  }
  void add_source(MECData::DE_SignalRequestSource source) {
    fbb_.AddElement<uint8_t>(MSG_SignalRequest::VT_SOURCE, static_cast<uint8_t>(source), 0);
  }
  explicit MSG_SignalRequestBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_SignalRequest> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_SignalRequest>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_SignalRequest> CreateMSG_SignalRequest(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    MECData::DE_SignalControlMode control_mode = MECData::DE_SignalControlMode_CYCLIC_FIXED,
    MECData::DF_SignalRequestDetails details_type = MECData::DF_SignalRequestDetails_NONE,
    ::flatbuffers::Offset<void> details = 0,
    ::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records = 0,
    int64_t msg_id = 0,
    MECData::DE_SignalRequestSource source = MECData::DE_SignalRequestSource_UNKNOWN) {
  MSG_SignalRequestBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_time_records(time_records);
  builder_.add_details(details);
  builder_.add_source(source);
  builder_.add_details_type(details_type);
  builder_.add_control_mode(control_mode);
  return builder_.Finish();
}

inline bool VerifyDF_SignalRequestDetails(::flatbuffers::Verifier &verifier, const void *obj, DF_SignalRequestDetails type) {
  switch (type) {
    case DF_SignalRequestDetails_NONE: {
      return true;
    }
    case DF_SignalRequestDetails_DF_SignalRequestByScheme: {
      auto ptr = reinterpret_cast<const MECData::DF_SignalRequestByScheme *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_SignalRequestDetails_DF_SignalRequestByPhase: {
      auto ptr = reinterpret_cast<const MECData::DF_SignalRequestByPhase *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_SignalRequestDetails_DF_SignalRequestPhaseActivate: {
      auto ptr = reinterpret_cast<const MECData::DF_SignalRequestPhaseActivate *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_SignalRequestDetails_DF_SignalRequestByConfig: {
      auto ptr = reinterpret_cast<const MECData::DF_SignalRequestByConfig *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_SignalRequestDetails_DF_SignalRequestBySchemeConstraint: {
      auto ptr = reinterpret_cast<const MECData::DF_SignalRequestBySchemeConstraint *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return true;
  }
}

inline bool VerifyDF_SignalRequestDetailsVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (::flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyDF_SignalRequestDetails(
        verifier,  values->Get(i), types->GetEnum<DF_SignalRequestDetails>(i))) {
      return false;
    }
  }
  return true;
}

inline const MECData::MSG_SignalRequest *GetMSG_SignalRequest(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_SignalRequest>(buf);
}

inline const MECData::MSG_SignalRequest *GetSizePrefixedMSG_SignalRequest(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_SignalRequest>(buf);
}

inline bool VerifyMSG_SignalRequestBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_SignalRequest>(nullptr);
}

inline bool VerifySizePrefixedMSG_SignalRequestBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_SignalRequest>(nullptr);
}

inline void FinishMSG_SignalRequestBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_SignalRequest> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_SignalRequestBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_SignalRequest> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_SIGNALREQUEST_MECDATA_H_
