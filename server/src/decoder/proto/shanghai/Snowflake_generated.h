// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_SNOWFLAKE_MECDATA_H_
#define FLATBUFFERS_GENERATED_SNOWFLAKE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_Snowflake;
struct DF_SnowflakeBuilder;

struct DF_Snowflake FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SnowflakeBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_DATACENTER_ID = 4,
    VT_MACHINE_ID = 6
  };
  int64_t datacenter_id() const {
    return GetField<int64_t>(VT_DATACENTER_ID, 0);
  }
  int64_t machine_id() const {
    return GetField<int64_t>(VT_MACHINE_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int64_t>(verifier, VT_DATACENTER_ID, 8) &&
           VerifyField<int64_t>(verifier, VT_MACHINE_ID, 8) &&
           verifier.EndTable();
  }
};

struct DF_SnowflakeBuilder {
  typedef DF_Snowflake Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_datacenter_id(int64_t datacenter_id) {
    fbb_.AddElement<int64_t>(DF_Snowflake::VT_DATACENTER_ID, datacenter_id, 0);
  }
  void add_machine_id(int64_t machine_id) {
    fbb_.AddElement<int64_t>(DF_Snowflake::VT_MACHINE_ID, machine_id, 0);
  }
  explicit DF_SnowflakeBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_Snowflake> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_Snowflake>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_Snowflake> CreateDF_Snowflake(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int64_t datacenter_id = 0,
    int64_t machine_id = 0) {
  DF_SnowflakeBuilder builder_(_fbb);
  builder_.add_machine_id(machine_id);
  builder_.add_datacenter_id(datacenter_id);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_SNOWFLAKE_MECDATA_H_
