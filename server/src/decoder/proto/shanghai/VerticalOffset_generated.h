// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_VERTICALOFFSET_MECDATA_H_
#define FLATBUFFERS_GENERATED_VERTICALOFFSET_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "VertOffsetB07_generated.h"
#include "VertOffsetB08_generated.h"
#include "VertOffsetB09_generated.h"
#include "VertOffsetB10_generated.h"
#include "VertOffsetB11_generated.h"
#include "VertOffsetB12_generated.h"

namespace MECData {

struct DF_Elevation;
struct DF_ElevationBuilder;

enum DF_VerticalOffset : uint8_t {
  DF_VerticalOffset_NONE = 0,
  DF_VerticalOffset_DF_VertOffsetB07 = 1,
  DF_VerticalOffset_DF_VertOffsetB08 = 2,
  DF_VerticalOffset_DF_VertOffsetB09 = 3,
  DF_VerticalOffset_DF_VertOffsetB10 = 4,
  DF_VerticalOffset_DF_VertOffsetB11 = 5,
  DF_VerticalOffset_DF_VertOffsetB12 = 6,
  DF_VerticalOffset_DF_Elevation = 7,
  DF_VerticalOffset_MIN = DF_VerticalOffset_NONE,
  DF_VerticalOffset_MAX = DF_VerticalOffset_DF_Elevation
};

inline const DF_VerticalOffset (&EnumValuesDF_VerticalOffset())[8] {
  static const DF_VerticalOffset values[] = {
    DF_VerticalOffset_NONE,
    DF_VerticalOffset_DF_VertOffsetB07,
    DF_VerticalOffset_DF_VertOffsetB08,
    DF_VerticalOffset_DF_VertOffsetB09,
    DF_VerticalOffset_DF_VertOffsetB10,
    DF_VerticalOffset_DF_VertOffsetB11,
    DF_VerticalOffset_DF_VertOffsetB12,
    DF_VerticalOffset_DF_Elevation
  };
  return values;
}

inline const char * const *EnumNamesDF_VerticalOffset() {
  static const char * const names[9] = {
    "NONE",
    "DF_VertOffsetB07",
    "DF_VertOffsetB08",
    "DF_VertOffsetB09",
    "DF_VertOffsetB10",
    "DF_VertOffsetB11",
    "DF_VertOffsetB12",
    "DF_Elevation",
    nullptr
  };
  return names;
}

inline const char *EnumNameDF_VerticalOffset(DF_VerticalOffset e) {
  if (::flatbuffers::IsOutRange(e, DF_VerticalOffset_NONE, DF_VerticalOffset_DF_Elevation)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDF_VerticalOffset()[index];
}

template<typename T> struct DF_VerticalOffsetTraits {
  static const DF_VerticalOffset enum_value = DF_VerticalOffset_NONE;
};

template<> struct DF_VerticalOffsetTraits<MECData::DF_VertOffsetB07> {
  static const DF_VerticalOffset enum_value = DF_VerticalOffset_DF_VertOffsetB07;
};

template<> struct DF_VerticalOffsetTraits<MECData::DF_VertOffsetB08> {
  static const DF_VerticalOffset enum_value = DF_VerticalOffset_DF_VertOffsetB08;
};

template<> struct DF_VerticalOffsetTraits<MECData::DF_VertOffsetB09> {
  static const DF_VerticalOffset enum_value = DF_VerticalOffset_DF_VertOffsetB09;
};

template<> struct DF_VerticalOffsetTraits<MECData::DF_VertOffsetB10> {
  static const DF_VerticalOffset enum_value = DF_VerticalOffset_DF_VertOffsetB10;
};

template<> struct DF_VerticalOffsetTraits<MECData::DF_VertOffsetB11> {
  static const DF_VerticalOffset enum_value = DF_VerticalOffset_DF_VertOffsetB11;
};

template<> struct DF_VerticalOffsetTraits<MECData::DF_VertOffsetB12> {
  static const DF_VerticalOffset enum_value = DF_VerticalOffset_DF_VertOffsetB12;
};

template<> struct DF_VerticalOffsetTraits<MECData::DF_Elevation> {
  static const DF_VerticalOffset enum_value = DF_VerticalOffset_DF_Elevation;
};

bool VerifyDF_VerticalOffset(::flatbuffers::Verifier &verifier, const void *obj, DF_VerticalOffset type);
bool VerifyDF_VerticalOffsetVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types);

struct DF_Elevation FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ElevationBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ELE = 4
  };
  int16_t ele() const {
    return GetField<int16_t>(VT_ELE, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int16_t>(verifier, VT_ELE, 2) &&
           verifier.EndTable();
  }
};

struct DF_ElevationBuilder {
  typedef DF_Elevation Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_ele(int16_t ele) {
    fbb_.AddElement<int16_t>(DF_Elevation::VT_ELE, ele, 0);
  }
  explicit DF_ElevationBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_Elevation> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_Elevation>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_Elevation> CreateDF_Elevation(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int16_t ele = 0) {
  DF_ElevationBuilder builder_(_fbb);
  builder_.add_ele(ele);
  return builder_.Finish();
}

inline bool VerifyDF_VerticalOffset(::flatbuffers::Verifier &verifier, const void *obj, DF_VerticalOffset type) {
  switch (type) {
    case DF_VerticalOffset_NONE: {
      return true;
    }
    case DF_VerticalOffset_DF_VertOffsetB07: {
      auto ptr = reinterpret_cast<const MECData::DF_VertOffsetB07 *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_VerticalOffset_DF_VertOffsetB08: {
      auto ptr = reinterpret_cast<const MECData::DF_VertOffsetB08 *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_VerticalOffset_DF_VertOffsetB09: {
      auto ptr = reinterpret_cast<const MECData::DF_VertOffsetB09 *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_VerticalOffset_DF_VertOffsetB10: {
      auto ptr = reinterpret_cast<const MECData::DF_VertOffsetB10 *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_VerticalOffset_DF_VertOffsetB11: {
      auto ptr = reinterpret_cast<const MECData::DF_VertOffsetB11 *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_VerticalOffset_DF_VertOffsetB12: {
      auto ptr = reinterpret_cast<const MECData::DF_VertOffsetB12 *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_VerticalOffset_DF_Elevation: {
      auto ptr = reinterpret_cast<const MECData::DF_Elevation *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return true;
  }
}

inline bool VerifyDF_VerticalOffsetVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (::flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyDF_VerticalOffset(
        verifier,  values->Get(i), types->GetEnum<DF_VerticalOffset>(i))) {
      return false;
    }
  }
  return true;
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_VERTICALOFFSET_MECDATA_H_
