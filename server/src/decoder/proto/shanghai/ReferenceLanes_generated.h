// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_REFERENCELANES_MECDATA_H_
#define FLATBUFFERS_GENERATED_REFERENCELANES_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_ReferenceLanes;
struct DF_ReferenceLanesBuilder;

struct DF_ReferenceLanes FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ReferenceLanesBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_REFERENCELANES = 4
  };
  int16_t referenceLanes() const {
    return GetField<int16_t>(VT_REFERENCELANES, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int16_t>(verifier, VT_REFERENCELANES, 2) &&
           verifier.EndTable();
  }
};

struct DF_ReferenceLanesBuilder {
  typedef DF_ReferenceLanes Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_referenceLanes(int16_t referenceLanes) {
    fbb_.AddElement<int16_t>(DF_ReferenceLanes::VT_REFERENCELANES, referenceLanes, 0);
  }
  explicit DF_ReferenceLanesBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ReferenceLanes> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ReferenceLanes>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ReferenceLanes> CreateDF_ReferenceLanes(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int16_t referenceLanes = 0) {
  DF_ReferenceLanesBuilder builder_(_fbb);
  builder_.add_referenceLanes(referenceLanes);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_REFERENCELANES_MECDATA_H_
