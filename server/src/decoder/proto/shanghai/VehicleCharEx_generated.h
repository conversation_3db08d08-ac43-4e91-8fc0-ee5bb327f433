// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_VEHICLECHAREX_MECDATA_H_
#define FLATBUFFERS_GENERATED_VEHICLECHAREX_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_VehicleCharEx;
struct DF_VehicleCharExBuilder;

enum DE_PlateColor : uint8_t {
  DE_PlateColor_UNKNOWN_PLATE_COLOR = 0,
  DE_PlateColor_BLUE_PLATE = 1,
  DE_PlateColor_YELLOW_PLATE = 2,
  DE_PlateColor_WHITE_PLATE = 3,
  DE_PlateColor_BLACK_PLATE = 4,
  DE_PlateColor_YELLOW_GREEN_PLATE = 5,
  DE_PlateColor_GRADIENT_GREEN_PLATE = 6,
  DE_PlateColor_MIN = DE_PlateColor_UNKNOWN_PLATE_COLOR,
  DE_PlateColor_MAX = DE_PlateColor_GRADIENT_GREEN_PLATE
};

inline const DE_PlateColor (&EnumValuesDE_PlateColor())[7] {
  static const DE_PlateColor values[] = {
    DE_PlateColor_UNKNOWN_PLATE_COLOR,
    DE_PlateColor_BLUE_PLATE,
    DE_PlateColor_YELLOW_PLATE,
    DE_PlateColor_WHITE_PLATE,
    DE_PlateColor_BLACK_PLATE,
    DE_PlateColor_YELLOW_GREEN_PLATE,
    DE_PlateColor_GRADIENT_GREEN_PLATE
  };
  return values;
}

inline const char * const *EnumNamesDE_PlateColor() {
  static const char * const names[8] = {
    "UNKNOWN_PLATE_COLOR",
    "BLUE_PLATE",
    "YELLOW_PLATE",
    "WHITE_PLATE",
    "BLACK_PLATE",
    "YELLOW_GREEN_PLATE",
    "GRADIENT_GREEN_PLATE",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_PlateColor(DE_PlateColor e) {
  if (::flatbuffers::IsOutRange(e, DE_PlateColor_UNKNOWN_PLATE_COLOR, DE_PlateColor_GRADIENT_GREEN_PLATE)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_PlateColor()[index];
}

enum DE_PlateType : uint8_t {
  DE_PlateType_UNKNOWN_PLATE = 0,
  DE_PlateType_LARGE_CAR_PLATE = 1,
  DE_PlateType_SMALL_CAR_PLATE = 2,
  DE_PlateType_EMBASSY_CAR_PLATE = 3,
  DE_PlateType_CONSULATE_CAR_PLATE = 4,
  DE_PlateType_OVERSEAS_CAR_PLATE = 5,
  DE_PlateType_FOREIGN_CAR_PLATE = 6,
  DE_PlateType_ORDINARY_MOTORCYCLE_PLATE = 7,
  DE_PlateType_MOPED_PLATE = 8,
  DE_PlateType_EMBASSY_MOTORCYCLE_PLATE = 9,
  DE_PlateType_CONSULATE_MOTORCYCLE_PLATE = 10,
  DE_PlateType_OVERSEAS_MOTORCYCLE_PLATE = 11,
  DE_PlateType_FOREIGN_MOTORCYCLE_PLATE = 12,
  DE_PlateType_LOW_SPEED_PLATE = 13,
  DE_PlateType_TRACTOR_PLATE = 14,
  DE_PlateType_TRAILER_PLATE = 15,
  DE_PlateType_COACH_CAR_PLATE = 16,
  DE_PlateType_COACH_MOTORCYCLE_PLATE = 17,
  DE_PlateType_TEMPORARY_ENTRY_PLATE = 20,
  DE_PlateType_TEMPORARY_ENTRY_MOTORCYCLE_PLATE = 21,
  DE_PlateType_TEMPORARY_DRIVING_PLATE = 22,
  DE_PlateType_POLICE_CAR_PLATE = 23,
  DE_PlateType_POLICE_MOTORCYCLE_PLATE = 24,
  DE_PlateType_ORIGINAL_AGRICULTURAL_MACHINERY_PLATE = 25,
  DE_PlateType_HONGKONG_PLATE = 26,
  DE_PlateType_MACAU_PLATE = 27,
  DE_PlateType_ARMED_POLICE_PLATE = 31,
  DE_PlateType_ARMY_PLATE = 32,
  DE_PlateType_NO_NUMBER_PLATE = 41,
  DE_PlateType_FAKE_PLATE = 42,
  DE_PlateType_MISAPPROPRIATION_PLATE = 43,
  DE_PlateType_UNRECOGNIZED_PLATE = 44,
  DE_PlateType_LARGE_NEW_ENERGY_YELLOW_GREEN_PLATE = 51,
  DE_PlateType_SMALL_NEW_ENERGY_GREEN_PLATE = 52,
  DE_PlateType_OTHER_PLATE = 99,
  DE_PlateType_MIN = DE_PlateType_UNKNOWN_PLATE,
  DE_PlateType_MAX = DE_PlateType_OTHER_PLATE
};

inline const DE_PlateType (&EnumValuesDE_PlateType())[35] {
  static const DE_PlateType values[] = {
    DE_PlateType_UNKNOWN_PLATE,
    DE_PlateType_LARGE_CAR_PLATE,
    DE_PlateType_SMALL_CAR_PLATE,
    DE_PlateType_EMBASSY_CAR_PLATE,
    DE_PlateType_CONSULATE_CAR_PLATE,
    DE_PlateType_OVERSEAS_CAR_PLATE,
    DE_PlateType_FOREIGN_CAR_PLATE,
    DE_PlateType_ORDINARY_MOTORCYCLE_PLATE,
    DE_PlateType_MOPED_PLATE,
    DE_PlateType_EMBASSY_MOTORCYCLE_PLATE,
    DE_PlateType_CONSULATE_MOTORCYCLE_PLATE,
    DE_PlateType_OVERSEAS_MOTORCYCLE_PLATE,
    DE_PlateType_FOREIGN_MOTORCYCLE_PLATE,
    DE_PlateType_LOW_SPEED_PLATE,
    DE_PlateType_TRACTOR_PLATE,
    DE_PlateType_TRAILER_PLATE,
    DE_PlateType_COACH_CAR_PLATE,
    DE_PlateType_COACH_MOTORCYCLE_PLATE,
    DE_PlateType_TEMPORARY_ENTRY_PLATE,
    DE_PlateType_TEMPORARY_ENTRY_MOTORCYCLE_PLATE,
    DE_PlateType_TEMPORARY_DRIVING_PLATE,
    DE_PlateType_POLICE_CAR_PLATE,
    DE_PlateType_POLICE_MOTORCYCLE_PLATE,
    DE_PlateType_ORIGINAL_AGRICULTURAL_MACHINERY_PLATE,
    DE_PlateType_HONGKONG_PLATE,
    DE_PlateType_MACAU_PLATE,
    DE_PlateType_ARMED_POLICE_PLATE,
    DE_PlateType_ARMY_PLATE,
    DE_PlateType_NO_NUMBER_PLATE,
    DE_PlateType_FAKE_PLATE,
    DE_PlateType_MISAPPROPRIATION_PLATE,
    DE_PlateType_UNRECOGNIZED_PLATE,
    DE_PlateType_LARGE_NEW_ENERGY_YELLOW_GREEN_PLATE,
    DE_PlateType_SMALL_NEW_ENERGY_GREEN_PLATE,
    DE_PlateType_OTHER_PLATE
  };
  return values;
}

inline const char * const *EnumNamesDE_PlateType() {
  static const char * const names[101] = {
    "UNKNOWN_PLATE",
    "LARGE_CAR_PLATE",
    "SMALL_CAR_PLATE",
    "EMBASSY_CAR_PLATE",
    "CONSULATE_CAR_PLATE",
    "OVERSEAS_CAR_PLATE",
    "FOREIGN_CAR_PLATE",
    "ORDINARY_MOTORCYCLE_PLATE",
    "MOPED_PLATE",
    "EMBASSY_MOTORCYCLE_PLATE",
    "CONSULATE_MOTORCYCLE_PLATE",
    "OVERSEAS_MOTORCYCLE_PLATE",
    "FOREIGN_MOTORCYCLE_PLATE",
    "LOW_SPEED_PLATE",
    "TRACTOR_PLATE",
    "TRAILER_PLATE",
    "COACH_CAR_PLATE",
    "COACH_MOTORCYCLE_PLATE",
    "",
    "",
    "TEMPORARY_ENTRY_PLATE",
    "TEMPORARY_ENTRY_MOTORCYCLE_PLATE",
    "TEMPORARY_DRIVING_PLATE",
    "POLICE_CAR_PLATE",
    "POLICE_MOTORCYCLE_PLATE",
    "ORIGINAL_AGRICULTURAL_MACHINERY_PLATE",
    "HONGKONG_PLATE",
    "MACAU_PLATE",
    "",
    "",
    "",
    "ARMED_POLICE_PLATE",
    "ARMY_PLATE",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "NO_NUMBER_PLATE",
    "FAKE_PLATE",
    "MISAPPROPRIATION_PLATE",
    "UNRECOGNIZED_PLATE",
    "",
    "",
    "",
    "",
    "",
    "",
    "LARGE_NEW_ENERGY_YELLOW_GREEN_PLATE",
    "SMALL_NEW_ENERGY_GREEN_PLATE",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "OTHER_PLATE",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_PlateType(DE_PlateType e) {
  if (::flatbuffers::IsOutRange(e, DE_PlateType_UNKNOWN_PLATE, DE_PlateType_OTHER_PLATE)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_PlateType()[index];
}

struct DF_VehicleCharEx FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_VehicleCharExBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_COLOR = 4,
    VT_TYPECLASSIFICATIONCONFIDENCE = 6,
    VT_DRIVERBEHAVIOR = 8,
    VT_LANECHANGINGAIM = 10,
    VT_PLATE = 12,
    VT_PLATE_COLOR = 14,
    VT_PLATE_TYPE = 16
  };
  const ::flatbuffers::String *color() const {
    return GetPointer<const ::flatbuffers::String *>(VT_COLOR);
  }
  uint8_t typeClassificationConfidence() const {
    return GetField<uint8_t>(VT_TYPECLASSIFICATIONCONFIDENCE, 100);
  }
  const ::flatbuffers::String *driverBehavior() const {
    return GetPointer<const ::flatbuffers::String *>(VT_DRIVERBEHAVIOR);
  }
  uint8_t laneChangingAim() const {
    return GetField<uint8_t>(VT_LANECHANGINGAIM, 0);
  }
  const ::flatbuffers::String *plate() const {
    return GetPointer<const ::flatbuffers::String *>(VT_PLATE);
  }
  MECData::DE_PlateColor plate_color() const {
    return static_cast<MECData::DE_PlateColor>(GetField<uint8_t>(VT_PLATE_COLOR, 0));
  }
  MECData::DE_PlateType plate_type() const {
    return static_cast<MECData::DE_PlateType>(GetField<uint8_t>(VT_PLATE_TYPE, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_COLOR) &&
           verifier.VerifyString(color()) &&
           VerifyField<uint8_t>(verifier, VT_TYPECLASSIFICATIONCONFIDENCE, 1) &&
           VerifyOffset(verifier, VT_DRIVERBEHAVIOR) &&
           verifier.VerifyString(driverBehavior()) &&
           VerifyField<uint8_t>(verifier, VT_LANECHANGINGAIM, 1) &&
           VerifyOffset(verifier, VT_PLATE) &&
           verifier.VerifyString(plate()) &&
           VerifyField<uint8_t>(verifier, VT_PLATE_COLOR, 1) &&
           VerifyField<uint8_t>(verifier, VT_PLATE_TYPE, 1) &&
           verifier.EndTable();
  }
};

struct DF_VehicleCharExBuilder {
  typedef DF_VehicleCharEx Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_color(::flatbuffers::Offset<::flatbuffers::String> color) {
    fbb_.AddOffset(DF_VehicleCharEx::VT_COLOR, color);
  }
  void add_typeClassificationConfidence(uint8_t typeClassificationConfidence) {
    fbb_.AddElement<uint8_t>(DF_VehicleCharEx::VT_TYPECLASSIFICATIONCONFIDENCE, typeClassificationConfidence, 100);
  }
  void add_driverBehavior(::flatbuffers::Offset<::flatbuffers::String> driverBehavior) {
    fbb_.AddOffset(DF_VehicleCharEx::VT_DRIVERBEHAVIOR, driverBehavior);
  }
  void add_laneChangingAim(uint8_t laneChangingAim) {
    fbb_.AddElement<uint8_t>(DF_VehicleCharEx::VT_LANECHANGINGAIM, laneChangingAim, 0);
  }
  void add_plate(::flatbuffers::Offset<::flatbuffers::String> plate) {
    fbb_.AddOffset(DF_VehicleCharEx::VT_PLATE, plate);
  }
  void add_plate_color(MECData::DE_PlateColor plate_color) {
    fbb_.AddElement<uint8_t>(DF_VehicleCharEx::VT_PLATE_COLOR, static_cast<uint8_t>(plate_color), 0);
  }
  void add_plate_type(MECData::DE_PlateType plate_type) {
    fbb_.AddElement<uint8_t>(DF_VehicleCharEx::VT_PLATE_TYPE, static_cast<uint8_t>(plate_type), 0);
  }
  explicit DF_VehicleCharExBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_VehicleCharEx> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_VehicleCharEx>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_VehicleCharEx> CreateDF_VehicleCharEx(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> color = 0,
    uint8_t typeClassificationConfidence = 100,
    ::flatbuffers::Offset<::flatbuffers::String> driverBehavior = 0,
    uint8_t laneChangingAim = 0,
    ::flatbuffers::Offset<::flatbuffers::String> plate = 0,
    MECData::DE_PlateColor plate_color = MECData::DE_PlateColor_UNKNOWN_PLATE_COLOR,
    MECData::DE_PlateType plate_type = MECData::DE_PlateType_UNKNOWN_PLATE) {
  DF_VehicleCharExBuilder builder_(_fbb);
  builder_.add_plate(plate);
  builder_.add_driverBehavior(driverBehavior);
  builder_.add_color(color);
  builder_.add_plate_type(plate_type);
  builder_.add_plate_color(plate_color);
  builder_.add_laneChangingAim(laneChangingAim);
  builder_.add_typeClassificationConfidence(typeClassificationConfidence);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_VehicleCharEx> CreateDF_VehicleCharExDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *color = nullptr,
    uint8_t typeClassificationConfidence = 100,
    const char *driverBehavior = nullptr,
    uint8_t laneChangingAim = 0,
    const char *plate = nullptr,
    MECData::DE_PlateColor plate_color = MECData::DE_PlateColor_UNKNOWN_PLATE_COLOR,
    MECData::DE_PlateType plate_type = MECData::DE_PlateType_UNKNOWN_PLATE) {
  auto color__ = color ? _fbb.CreateString(color) : 0;
  auto driverBehavior__ = driverBehavior ? _fbb.CreateString(driverBehavior) : 0;
  auto plate__ = plate ? _fbb.CreateString(plate) : 0;
  return MECData::CreateDF_VehicleCharEx(
      _fbb,
      color__,
      typeClassificationConfidence,
      driverBehavior__,
      laneChangingAim,
      plate__,
      plate_color,
      plate_type);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_VEHICLECHAREX_MECDATA_H_
