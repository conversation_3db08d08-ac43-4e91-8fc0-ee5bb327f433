// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_SOURCETYPE_MECDATA_H_
#define FLATBUFFERS_GENERATED_SOURCETYPE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_SourceType : uint8_t {
  DE_SourceType_unknown = 0,
  DE_SourceType_selfinfo = 1,
  DE_SourceType_v2x = 2,
  DE_SourceType_video = 3,
  DE_SourceType_microwaveRadar = 4,
  DE_SourceType_loop = 5,
  DE_SourceType_lidar = 6,
  DE_SourceType_integrated = 7,
  DE_SourceType_MIN = DE_SourceType_unknown,
  DE_SourceType_MAX = DE_SourceType_integrated
};

inline const DE_SourceType (&EnumValuesDE_SourceType())[8] {
  static const DE_SourceType values[] = {
    DE_SourceType_unknown,
    DE_SourceType_selfinfo,
    DE_SourceType_v2x,
    DE_SourceType_video,
    DE_SourceType_microwaveRadar,
    DE_SourceType_loop,
    DE_SourceType_lidar,
    DE_SourceType_integrated
  };
  return values;
}

inline const char * const *EnumNamesDE_SourceType() {
  static const char * const names[9] = {
    "unknown",
    "selfinfo",
    "v2x",
    "video",
    "microwaveRadar",
    "loop",
    "lidar",
    "integrated",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_SourceType(DE_SourceType e) {
  if (::flatbuffers::IsOutRange(e, DE_SourceType_unknown, DE_SourceType_integrated)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_SourceType()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_SOURCETYPE_MECDATA_H_
