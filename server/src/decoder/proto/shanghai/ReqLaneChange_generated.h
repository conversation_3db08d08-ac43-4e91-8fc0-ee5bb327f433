// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_REQLANECHANGE_MECDATA_H_
#define FLATBUFFERS_GENERATED_REQLANECHANGE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "NodeReferenceID_generated.h"

namespace MECData {

struct DF_ReqLaneChange;
struct DF_ReqLaneChangeBuilder;

struct DF_ReqLaneChange FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ReqLaneChangeBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_UPSTREAMNODE = 4,
    VT_DOWNSTREAMNODE = 6,
    VT_TARGETLANE = 8,
    VT_TARGET_LANE_EXT_ID = 10
  };
  const MECData::DF_NodeReferenceID *upstreamNode() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_UPSTREAMNODE);
  }
  const MECData::DF_NodeReferenceID *downstreamNode() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_DOWNSTREAMNODE);
  }
  uint8_t targetLane() const {
    return GetField<uint8_t>(VT_TARGETLANE, 255);
  }
  const ::flatbuffers::String *target_lane_ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_TARGET_LANE_EXT_ID);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_UPSTREAMNODE) &&
           verifier.VerifyTable(upstreamNode()) &&
           VerifyOffsetRequired(verifier, VT_DOWNSTREAMNODE) &&
           verifier.VerifyTable(downstreamNode()) &&
           VerifyField<uint8_t>(verifier, VT_TARGETLANE, 1) &&
           VerifyOffset(verifier, VT_TARGET_LANE_EXT_ID) &&
           verifier.VerifyString(target_lane_ext_id()) &&
           verifier.EndTable();
  }
};

struct DF_ReqLaneChangeBuilder {
  typedef DF_ReqLaneChange Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_upstreamNode(::flatbuffers::Offset<MECData::DF_NodeReferenceID> upstreamNode) {
    fbb_.AddOffset(DF_ReqLaneChange::VT_UPSTREAMNODE, upstreamNode);
  }
  void add_downstreamNode(::flatbuffers::Offset<MECData::DF_NodeReferenceID> downstreamNode) {
    fbb_.AddOffset(DF_ReqLaneChange::VT_DOWNSTREAMNODE, downstreamNode);
  }
  void add_targetLane(uint8_t targetLane) {
    fbb_.AddElement<uint8_t>(DF_ReqLaneChange::VT_TARGETLANE, targetLane, 255);
  }
  void add_target_lane_ext_id(::flatbuffers::Offset<::flatbuffers::String> target_lane_ext_id) {
    fbb_.AddOffset(DF_ReqLaneChange::VT_TARGET_LANE_EXT_ID, target_lane_ext_id);
  }
  explicit DF_ReqLaneChangeBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ReqLaneChange> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ReqLaneChange>(end);
    fbb_.Required(o, DF_ReqLaneChange::VT_UPSTREAMNODE);
    fbb_.Required(o, DF_ReqLaneChange::VT_DOWNSTREAMNODE);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ReqLaneChange> CreateDF_ReqLaneChange(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> upstreamNode = 0,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> downstreamNode = 0,
    uint8_t targetLane = 255,
    ::flatbuffers::Offset<::flatbuffers::String> target_lane_ext_id = 0) {
  DF_ReqLaneChangeBuilder builder_(_fbb);
  builder_.add_target_lane_ext_id(target_lane_ext_id);
  builder_.add_downstreamNode(downstreamNode);
  builder_.add_upstreamNode(upstreamNode);
  builder_.add_targetLane(targetLane);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ReqLaneChange> CreateDF_ReqLaneChangeDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> upstreamNode = 0,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> downstreamNode = 0,
    uint8_t targetLane = 255,
    const char *target_lane_ext_id = nullptr) {
  auto target_lane_ext_id__ = target_lane_ext_id ? _fbb.CreateString(target_lane_ext_id) : 0;
  return MECData::CreateDF_ReqLaneChange(
      _fbb,
      upstreamNode,
      downstreamNode,
      targetLane,
      target_lane_ext_id__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_REQLANECHANGE_MECDATA_H_
