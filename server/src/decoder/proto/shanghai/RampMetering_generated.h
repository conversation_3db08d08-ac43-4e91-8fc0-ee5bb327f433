// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_RAMPMETERING_MECDATA_H_
#define FLATBUFFERS_GENERATED_RAMPMETERING_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "RMScheme_generated.h"
#include "SignalPhase_generated.h"

namespace MECData {

struct MSG_RampMetering;
struct MSG_RampMeteringBuilder;

struct MSG_RampMetering FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_RampMeteringBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MOY = 4,
    VT_SECMARK = 6,
    VT_SCHEMES = 8,
    VT_PHASES = 10,
    VT_MSG_ID = 12
  };
  uint32_t moy() const {
    return GetField<uint32_t>(VT_MOY, 4294967295);
  }
  uint16_t secMark() const {
    return GetField<uint16_t>(VT_SECMARK, 65535);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RMScheme>> *schemes() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RMScheme>> *>(VT_SCHEMES);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SignalPhase>> *phases() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SignalPhase>> *>(VT_PHASES);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_MOY, 4) &&
           VerifyField<uint16_t>(verifier, VT_SECMARK, 2) &&
           VerifyOffsetRequired(verifier, VT_SCHEMES) &&
           verifier.VerifyVector(schemes()) &&
           verifier.VerifyVectorOfTables(schemes()) &&
           VerifyOffsetRequired(verifier, VT_PHASES) &&
           verifier.VerifyVector(phases()) &&
           verifier.VerifyVectorOfTables(phases()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct MSG_RampMeteringBuilder {
  typedef MSG_RampMetering Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_moy(uint32_t moy) {
    fbb_.AddElement<uint32_t>(MSG_RampMetering::VT_MOY, moy, 4294967295);
  }
  void add_secMark(uint16_t secMark) {
    fbb_.AddElement<uint16_t>(MSG_RampMetering::VT_SECMARK, secMark, 65535);
  }
  void add_schemes(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RMScheme>>> schemes) {
    fbb_.AddOffset(MSG_RampMetering::VT_SCHEMES, schemes);
  }
  void add_phases(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SignalPhase>>> phases) {
    fbb_.AddOffset(MSG_RampMetering::VT_PHASES, phases);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_RampMetering::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_RampMeteringBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_RampMetering> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_RampMetering>(end);
    fbb_.Required(o, MSG_RampMetering::VT_SCHEMES);
    fbb_.Required(o, MSG_RampMetering::VT_PHASES);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_RampMetering> CreateMSG_RampMetering(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t moy = 4294967295,
    uint16_t secMark = 65535,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RMScheme>>> schemes = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SignalPhase>>> phases = 0,
    int64_t msg_id = 0) {
  MSG_RampMeteringBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_phases(phases);
  builder_.add_schemes(schemes);
  builder_.add_moy(moy);
  builder_.add_secMark(secMark);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_RampMetering> CreateMSG_RampMeteringDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t moy = 4294967295,
    uint16_t secMark = 65535,
    const std::vector<::flatbuffers::Offset<MECData::DF_RMScheme>> *schemes = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_SignalPhase>> *phases = nullptr,
    int64_t msg_id = 0) {
  auto schemes__ = schemes ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_RMScheme>>(*schemes) : 0;
  auto phases__ = phases ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_SignalPhase>>(*phases) : 0;
  return MECData::CreateMSG_RampMetering(
      _fbb,
      moy,
      secMark,
      schemes__,
      phases__,
      msg_id);
}

inline const MECData::MSG_RampMetering *GetMSG_RampMetering(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_RampMetering>(buf);
}

inline const MECData::MSG_RampMetering *GetSizePrefixedMSG_RampMetering(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_RampMetering>(buf);
}

inline bool VerifyMSG_RampMeteringBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_RampMetering>(nullptr);
}

inline bool VerifySizePrefixedMSG_RampMeteringBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_RampMetering>(nullptr);
}

inline void FinishMSG_RampMeteringBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_RampMetering> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_RampMeteringBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_RampMetering> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_RAMPMETERING_MECDATA_H_
