// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_REGULATORYSPEEDLIMIT_MECDATA_H_
#define FLATBUFFERS_GENERATED_REGULATORYSPEEDLIMIT_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "SpeedLimitType_generated.h"

namespace MECData {

struct DF_RegulatorySpeedLimit;
struct DF_RegulatorySpeedLimitBuilder;

struct DF_RegulatorySpeedLimit FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_RegulatorySpeedLimitBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TYPE = 4,
    VT_SPEED = 6
  };
  MECData::DE_SpeedLimitType type() const {
    return static_cast<MECData::DE_SpeedLimitType>(GetField<int8_t>(VT_TYPE, 0));
  }
  uint16_t speed() const {
    return GetField<uint16_t>(VT_SPEED, 65535);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_TYPE, 1) &&
           VerifyField<uint16_t>(verifier, VT_SPEED, 2) &&
           verifier.EndTable();
  }
};

struct DF_RegulatorySpeedLimitBuilder {
  typedef DF_RegulatorySpeedLimit Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_type(MECData::DE_SpeedLimitType type) {
    fbb_.AddElement<int8_t>(DF_RegulatorySpeedLimit::VT_TYPE, static_cast<int8_t>(type), 0);
  }
  void add_speed(uint16_t speed) {
    fbb_.AddElement<uint16_t>(DF_RegulatorySpeedLimit::VT_SPEED, speed, 65535);
  }
  explicit DF_RegulatorySpeedLimitBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_RegulatorySpeedLimit> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_RegulatorySpeedLimit>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_RegulatorySpeedLimit> CreateDF_RegulatorySpeedLimit(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    MECData::DE_SpeedLimitType type = MECData::DE_SpeedLimitType_unknown,
    uint16_t speed = 65535) {
  DF_RegulatorySpeedLimitBuilder builder_(_fbb);
  builder_.add_speed(speed);
  builder_.add_type(type);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_REGULATORYSPEEDLIMIT_MECDATA_H_
