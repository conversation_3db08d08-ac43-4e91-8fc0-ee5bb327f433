// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_ZBRSUSER_MECDATA_H_
#define FLATBUFFERS_GENERATED_ZBRSUSER_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_ZBRSUserConfig;
struct DF_ZBRSUserConfigBuilder;

enum DE_UserPermission : int8_t {
  DE_UserPermission_NONE = 0,
  DE_UserPermission_READ_ONLY = 1,
  DE_UserPermission_READ_WRITE = 2,
  DE_UserPermission_MIN = DE_UserPermission_NONE,
  DE_UserPermission_MAX = DE_UserPermission_READ_WRITE
};

inline const DE_UserPermission (&EnumValuesDE_UserPermission())[3] {
  static const DE_UserPermission values[] = {
    DE_UserPermission_NONE,
    DE_UserPermission_READ_ONLY,
    DE_UserPermission_READ_WRITE
  };
  return values;
}

inline const char * const *EnumNamesDE_UserPermission() {
  static const char * const names[4] = {
    "NONE",
    "READ_ONLY",
    "READ_WRITE",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_UserPermission(DE_UserPermission e) {
  if (::flatbuffers::IsOutRange(e, DE_UserPermission_NONE, DE_UserPermission_READ_WRITE)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_UserPermission()[index];
}

enum DE_UserType : int8_t {
  DE_UserType_ADMIN = 0,
  DE_UserType_DEVELOPER = 1,
  DE_UserType_OPERATOR = 2,
  DE_UserType_MIN = DE_UserType_ADMIN,
  DE_UserType_MAX = DE_UserType_OPERATOR
};

inline const DE_UserType (&EnumValuesDE_UserType())[3] {
  static const DE_UserType values[] = {
    DE_UserType_ADMIN,
    DE_UserType_DEVELOPER,
    DE_UserType_OPERATOR
  };
  return values;
}

inline const char * const *EnumNamesDE_UserType() {
  static const char * const names[4] = {
    "ADMIN",
    "DEVELOPER",
    "OPERATOR",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_UserType(DE_UserType e) {
  if (::flatbuffers::IsOutRange(e, DE_UserType_ADMIN, DE_UserType_OPERATOR)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_UserType()[index];
}

struct DF_ZBRSUserConfig FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ZBRSUserConfigBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_USERNAME = 4,
    VT_USER_TYPE = 6,
    VT_USER_PERMISSION = 8
  };
  const ::flatbuffers::String *username() const {
    return GetPointer<const ::flatbuffers::String *>(VT_USERNAME);
  }
  MECData::DE_UserType user_type() const {
    return static_cast<MECData::DE_UserType>(GetField<int8_t>(VT_USER_TYPE, 2));
  }
  MECData::DE_UserPermission user_permission() const {
    return static_cast<MECData::DE_UserPermission>(GetField<int8_t>(VT_USER_PERMISSION, 1));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_USERNAME) &&
           verifier.VerifyString(username()) &&
           VerifyField<int8_t>(verifier, VT_USER_TYPE, 1) &&
           VerifyField<int8_t>(verifier, VT_USER_PERMISSION, 1) &&
           verifier.EndTable();
  }
};

struct DF_ZBRSUserConfigBuilder {
  typedef DF_ZBRSUserConfig Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_username(::flatbuffers::Offset<::flatbuffers::String> username) {
    fbb_.AddOffset(DF_ZBRSUserConfig::VT_USERNAME, username);
  }
  void add_user_type(MECData::DE_UserType user_type) {
    fbb_.AddElement<int8_t>(DF_ZBRSUserConfig::VT_USER_TYPE, static_cast<int8_t>(user_type), 2);
  }
  void add_user_permission(MECData::DE_UserPermission user_permission) {
    fbb_.AddElement<int8_t>(DF_ZBRSUserConfig::VT_USER_PERMISSION, static_cast<int8_t>(user_permission), 1);
  }
  explicit DF_ZBRSUserConfigBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ZBRSUserConfig> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ZBRSUserConfig>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ZBRSUserConfig> CreateDF_ZBRSUserConfig(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> username = 0,
    MECData::DE_UserType user_type = MECData::DE_UserType_OPERATOR,
    MECData::DE_UserPermission user_permission = MECData::DE_UserPermission_READ_ONLY) {
  DF_ZBRSUserConfigBuilder builder_(_fbb);
  builder_.add_username(username);
  builder_.add_user_permission(user_permission);
  builder_.add_user_type(user_type);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ZBRSUserConfig> CreateDF_ZBRSUserConfigDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *username = nullptr,
    MECData::DE_UserType user_type = MECData::DE_UserType_OPERATOR,
    MECData::DE_UserPermission user_permission = MECData::DE_UserPermission_READ_ONLY) {
  auto username__ = username ? _fbb.CreateString(username) : 0;
  return MECData::CreateDF_ZBRSUserConfig(
      _fbb,
      username__,
      user_type,
      user_permission);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_ZBRSUSER_MECDATA_H_
