// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_TIMECONFIDENCE_MECDATA_H_
#define FLATBUFFERS_GENERATED_TIMECONFIDENCE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_TimeConfidence : int8_t {
  DE_TimeConfidence_unavailable = 0,
  DE_TimeConfidence_time100000 = 1,
  DE_TimeConfidence_time050000 = 2,
  DE_TimeConfidence_time020000 = 3,
  DE_TimeConfidence_time010000 = 4,
  DE_TimeConfidence_time002000 = 5,
  DE_TimeConfidence_time001000 = 6,
  DE_TimeConfidence_time000500 = 7,
  DE_TimeConfidence_time000200 = 8,
  DE_TimeConfidence_time000100 = 9,
  DE_TimeConfidence_time000050 = 10,
  DE_TimeConfidence_time000020 = 11,
  DE_TimeConfidence_time000010 = 12,
  DE_TimeConfidence_time000005 = 13,
  DE_TimeConfidence_time000002 = 14,
  DE_TimeConfidence_time000001 = 15,
  DE_TimeConfidence_time0000005 = 16,
  DE_TimeConfidence_time0000002 = 17,
  DE_TimeConfidence_time0000001 = 18,
  DE_TimeConfidence_time00000005 = 19,
  DE_TimeConfidence_time00000002 = 20,
  DE_TimeConfidence_time00000001 = 21,
  DE_TimeConfidence_time000000005 = 22,
  DE_TimeConfidence_time000000002 = 23,
  DE_TimeConfidence_time000000001 = 24,
  DE_TimeConfidence_time0000000005 = 25,
  DE_TimeConfidence_time0000000002 = 26,
  DE_TimeConfidence_time0000000001 = 27,
  DE_TimeConfidence_time00000000005 = 28,
  DE_TimeConfidence_time00000000002 = 29,
  DE_TimeConfidence_time00000000001 = 30,
  DE_TimeConfidence_time000000000005 = 31,
  DE_TimeConfidence_time000000000002 = 32,
  DE_TimeConfidence_time000000000001 = 33,
  DE_TimeConfidence_time0000000000005 = 34,
  DE_TimeConfidence_time0000000000002 = 35,
  DE_TimeConfidence_time0000000000001 = 36,
  DE_TimeConfidence_time00000000000005 = 37,
  DE_TimeConfidence_time00000000000002 = 38,
  DE_TimeConfidence_time00000000000001 = 39,
  DE_TimeConfidence_MIN = DE_TimeConfidence_unavailable,
  DE_TimeConfidence_MAX = DE_TimeConfidence_time00000000000001
};

inline const DE_TimeConfidence (&EnumValuesDE_TimeConfidence())[40] {
  static const DE_TimeConfidence values[] = {
    DE_TimeConfidence_unavailable,
    DE_TimeConfidence_time100000,
    DE_TimeConfidence_time050000,
    DE_TimeConfidence_time020000,
    DE_TimeConfidence_time010000,
    DE_TimeConfidence_time002000,
    DE_TimeConfidence_time001000,
    DE_TimeConfidence_time000500,
    DE_TimeConfidence_time000200,
    DE_TimeConfidence_time000100,
    DE_TimeConfidence_time000050,
    DE_TimeConfidence_time000020,
    DE_TimeConfidence_time000010,
    DE_TimeConfidence_time000005,
    DE_TimeConfidence_time000002,
    DE_TimeConfidence_time000001,
    DE_TimeConfidence_time0000005,
    DE_TimeConfidence_time0000002,
    DE_TimeConfidence_time0000001,
    DE_TimeConfidence_time00000005,
    DE_TimeConfidence_time00000002,
    DE_TimeConfidence_time00000001,
    DE_TimeConfidence_time000000005,
    DE_TimeConfidence_time000000002,
    DE_TimeConfidence_time000000001,
    DE_TimeConfidence_time0000000005,
    DE_TimeConfidence_time0000000002,
    DE_TimeConfidence_time0000000001,
    DE_TimeConfidence_time00000000005,
    DE_TimeConfidence_time00000000002,
    DE_TimeConfidence_time00000000001,
    DE_TimeConfidence_time000000000005,
    DE_TimeConfidence_time000000000002,
    DE_TimeConfidence_time000000000001,
    DE_TimeConfidence_time0000000000005,
    DE_TimeConfidence_time0000000000002,
    DE_TimeConfidence_time0000000000001,
    DE_TimeConfidence_time00000000000005,
    DE_TimeConfidence_time00000000000002,
    DE_TimeConfidence_time00000000000001
  };
  return values;
}

inline const char * const *EnumNamesDE_TimeConfidence() {
  static const char * const names[41] = {
    "unavailable",
    "time100000",
    "time050000",
    "time020000",
    "time010000",
    "time002000",
    "time001000",
    "time000500",
    "time000200",
    "time000100",
    "time000050",
    "time000020",
    "time000010",
    "time000005",
    "time000002",
    "time000001",
    "time0000005",
    "time0000002",
    "time0000001",
    "time00000005",
    "time00000002",
    "time00000001",
    "time000000005",
    "time000000002",
    "time000000001",
    "time0000000005",
    "time0000000002",
    "time0000000001",
    "time00000000005",
    "time00000000002",
    "time00000000001",
    "time000000000005",
    "time000000000002",
    "time000000000001",
    "time0000000000005",
    "time0000000000002",
    "time0000000000001",
    "time00000000000005",
    "time00000000000002",
    "time00000000000001",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_TimeConfidence(DE_TimeConfidence e) {
  if (::flatbuffers::IsOutRange(e, DE_TimeConfidence_unavailable, DE_TimeConfidence_time00000000000001)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_TimeConfidence()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_TIMECONFIDENCE_MECDATA_H_
