// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_SPEEDLIMITTYPE_MECDATA_H_
#define FLATBUFFERS_GENERATED_SPEEDLIMITTYPE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_SpeedLimitType : int8_t {
  DE_SpeedLimitType_unknown = 0,
  DE_SpeedLimitType_maxSpeedInSchoolZone = 1,
  DE_SpeedLimitType_maxSpeedInSchoolZoneWhenChildrenArePresent = 2,
  DE_SpeedLimitType_maxSpeedInConstructionZone = 3,
  DE_SpeedLimitType_vehicleMinSpeed = 4,
  DE_SpeedLimitType_vehicleMaxSpeed = 5,
  DE_SpeedLimitType_vehicleNightMaxSpeed = 6,
  DE_SpeedLimitType_truckMinSpeed = 7,
  DE_SpeedLimitType_truckMaxSpeed = 8,
  DE_SpeedLimitType_truckNightMaxSpeed = 9,
  DE_SpeedLimitType_vehiclesWithTrailersMinSpeed = 10,
  DE_SpeedLimitType_vehiclesWithTrailersMaxSpeed = 11,
  DE_SpeedLimitType_vehiclesWithTrailersNightMaxSpeed = 12,
  DE_SpeedLimitType_MIN = DE_SpeedLimitType_unknown,
  DE_SpeedLimitType_MAX = DE_SpeedLimitType_vehiclesWithTrailersNightMaxSpeed
};

inline const DE_SpeedLimitType (&EnumValuesDE_SpeedLimitType())[13] {
  static const DE_SpeedLimitType values[] = {
    DE_SpeedLimitType_unknown,
    DE_SpeedLimitType_maxSpeedInSchoolZone,
    DE_SpeedLimitType_maxSpeedInSchoolZoneWhenChildrenArePresent,
    DE_SpeedLimitType_maxSpeedInConstructionZone,
    DE_SpeedLimitType_vehicleMinSpeed,
    DE_SpeedLimitType_vehicleMaxSpeed,
    DE_SpeedLimitType_vehicleNightMaxSpeed,
    DE_SpeedLimitType_truckMinSpeed,
    DE_SpeedLimitType_truckMaxSpeed,
    DE_SpeedLimitType_truckNightMaxSpeed,
    DE_SpeedLimitType_vehiclesWithTrailersMinSpeed,
    DE_SpeedLimitType_vehiclesWithTrailersMaxSpeed,
    DE_SpeedLimitType_vehiclesWithTrailersNightMaxSpeed
  };
  return values;
}

inline const char * const *EnumNamesDE_SpeedLimitType() {
  static const char * const names[14] = {
    "unknown",
    "maxSpeedInSchoolZone",
    "maxSpeedInSchoolZoneWhenChildrenArePresent",
    "maxSpeedInConstructionZone",
    "vehicleMinSpeed",
    "vehicleMaxSpeed",
    "vehicleNightMaxSpeed",
    "truckMinSpeed",
    "truckMaxSpeed",
    "truckNightMaxSpeed",
    "vehiclesWithTrailersMinSpeed",
    "vehiclesWithTrailersMaxSpeed",
    "vehiclesWithTrailersNightMaxSpeed",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_SpeedLimitType(DE_SpeedLimitType e) {
  if (::flatbuffers::IsOutRange(e, DE_SpeedLimitType_unknown, DE_SpeedLimitType_vehiclesWithTrailersNightMaxSpeed)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_SpeedLimitType()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_SPEEDLIMITTYPE_MECDATA_H_
