// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_POSITIONOFFSETLLV_MECDATA_H_
#define FLATBUFFERS_GENERATED_POSITIONOFFSETLLV_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "PositionOffsetLL_generated.h"
#include "VerticalOffset_generated.h"

namespace MECData {

struct DF_PositionOffsetLLV;
struct DF_PositionOffsetLLVBuilder;

struct DF_PositionOffsetLLV FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_PositionOffsetLLVBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_OFFSETLL_TYPE = 4,
    VT_OFFSETLL = 6,
    VT_OFFSETV_TYPE = 8,
    VT_OFFSETV = 10
  };
  MECData::DF_PositionOffsetLL offsetLL_type() const {
    return static_cast<MECData::DF_PositionOffsetLL>(GetField<uint8_t>(VT_OFFSETLL_TYPE, 0));
  }
  const void *offsetLL() const {
    return GetPointer<const void *>(VT_OFFSETLL);
  }
  template<typename T> const T *offsetLL_as() const;
  const MECData::DF_PositionLL24B *offsetLL_as_DF_PositionLL24B() const {
    return offsetLL_type() == MECData::DF_PositionOffsetLL_DF_PositionLL24B ? static_cast<const MECData::DF_PositionLL24B *>(offsetLL()) : nullptr;
  }
  const MECData::DF_PositionLL28B *offsetLL_as_DF_PositionLL28B() const {
    return offsetLL_type() == MECData::DF_PositionOffsetLL_DF_PositionLL28B ? static_cast<const MECData::DF_PositionLL28B *>(offsetLL()) : nullptr;
  }
  const MECData::DF_PositionLL32B *offsetLL_as_DF_PositionLL32B() const {
    return offsetLL_type() == MECData::DF_PositionOffsetLL_DF_PositionLL32B ? static_cast<const MECData::DF_PositionLL32B *>(offsetLL()) : nullptr;
  }
  const MECData::DF_PositionLL36B *offsetLL_as_DF_PositionLL36B() const {
    return offsetLL_type() == MECData::DF_PositionOffsetLL_DF_PositionLL36B ? static_cast<const MECData::DF_PositionLL36B *>(offsetLL()) : nullptr;
  }
  const MECData::DF_PositionLL44B *offsetLL_as_DF_PositionLL44B() const {
    return offsetLL_type() == MECData::DF_PositionOffsetLL_DF_PositionLL44B ? static_cast<const MECData::DF_PositionLL44B *>(offsetLL()) : nullptr;
  }
  const MECData::DF_PositionLL48B *offsetLL_as_DF_PositionLL48B() const {
    return offsetLL_type() == MECData::DF_PositionOffsetLL_DF_PositionLL48B ? static_cast<const MECData::DF_PositionLL48B *>(offsetLL()) : nullptr;
  }
  const MECData::DF_PositionLLmD64b *offsetLL_as_DF_PositionLLmD64b() const {
    return offsetLL_type() == MECData::DF_PositionOffsetLL_DF_PositionLLmD64b ? static_cast<const MECData::DF_PositionLLmD64b *>(offsetLL()) : nullptr;
  }
  MECData::DF_VerticalOffset offsetV_type() const {
    return static_cast<MECData::DF_VerticalOffset>(GetField<uint8_t>(VT_OFFSETV_TYPE, 0));
  }
  const void *offsetV() const {
    return GetPointer<const void *>(VT_OFFSETV);
  }
  template<typename T> const T *offsetV_as() const;
  const MECData::DF_VertOffsetB07 *offsetV_as_DF_VertOffsetB07() const {
    return offsetV_type() == MECData::DF_VerticalOffset_DF_VertOffsetB07 ? static_cast<const MECData::DF_VertOffsetB07 *>(offsetV()) : nullptr;
  }
  const MECData::DF_VertOffsetB08 *offsetV_as_DF_VertOffsetB08() const {
    return offsetV_type() == MECData::DF_VerticalOffset_DF_VertOffsetB08 ? static_cast<const MECData::DF_VertOffsetB08 *>(offsetV()) : nullptr;
  }
  const MECData::DF_VertOffsetB09 *offsetV_as_DF_VertOffsetB09() const {
    return offsetV_type() == MECData::DF_VerticalOffset_DF_VertOffsetB09 ? static_cast<const MECData::DF_VertOffsetB09 *>(offsetV()) : nullptr;
  }
  const MECData::DF_VertOffsetB10 *offsetV_as_DF_VertOffsetB10() const {
    return offsetV_type() == MECData::DF_VerticalOffset_DF_VertOffsetB10 ? static_cast<const MECData::DF_VertOffsetB10 *>(offsetV()) : nullptr;
  }
  const MECData::DF_VertOffsetB11 *offsetV_as_DF_VertOffsetB11() const {
    return offsetV_type() == MECData::DF_VerticalOffset_DF_VertOffsetB11 ? static_cast<const MECData::DF_VertOffsetB11 *>(offsetV()) : nullptr;
  }
  const MECData::DF_VertOffsetB12 *offsetV_as_DF_VertOffsetB12() const {
    return offsetV_type() == MECData::DF_VerticalOffset_DF_VertOffsetB12 ? static_cast<const MECData::DF_VertOffsetB12 *>(offsetV()) : nullptr;
  }
  const MECData::DF_Elevation *offsetV_as_DF_Elevation() const {
    return offsetV_type() == MECData::DF_VerticalOffset_DF_Elevation ? static_cast<const MECData::DF_Elevation *>(offsetV()) : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_OFFSETLL_TYPE, 1) &&
           VerifyOffsetRequired(verifier, VT_OFFSETLL) &&
           VerifyDF_PositionOffsetLL(verifier, offsetLL(), offsetLL_type()) &&
           VerifyField<uint8_t>(verifier, VT_OFFSETV_TYPE, 1) &&
           VerifyOffset(verifier, VT_OFFSETV) &&
           VerifyDF_VerticalOffset(verifier, offsetV(), offsetV_type()) &&
           verifier.EndTable();
  }
};

template<> inline const MECData::DF_PositionLL24B *DF_PositionOffsetLLV::offsetLL_as<MECData::DF_PositionLL24B>() const {
  return offsetLL_as_DF_PositionLL24B();
}

template<> inline const MECData::DF_PositionLL28B *DF_PositionOffsetLLV::offsetLL_as<MECData::DF_PositionLL28B>() const {
  return offsetLL_as_DF_PositionLL28B();
}

template<> inline const MECData::DF_PositionLL32B *DF_PositionOffsetLLV::offsetLL_as<MECData::DF_PositionLL32B>() const {
  return offsetLL_as_DF_PositionLL32B();
}

template<> inline const MECData::DF_PositionLL36B *DF_PositionOffsetLLV::offsetLL_as<MECData::DF_PositionLL36B>() const {
  return offsetLL_as_DF_PositionLL36B();
}

template<> inline const MECData::DF_PositionLL44B *DF_PositionOffsetLLV::offsetLL_as<MECData::DF_PositionLL44B>() const {
  return offsetLL_as_DF_PositionLL44B();
}

template<> inline const MECData::DF_PositionLL48B *DF_PositionOffsetLLV::offsetLL_as<MECData::DF_PositionLL48B>() const {
  return offsetLL_as_DF_PositionLL48B();
}

template<> inline const MECData::DF_PositionLLmD64b *DF_PositionOffsetLLV::offsetLL_as<MECData::DF_PositionLLmD64b>() const {
  return offsetLL_as_DF_PositionLLmD64b();
}

template<> inline const MECData::DF_VertOffsetB07 *DF_PositionOffsetLLV::offsetV_as<MECData::DF_VertOffsetB07>() const {
  return offsetV_as_DF_VertOffsetB07();
}

template<> inline const MECData::DF_VertOffsetB08 *DF_PositionOffsetLLV::offsetV_as<MECData::DF_VertOffsetB08>() const {
  return offsetV_as_DF_VertOffsetB08();
}

template<> inline const MECData::DF_VertOffsetB09 *DF_PositionOffsetLLV::offsetV_as<MECData::DF_VertOffsetB09>() const {
  return offsetV_as_DF_VertOffsetB09();
}

template<> inline const MECData::DF_VertOffsetB10 *DF_PositionOffsetLLV::offsetV_as<MECData::DF_VertOffsetB10>() const {
  return offsetV_as_DF_VertOffsetB10();
}

template<> inline const MECData::DF_VertOffsetB11 *DF_PositionOffsetLLV::offsetV_as<MECData::DF_VertOffsetB11>() const {
  return offsetV_as_DF_VertOffsetB11();
}

template<> inline const MECData::DF_VertOffsetB12 *DF_PositionOffsetLLV::offsetV_as<MECData::DF_VertOffsetB12>() const {
  return offsetV_as_DF_VertOffsetB12();
}

template<> inline const MECData::DF_Elevation *DF_PositionOffsetLLV::offsetV_as<MECData::DF_Elevation>() const {
  return offsetV_as_DF_Elevation();
}

struct DF_PositionOffsetLLVBuilder {
  typedef DF_PositionOffsetLLV Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_offsetLL_type(MECData::DF_PositionOffsetLL offsetLL_type) {
    fbb_.AddElement<uint8_t>(DF_PositionOffsetLLV::VT_OFFSETLL_TYPE, static_cast<uint8_t>(offsetLL_type), 0);
  }
  void add_offsetLL(::flatbuffers::Offset<void> offsetLL) {
    fbb_.AddOffset(DF_PositionOffsetLLV::VT_OFFSETLL, offsetLL);
  }
  void add_offsetV_type(MECData::DF_VerticalOffset offsetV_type) {
    fbb_.AddElement<uint8_t>(DF_PositionOffsetLLV::VT_OFFSETV_TYPE, static_cast<uint8_t>(offsetV_type), 0);
  }
  void add_offsetV(::flatbuffers::Offset<void> offsetV) {
    fbb_.AddOffset(DF_PositionOffsetLLV::VT_OFFSETV, offsetV);
  }
  explicit DF_PositionOffsetLLVBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_PositionOffsetLLV> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_PositionOffsetLLV>(end);
    fbb_.Required(o, DF_PositionOffsetLLV::VT_OFFSETLL);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_PositionOffsetLLV> CreateDF_PositionOffsetLLV(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    MECData::DF_PositionOffsetLL offsetLL_type = MECData::DF_PositionOffsetLL_NONE,
    ::flatbuffers::Offset<void> offsetLL = 0,
    MECData::DF_VerticalOffset offsetV_type = MECData::DF_VerticalOffset_NONE,
    ::flatbuffers::Offset<void> offsetV = 0) {
  DF_PositionOffsetLLVBuilder builder_(_fbb);
  builder_.add_offsetV(offsetV);
  builder_.add_offsetLL(offsetLL);
  builder_.add_offsetV_type(offsetV_type);
  builder_.add_offsetLL_type(offsetLL_type);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_POSITIONOFFSETLLV_MECDATA_H_
