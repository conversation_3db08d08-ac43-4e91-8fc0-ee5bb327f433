// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_REQINFO_MECDATA_H_
#define FLATBUFFERS_GENERATED_REQINFO_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "ReqClearTheWay_generated.h"
#include "ReqLaneChange_generated.h"
#include "ReqParkingArea_generated.h"
#include "ReqSensorSharing_generated.h"
#include "ReqSignalPriority_generated.h"

namespace MECData {

enum DF_ReqInfo : uint8_t {
  DF_ReqInfo_NONE = 0,
  DF_ReqInfo_DF_ReqLaneChange = 1,
  DF_ReqInfo_DF_ReqClearTheWay = 2,
  DF_ReqInfo_DF_ReqSignalPriority = 3,
  DF_ReqInfo_DF_ReqSensorSharing = 4,
  DF_ReqInfo_DF_ReqParkingArea = 5,
  DF_ReqInfo_MIN = DF_ReqInfo_NONE,
  DF_ReqInfo_MAX = DF_ReqInfo_DF_ReqParkingArea
};

inline const DF_ReqInfo (&EnumValuesDF_ReqInfo())[6] {
  static const DF_ReqInfo values[] = {
    DF_ReqInfo_NONE,
    DF_ReqInfo_DF_ReqLaneChange,
    DF_ReqInfo_DF_ReqClearTheWay,
    DF_ReqInfo_DF_ReqSignalPriority,
    DF_ReqInfo_DF_ReqSensorSharing,
    DF_ReqInfo_DF_ReqParkingArea
  };
  return values;
}

inline const char * const *EnumNamesDF_ReqInfo() {
  static const char * const names[7] = {
    "NONE",
    "DF_ReqLaneChange",
    "DF_ReqClearTheWay",
    "DF_ReqSignalPriority",
    "DF_ReqSensorSharing",
    "DF_ReqParkingArea",
    nullptr
  };
  return names;
}

inline const char *EnumNameDF_ReqInfo(DF_ReqInfo e) {
  if (::flatbuffers::IsOutRange(e, DF_ReqInfo_NONE, DF_ReqInfo_DF_ReqParkingArea)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDF_ReqInfo()[index];
}

template<typename T> struct DF_ReqInfoTraits {
  static const DF_ReqInfo enum_value = DF_ReqInfo_NONE;
};

template<> struct DF_ReqInfoTraits<MECData::DF_ReqLaneChange> {
  static const DF_ReqInfo enum_value = DF_ReqInfo_DF_ReqLaneChange;
};

template<> struct DF_ReqInfoTraits<MECData::DF_ReqClearTheWay> {
  static const DF_ReqInfo enum_value = DF_ReqInfo_DF_ReqClearTheWay;
};

template<> struct DF_ReqInfoTraits<MECData::DF_ReqSignalPriority> {
  static const DF_ReqInfo enum_value = DF_ReqInfo_DF_ReqSignalPriority;
};

template<> struct DF_ReqInfoTraits<MECData::DF_ReqSensorSharing> {
  static const DF_ReqInfo enum_value = DF_ReqInfo_DF_ReqSensorSharing;
};

template<> struct DF_ReqInfoTraits<MECData::DF_ReqParkingArea> {
  static const DF_ReqInfo enum_value = DF_ReqInfo_DF_ReqParkingArea;
};

bool VerifyDF_ReqInfo(::flatbuffers::Verifier &verifier, const void *obj, DF_ReqInfo type);
bool VerifyDF_ReqInfoVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types);

inline bool VerifyDF_ReqInfo(::flatbuffers::Verifier &verifier, const void *obj, DF_ReqInfo type) {
  switch (type) {
    case DF_ReqInfo_NONE: {
      return true;
    }
    case DF_ReqInfo_DF_ReqLaneChange: {
      auto ptr = reinterpret_cast<const MECData::DF_ReqLaneChange *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_ReqInfo_DF_ReqClearTheWay: {
      auto ptr = reinterpret_cast<const MECData::DF_ReqClearTheWay *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_ReqInfo_DF_ReqSignalPriority: {
      auto ptr = reinterpret_cast<const MECData::DF_ReqSignalPriority *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_ReqInfo_DF_ReqSensorSharing: {
      auto ptr = reinterpret_cast<const MECData::DF_ReqSensorSharing *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_ReqInfo_DF_ReqParkingArea: {
      auto ptr = reinterpret_cast<const MECData::DF_ReqParkingArea *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return true;
  }
}

inline bool VerifyDF_ReqInfoVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (::flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyDF_ReqInfo(
        verifier,  values->Get(i), types->GetEnum<DF_ReqInfo>(i))) {
      return false;
    }
  }
  return true;
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_REQINFO_MECDATA_H_
