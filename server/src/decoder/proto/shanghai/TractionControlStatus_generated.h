// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_TRACTIONCONTROLSTATUS_MECDATA_H_
#define FLATBUFFERS_GENERATED_TRACTIONCONTROLSTATUS_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_TractionControlStatus : int8_t {
  DE_TractionControlStatus_unavailable = 0,
  DE_TractionControlStatus_off = 1,
  DE_TractionControlStatus_on = 2,
  DE_TractionControlStatus_engaged = 3,
  DE_TractionControlStatus_MIN = DE_TractionControlStatus_unavailable,
  DE_TractionControlStatus_MAX = DE_TractionControlStatus_engaged
};

inline const DE_TractionControlStatus (&EnumValuesDE_TractionControlStatus())[4] {
  static const DE_TractionControlStatus values[] = {
    DE_TractionControlStatus_unavailable,
    DE_TractionControlStatus_off,
    DE_TractionControlStatus_on,
    DE_TractionControlStatus_engaged
  };
  return values;
}

inline const char * const *EnumNamesDE_TractionControlStatus() {
  static const char * const names[5] = {
    "unavailable",
    "off",
    "on",
    "engaged",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_TractionControlStatus(DE_TractionControlStatus e) {
  if (::flatbuffers::IsOutRange(e, DE_TractionControlStatus_unavailable, DE_TractionControlStatus_engaged)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_TractionControlStatus()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_TRACTIONCONTROLSTATUS_MECDATA_H_
