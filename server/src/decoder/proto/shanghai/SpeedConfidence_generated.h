// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_SPEEDCONFIDENCE_MECDATA_H_
#define FLATBUFFERS_GENERATED_SPEEDCONFIDENCE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_SpeedConfidence : int8_t {
  DE_SpeedConfidence_unavailable = 0,
  DE_SpeedConfidence_prec100ms = 1,
  DE_SpeedConfidence_prec10ms = 2,
  DE_SpeedConfidence_prec5ms = 3,
  DE_SpeedConfidence_prec1ms = 4,
  DE_SpeedConfidence_prec01ms = 5,
  DE_SpeedConfidence_prec005ms = 6,
  DE_SpeedConfidence_prec001ms = 7,
  DE_SpeedConfidence_MIN = DE_SpeedConfidence_unavailable,
  DE_SpeedConfidence_MAX = DE_SpeedConfidence_prec001ms
};

inline const DE_SpeedConfidence (&EnumValuesDE_SpeedConfidence())[8] {
  static const DE_SpeedConfidence values[] = {
    DE_SpeedConfidence_unavailable,
    DE_SpeedConfidence_prec100ms,
    DE_SpeedConfidence_prec10ms,
    DE_SpeedConfidence_prec5ms,
    DE_SpeedConfidence_prec1ms,
    DE_SpeedConfidence_prec01ms,
    DE_SpeedConfidence_prec005ms,
    DE_SpeedConfidence_prec001ms
  };
  return values;
}

inline const char * const *EnumNamesDE_SpeedConfidence() {
  static const char * const names[9] = {
    "unavailable",
    "prec100ms",
    "prec10ms",
    "prec5ms",
    "prec1ms",
    "prec01ms",
    "prec005ms",
    "prec001ms",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_SpeedConfidence(DE_SpeedConfidence e) {
  if (::flatbuffers::IsOutRange(e, DE_SpeedConfidence_unavailable, DE_SpeedConfidence_prec001ms)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_SpeedConfidence()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_SPEEDCONFIDENCE_MECDATA_H_
