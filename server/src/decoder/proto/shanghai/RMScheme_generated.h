// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_RMSCHEME_MECDATA_H_
#define FLATBUFFERS_GENERATED_RMSCHEME_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_RMScheme;
struct DF_RMSchemeBuilder;

struct DF_RMScheme FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_RMSchemeBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ID = 4,
    VT_RAMPID = 6,
    VT_SIGNALHEADID = 8,
    VT_CYCLE = 10,
    VT_GREEN = 12,
    VT_RED = 14
  };
  int32_t id() const {
    return GetField<int32_t>(VT_ID, 0);
  }
  int32_t rampId() const {
    return GetField<int32_t>(VT_RAMPID, 0);
  }
  int32_t signalHeadId() const {
    return GetField<int32_t>(VT_SIGNALHEADID, 0);
  }
  uint16_t cycle() const {
    return GetField<uint16_t>(VT_CYCLE, 0);
  }
  uint16_t green() const {
    return GetField<uint16_t>(VT_GREEN, 0);
  }
  uint16_t red() const {
    return GetField<uint16_t>(VT_RED, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_ID, 4) &&
           VerifyField<int32_t>(verifier, VT_RAMPID, 4) &&
           VerifyField<int32_t>(verifier, VT_SIGNALHEADID, 4) &&
           VerifyField<uint16_t>(verifier, VT_CYCLE, 2) &&
           VerifyField<uint16_t>(verifier, VT_GREEN, 2) &&
           VerifyField<uint16_t>(verifier, VT_RED, 2) &&
           verifier.EndTable();
  }
};

struct DF_RMSchemeBuilder {
  typedef DF_RMScheme Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_id(int32_t id) {
    fbb_.AddElement<int32_t>(DF_RMScheme::VT_ID, id, 0);
  }
  void add_rampId(int32_t rampId) {
    fbb_.AddElement<int32_t>(DF_RMScheme::VT_RAMPID, rampId, 0);
  }
  void add_signalHeadId(int32_t signalHeadId) {
    fbb_.AddElement<int32_t>(DF_RMScheme::VT_SIGNALHEADID, signalHeadId, 0);
  }
  void add_cycle(uint16_t cycle) {
    fbb_.AddElement<uint16_t>(DF_RMScheme::VT_CYCLE, cycle, 0);
  }
  void add_green(uint16_t green) {
    fbb_.AddElement<uint16_t>(DF_RMScheme::VT_GREEN, green, 0);
  }
  void add_red(uint16_t red) {
    fbb_.AddElement<uint16_t>(DF_RMScheme::VT_RED, red, 0);
  }
  explicit DF_RMSchemeBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_RMScheme> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_RMScheme>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_RMScheme> CreateDF_RMScheme(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t id = 0,
    int32_t rampId = 0,
    int32_t signalHeadId = 0,
    uint16_t cycle = 0,
    uint16_t green = 0,
    uint16_t red = 0) {
  DF_RMSchemeBuilder builder_(_fbb);
  builder_.add_signalHeadId(signalHeadId);
  builder_.add_rampId(rampId);
  builder_.add_id(id);
  builder_.add_red(red);
  builder_.add_green(green);
  builder_.add_cycle(cycle);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_RMSCHEME_MECDATA_H_
