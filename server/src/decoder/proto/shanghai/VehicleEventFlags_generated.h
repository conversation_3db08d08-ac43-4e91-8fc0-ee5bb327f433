// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_VEHICLEEVENTFLAGS_MECDATA_H_
#define FLATBUFFERS_GENERATED_VEHICLEEVENTFLAGS_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_VehicleEventFlags;
struct DF_VehicleEventFlagsBuilder;

struct DF_VehicleEventFlags FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_VehicleEventFlagsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_EVENTS = 4
  };
  int16_t events() const {
    return GetField<int16_t>(VT_EVENTS, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int16_t>(verifier, VT_EVENTS, 2) &&
           verifier.EndTable();
  }
};

struct DF_VehicleEventFlagsBuilder {
  typedef DF_VehicleEventFlags Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_events(int16_t events) {
    fbb_.AddElement<int16_t>(DF_VehicleEventFlags::VT_EVENTS, events, 0);
  }
  explicit DF_VehicleEventFlagsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_VehicleEventFlags> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_VehicleEventFlags>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_VehicleEventFlags> CreateDF_VehicleEventFlags(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int16_t events = 0) {
  DF_VehicleEventFlagsBuilder builder_(_fbb);
  builder_.add_events(events);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_VEHICLEEVENTFLAGS_MECDATA_H_
