// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_SPEEDLANEAREA_MECDATA_H_
#define FLATBUFFERS_GENERATED_SPEEDLANEAREA_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct MSG_SpeedLaneArea;
struct MSG_SpeedLaneAreaBuilder;

struct MSG_SpeedLaneArea FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_SpeedLaneAreaBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_DETECTORID = 4,
    VT_STARTDETECTAERAID = 6,
    VT_ENDDETECTAERAID = 8,
    VT_MOY = 10,
    VT_SECMARK = 12,
    VT_CYCLE = 14,
    VT_SPEED = 16
  };
  uint16_t detectorId() const {
    return GetField<uint16_t>(VT_DETECTORID, 0);
  }
  int32_t startDetectAeraId() const {
    return GetField<int32_t>(VT_STARTDETECTAERAID, 0);
  }
  int32_t endDetectAeraId() const {
    return GetField<int32_t>(VT_ENDDETECTAERAID, 0);
  }
  uint32_t moy() const {
    return GetField<uint32_t>(VT_MOY, 4294967295);
  }
  uint16_t secMark() const {
    return GetField<uint16_t>(VT_SECMARK, 65535);
  }
  uint16_t cycle() const {
    return GetField<uint16_t>(VT_CYCLE, 65535);
  }
  uint16_t speed() const {
    return GetField<uint16_t>(VT_SPEED, 65535);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_DETECTORID, 2) &&
           VerifyField<int32_t>(verifier, VT_STARTDETECTAERAID, 4) &&
           VerifyField<int32_t>(verifier, VT_ENDDETECTAERAID, 4) &&
           VerifyField<uint32_t>(verifier, VT_MOY, 4) &&
           VerifyField<uint16_t>(verifier, VT_SECMARK, 2) &&
           VerifyField<uint16_t>(verifier, VT_CYCLE, 2) &&
           VerifyField<uint16_t>(verifier, VT_SPEED, 2) &&
           verifier.EndTable();
  }
};

struct MSG_SpeedLaneAreaBuilder {
  typedef MSG_SpeedLaneArea Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_detectorId(uint16_t detectorId) {
    fbb_.AddElement<uint16_t>(MSG_SpeedLaneArea::VT_DETECTORID, detectorId, 0);
  }
  void add_startDetectAeraId(int32_t startDetectAeraId) {
    fbb_.AddElement<int32_t>(MSG_SpeedLaneArea::VT_STARTDETECTAERAID, startDetectAeraId, 0);
  }
  void add_endDetectAeraId(int32_t endDetectAeraId) {
    fbb_.AddElement<int32_t>(MSG_SpeedLaneArea::VT_ENDDETECTAERAID, endDetectAeraId, 0);
  }
  void add_moy(uint32_t moy) {
    fbb_.AddElement<uint32_t>(MSG_SpeedLaneArea::VT_MOY, moy, 4294967295);
  }
  void add_secMark(uint16_t secMark) {
    fbb_.AddElement<uint16_t>(MSG_SpeedLaneArea::VT_SECMARK, secMark, 65535);
  }
  void add_cycle(uint16_t cycle) {
    fbb_.AddElement<uint16_t>(MSG_SpeedLaneArea::VT_CYCLE, cycle, 65535);
  }
  void add_speed(uint16_t speed) {
    fbb_.AddElement<uint16_t>(MSG_SpeedLaneArea::VT_SPEED, speed, 65535);
  }
  explicit MSG_SpeedLaneAreaBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_SpeedLaneArea> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_SpeedLaneArea>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_SpeedLaneArea> CreateMSG_SpeedLaneArea(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t detectorId = 0,
    int32_t startDetectAeraId = 0,
    int32_t endDetectAeraId = 0,
    uint32_t moy = 4294967295,
    uint16_t secMark = 65535,
    uint16_t cycle = 65535,
    uint16_t speed = 65535) {
  MSG_SpeedLaneAreaBuilder builder_(_fbb);
  builder_.add_moy(moy);
  builder_.add_endDetectAeraId(endDetectAeraId);
  builder_.add_startDetectAeraId(startDetectAeraId);
  builder_.add_speed(speed);
  builder_.add_cycle(cycle);
  builder_.add_secMark(secMark);
  builder_.add_detectorId(detectorId);
  return builder_.Finish();
}

inline const MECData::MSG_SpeedLaneArea *GetMSG_SpeedLaneArea(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_SpeedLaneArea>(buf);
}

inline const MECData::MSG_SpeedLaneArea *GetSizePrefixedMSG_SpeedLaneArea(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_SpeedLaneArea>(buf);
}

inline bool VerifyMSG_SpeedLaneAreaBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_SpeedLaneArea>(nullptr);
}

inline bool VerifySizePrefixedMSG_SpeedLaneAreaBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_SpeedLaneArea>(nullptr);
}

inline void FinishMSG_SpeedLaneAreaBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_SpeedLaneArea> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_SpeedLaneAreaBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_SpeedLaneArea> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_SPEEDLANEAREA_MECDATA_H_
