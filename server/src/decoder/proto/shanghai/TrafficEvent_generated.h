// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_TRAFFICEVENT_MECDATA_H_
#define FLATBUFFERS_GENERATED_TRAFFICEVENT_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "DebugTimeRecords_generated.h"
#include "EventAction_generated.h"
#include "EventOperationType_generated.h"
#include "EventSource_generated.h"
#include "Image_generated.h"
#include "ObstacleData_generated.h"
#include "ParticipantID_generated.h"
#include "Position3D_generated.h"
#include "RSITimeDetails_generated.h"
#include "ReferenceLink_generated.h"
#include "ReferencePath_generated.h"

namespace MECData {

struct MSG_TrafficEvent;
struct MSG_TrafficEventBuilder;

struct MSG_TrafficEvent FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_TrafficEventBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_EVENTID = 4,
    VT_EVENTTYPE = 6,
    VT_SOURCE = 8,
    VT_EVENTPOS = 10,
    VT_EVENTRADIUS = 12,
    VT_DESCRIPTION = 14,
    VT_TIMEDETAILS = 16,
    VT_PRIORITY = 18,
    VT_REFERENCEPATHS = 20,
    VT_PATHRADIUS = 22,
    VT_REFERENCELINKS = 24,
    VT_EVENTCONFIDENCE = 26,
    VT_TIME_EXT = 28,
    VT_TIME_RECORDS = 30,
    VT_PARTICIPANT_IDS = 32,
    VT_OBSTACLES = 34,
    VT_IMAGES = 36,
    VT_DEVICES = 38,
    VT_EVENT_SOURCE = 40,
    VT_MSG_ID = 42,
    VT_SESSION_ID = 44,
    VT_ACTION = 46,
    VT_OPERATION_TYPE = 48
  };
  uint32_t eventId() const {
    return GetField<uint32_t>(VT_EVENTID, 4294967295);
  }
  uint16_t eventType() const {
    return GetField<uint16_t>(VT_EVENTTYPE, 65535);
  }
  uint8_t source() const {
    return GetField<uint8_t>(VT_SOURCE, 255);
  }
  const MECData::DF_Position3D *eventPos() const {
    return GetPointer<const MECData::DF_Position3D *>(VT_EVENTPOS);
  }
  uint16_t eventRadius() const {
    return GetField<uint16_t>(VT_EVENTRADIUS, 0);
  }
  const ::flatbuffers::String *description() const {
    return GetPointer<const ::flatbuffers::String *>(VT_DESCRIPTION);
  }
  const MECData::DF_RSITimeDetails *timeDetails() const {
    return GetPointer<const MECData::DF_RSITimeDetails *>(VT_TIMEDETAILS);
  }
  int32_t priority() const {
    return GetField<int32_t>(VT_PRIORITY, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferencePath>> *referencePaths() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferencePath>> *>(VT_REFERENCEPATHS);
  }
  uint16_t pathRadius() const {
    return GetField<uint16_t>(VT_PATHRADIUS, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferenceLink>> *referenceLinks() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferenceLink>> *>(VT_REFERENCELINKS);
  }
  uint8_t eventConfidence() const {
    return GetField<uint8_t>(VT_EVENTCONFIDENCE, 0);
  }
  uint64_t time_ext() const {
    return GetField<uint64_t>(VT_TIME_EXT, 0);
  }
  const MECData::DF_DebugTimeRecords *time_records() const {
    return GetPointer<const MECData::DF_DebugTimeRecords *>(VT_TIME_RECORDS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ParticipantID>> *participant_ids() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ParticipantID>> *>(VT_PARTICIPANT_IDS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ObstacleData>> *obstacles() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ObstacleData>> *>(VT_OBSTACLES);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Image>> *images() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Image>> *>(VT_IMAGES);
  }
  const ::flatbuffers::Vector<uint16_t> *devices() const {
    return GetPointer<const ::flatbuffers::Vector<uint16_t> *>(VT_DEVICES);
  }
  const MECData::DF_EventSource *event_source() const {
    return GetPointer<const MECData::DF_EventSource *>(VT_EVENT_SOURCE);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  int64_t session_id() const {
    return GetField<int64_t>(VT_SESSION_ID, 0);
  }
  MECData::DE_EventAction action() const {
    return static_cast<MECData::DE_EventAction>(GetField<uint8_t>(VT_ACTION, 0));
  }
  MECData::DE_EventOperationType operation_type() const {
    return static_cast<MECData::DE_EventOperationType>(GetField<uint8_t>(VT_OPERATION_TYPE, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_EVENTID, 4) &&
           VerifyField<uint16_t>(verifier, VT_EVENTTYPE, 2) &&
           VerifyField<uint8_t>(verifier, VT_SOURCE, 1) &&
           VerifyOffsetRequired(verifier, VT_EVENTPOS) &&
           verifier.VerifyTable(eventPos()) &&
           VerifyField<uint16_t>(verifier, VT_EVENTRADIUS, 2) &&
           VerifyOffset(verifier, VT_DESCRIPTION) &&
           verifier.VerifyString(description()) &&
           VerifyOffset(verifier, VT_TIMEDETAILS) &&
           verifier.VerifyTable(timeDetails()) &&
           VerifyField<int32_t>(verifier, VT_PRIORITY, 4) &&
           VerifyOffset(verifier, VT_REFERENCEPATHS) &&
           verifier.VerifyVector(referencePaths()) &&
           verifier.VerifyVectorOfTables(referencePaths()) &&
           VerifyField<uint16_t>(verifier, VT_PATHRADIUS, 2) &&
           VerifyOffset(verifier, VT_REFERENCELINKS) &&
           verifier.VerifyVector(referenceLinks()) &&
           verifier.VerifyVectorOfTables(referenceLinks()) &&
           VerifyField<uint8_t>(verifier, VT_EVENTCONFIDENCE, 1) &&
           VerifyField<uint64_t>(verifier, VT_TIME_EXT, 8) &&
           VerifyOffset(verifier, VT_TIME_RECORDS) &&
           verifier.VerifyTable(time_records()) &&
           VerifyOffset(verifier, VT_PARTICIPANT_IDS) &&
           verifier.VerifyVector(participant_ids()) &&
           verifier.VerifyVectorOfTables(participant_ids()) &&
           VerifyOffset(verifier, VT_OBSTACLES) &&
           verifier.VerifyVector(obstacles()) &&
           verifier.VerifyVectorOfTables(obstacles()) &&
           VerifyOffset(verifier, VT_IMAGES) &&
           verifier.VerifyVector(images()) &&
           verifier.VerifyVectorOfTables(images()) &&
           VerifyOffset(verifier, VT_DEVICES) &&
           verifier.VerifyVector(devices()) &&
           VerifyOffset(verifier, VT_EVENT_SOURCE) &&
           verifier.VerifyTable(event_source()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           VerifyField<int64_t>(verifier, VT_SESSION_ID, 8) &&
           VerifyField<uint8_t>(verifier, VT_ACTION, 1) &&
           VerifyField<uint8_t>(verifier, VT_OPERATION_TYPE, 1) &&
           verifier.EndTable();
  }
};

struct MSG_TrafficEventBuilder {
  typedef MSG_TrafficEvent Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_eventId(uint32_t eventId) {
    fbb_.AddElement<uint32_t>(MSG_TrafficEvent::VT_EVENTID, eventId, 4294967295);
  }
  void add_eventType(uint16_t eventType) {
    fbb_.AddElement<uint16_t>(MSG_TrafficEvent::VT_EVENTTYPE, eventType, 65535);
  }
  void add_source(uint8_t source) {
    fbb_.AddElement<uint8_t>(MSG_TrafficEvent::VT_SOURCE, source, 255);
  }
  void add_eventPos(::flatbuffers::Offset<MECData::DF_Position3D> eventPos) {
    fbb_.AddOffset(MSG_TrafficEvent::VT_EVENTPOS, eventPos);
  }
  void add_eventRadius(uint16_t eventRadius) {
    fbb_.AddElement<uint16_t>(MSG_TrafficEvent::VT_EVENTRADIUS, eventRadius, 0);
  }
  void add_description(::flatbuffers::Offset<::flatbuffers::String> description) {
    fbb_.AddOffset(MSG_TrafficEvent::VT_DESCRIPTION, description);
  }
  void add_timeDetails(::flatbuffers::Offset<MECData::DF_RSITimeDetails> timeDetails) {
    fbb_.AddOffset(MSG_TrafficEvent::VT_TIMEDETAILS, timeDetails);
  }
  void add_priority(int32_t priority) {
    fbb_.AddElement<int32_t>(MSG_TrafficEvent::VT_PRIORITY, priority, 0);
  }
  void add_referencePaths(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferencePath>>> referencePaths) {
    fbb_.AddOffset(MSG_TrafficEvent::VT_REFERENCEPATHS, referencePaths);
  }
  void add_pathRadius(uint16_t pathRadius) {
    fbb_.AddElement<uint16_t>(MSG_TrafficEvent::VT_PATHRADIUS, pathRadius, 0);
  }
  void add_referenceLinks(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferenceLink>>> referenceLinks) {
    fbb_.AddOffset(MSG_TrafficEvent::VT_REFERENCELINKS, referenceLinks);
  }
  void add_eventConfidence(uint8_t eventConfidence) {
    fbb_.AddElement<uint8_t>(MSG_TrafficEvent::VT_EVENTCONFIDENCE, eventConfidence, 0);
  }
  void add_time_ext(uint64_t time_ext) {
    fbb_.AddElement<uint64_t>(MSG_TrafficEvent::VT_TIME_EXT, time_ext, 0);
  }
  void add_time_records(::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records) {
    fbb_.AddOffset(MSG_TrafficEvent::VT_TIME_RECORDS, time_records);
  }
  void add_participant_ids(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ParticipantID>>> participant_ids) {
    fbb_.AddOffset(MSG_TrafficEvent::VT_PARTICIPANT_IDS, participant_ids);
  }
  void add_obstacles(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ObstacleData>>> obstacles) {
    fbb_.AddOffset(MSG_TrafficEvent::VT_OBSTACLES, obstacles);
  }
  void add_images(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Image>>> images) {
    fbb_.AddOffset(MSG_TrafficEvent::VT_IMAGES, images);
  }
  void add_devices(::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> devices) {
    fbb_.AddOffset(MSG_TrafficEvent::VT_DEVICES, devices);
  }
  void add_event_source(::flatbuffers::Offset<MECData::DF_EventSource> event_source) {
    fbb_.AddOffset(MSG_TrafficEvent::VT_EVENT_SOURCE, event_source);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_TrafficEvent::VT_MSG_ID, msg_id, 0);
  }
  void add_session_id(int64_t session_id) {
    fbb_.AddElement<int64_t>(MSG_TrafficEvent::VT_SESSION_ID, session_id, 0);
  }
  void add_action(MECData::DE_EventAction action) {
    fbb_.AddElement<uint8_t>(MSG_TrafficEvent::VT_ACTION, static_cast<uint8_t>(action), 0);
  }
  void add_operation_type(MECData::DE_EventOperationType operation_type) {
    fbb_.AddElement<uint8_t>(MSG_TrafficEvent::VT_OPERATION_TYPE, static_cast<uint8_t>(operation_type), 0);
  }
  explicit MSG_TrafficEventBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_TrafficEvent> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_TrafficEvent>(end);
    fbb_.Required(o, MSG_TrafficEvent::VT_EVENTPOS);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_TrafficEvent> CreateMSG_TrafficEvent(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t eventId = 4294967295,
    uint16_t eventType = 65535,
    uint8_t source = 255,
    ::flatbuffers::Offset<MECData::DF_Position3D> eventPos = 0,
    uint16_t eventRadius = 0,
    ::flatbuffers::Offset<::flatbuffers::String> description = 0,
    ::flatbuffers::Offset<MECData::DF_RSITimeDetails> timeDetails = 0,
    int32_t priority = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferencePath>>> referencePaths = 0,
    uint16_t pathRadius = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ReferenceLink>>> referenceLinks = 0,
    uint8_t eventConfidence = 0,
    uint64_t time_ext = 0,
    ::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ParticipantID>>> participant_ids = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ObstacleData>>> obstacles = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Image>>> images = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> devices = 0,
    ::flatbuffers::Offset<MECData::DF_EventSource> event_source = 0,
    int64_t msg_id = 0,
    int64_t session_id = 0,
    MECData::DE_EventAction action = MECData::DE_EventAction_OCCURRED,
    MECData::DE_EventOperationType operation_type = MECData::DE_EventOperationType_AUTO_FORWARD) {
  MSG_TrafficEventBuilder builder_(_fbb);
  builder_.add_session_id(session_id);
  builder_.add_msg_id(msg_id);
  builder_.add_time_ext(time_ext);
  builder_.add_event_source(event_source);
  builder_.add_devices(devices);
  builder_.add_images(images);
  builder_.add_obstacles(obstacles);
  builder_.add_participant_ids(participant_ids);
  builder_.add_time_records(time_records);
  builder_.add_referenceLinks(referenceLinks);
  builder_.add_referencePaths(referencePaths);
  builder_.add_priority(priority);
  builder_.add_timeDetails(timeDetails);
  builder_.add_description(description);
  builder_.add_eventPos(eventPos);
  builder_.add_eventId(eventId);
  builder_.add_pathRadius(pathRadius);
  builder_.add_eventRadius(eventRadius);
  builder_.add_eventType(eventType);
  builder_.add_operation_type(operation_type);
  builder_.add_action(action);
  builder_.add_eventConfidence(eventConfidence);
  builder_.add_source(source);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_TrafficEvent> CreateMSG_TrafficEventDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t eventId = 4294967295,
    uint16_t eventType = 65535,
    uint8_t source = 255,
    ::flatbuffers::Offset<MECData::DF_Position3D> eventPos = 0,
    uint16_t eventRadius = 0,
    const char *description = nullptr,
    ::flatbuffers::Offset<MECData::DF_RSITimeDetails> timeDetails = 0,
    int32_t priority = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_ReferencePath>> *referencePaths = nullptr,
    uint16_t pathRadius = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_ReferenceLink>> *referenceLinks = nullptr,
    uint8_t eventConfidence = 0,
    uint64_t time_ext = 0,
    ::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_ParticipantID>> *participant_ids = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_ObstacleData>> *obstacles = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_Image>> *images = nullptr,
    const std::vector<uint16_t> *devices = nullptr,
    ::flatbuffers::Offset<MECData::DF_EventSource> event_source = 0,
    int64_t msg_id = 0,
    int64_t session_id = 0,
    MECData::DE_EventAction action = MECData::DE_EventAction_OCCURRED,
    MECData::DE_EventOperationType operation_type = MECData::DE_EventOperationType_AUTO_FORWARD) {
  auto description__ = description ? _fbb.CreateString(description) : 0;
  auto referencePaths__ = referencePaths ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_ReferencePath>>(*referencePaths) : 0;
  auto referenceLinks__ = referenceLinks ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_ReferenceLink>>(*referenceLinks) : 0;
  auto participant_ids__ = participant_ids ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_ParticipantID>>(*participant_ids) : 0;
  auto obstacles__ = obstacles ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_ObstacleData>>(*obstacles) : 0;
  auto images__ = images ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_Image>>(*images) : 0;
  auto devices__ = devices ? _fbb.CreateVector<uint16_t>(*devices) : 0;
  return MECData::CreateMSG_TrafficEvent(
      _fbb,
      eventId,
      eventType,
      source,
      eventPos,
      eventRadius,
      description__,
      timeDetails,
      priority,
      referencePaths__,
      pathRadius,
      referenceLinks__,
      eventConfidence,
      time_ext,
      time_records,
      participant_ids__,
      obstacles__,
      images__,
      devices__,
      event_source,
      msg_id,
      session_id,
      action,
      operation_type);
}

inline const MECData::MSG_TrafficEvent *GetMSG_TrafficEvent(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_TrafficEvent>(buf);
}

inline const MECData::MSG_TrafficEvent *GetSizePrefixedMSG_TrafficEvent(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_TrafficEvent>(buf);
}

inline bool VerifyMSG_TrafficEventBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_TrafficEvent>(nullptr);
}

inline bool VerifySizePrefixedMSG_TrafficEventBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_TrafficEvent>(nullptr);
}

inline void FinishMSG_TrafficEventBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_TrafficEvent> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_TrafficEventBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_TrafficEvent> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_TRAFFICEVENT_MECDATA_H_
