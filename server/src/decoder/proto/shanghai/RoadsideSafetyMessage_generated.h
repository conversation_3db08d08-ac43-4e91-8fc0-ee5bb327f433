// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_ROADSIDESAFETYMESSAGE_MECDATA_H_
#define FLATBUFFERS_GENERATED_ROADSIDESAFETYMESSAGE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "DebugTimeRecords_generated.h"
#include "ParticipantData_generated.h"
#include "Position3D_generated.h"

namespace MECData {

struct MSG_RoadsideSafetyMessage;
struct MSG_RoadsideSafetyMessageBuilder;

struct MSG_RoadsideSafetyMessage FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_RoadsideSafetyMessageBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ID = 4,
    VT_REFPOS = 6,
    VT_PARTICIPANTS = 8,
    VT_TIME_RECORDS = 10,
    VT_MSG_ID = 12,
    VT_RSU_ID = 14
  };
  uint16_t id() const {
    return GetField<uint16_t>(VT_ID, 0);
  }
  const MECData::DF_Position3D *refPos() const {
    return GetPointer<const MECData::DF_Position3D *>(VT_REFPOS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ParticipantData>> *participants() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ParticipantData>> *>(VT_PARTICIPANTS);
  }
  const MECData::DF_DebugTimeRecords *time_records() const {
    return GetPointer<const MECData::DF_DebugTimeRecords *>(VT_TIME_RECORDS);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  const ::flatbuffers::Vector<uint8_t> *rsu_id() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_RSU_ID);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_ID, 2) &&
           VerifyOffsetRequired(verifier, VT_REFPOS) &&
           verifier.VerifyTable(refPos()) &&
           VerifyOffsetRequired(verifier, VT_PARTICIPANTS) &&
           verifier.VerifyVector(participants()) &&
           verifier.VerifyVectorOfTables(participants()) &&
           VerifyOffset(verifier, VT_TIME_RECORDS) &&
           verifier.VerifyTable(time_records()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           VerifyOffset(verifier, VT_RSU_ID) &&
           verifier.VerifyVector(rsu_id()) &&
           verifier.EndTable();
  }
};

struct MSG_RoadsideSafetyMessageBuilder {
  typedef MSG_RoadsideSafetyMessage Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_id(uint16_t id) {
    fbb_.AddElement<uint16_t>(MSG_RoadsideSafetyMessage::VT_ID, id, 0);
  }
  void add_refPos(::flatbuffers::Offset<MECData::DF_Position3D> refPos) {
    fbb_.AddOffset(MSG_RoadsideSafetyMessage::VT_REFPOS, refPos);
  }
  void add_participants(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ParticipantData>>> participants) {
    fbb_.AddOffset(MSG_RoadsideSafetyMessage::VT_PARTICIPANTS, participants);
  }
  void add_time_records(::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records) {
    fbb_.AddOffset(MSG_RoadsideSafetyMessage::VT_TIME_RECORDS, time_records);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_RoadsideSafetyMessage::VT_MSG_ID, msg_id, 0);
  }
  void add_rsu_id(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> rsu_id) {
    fbb_.AddOffset(MSG_RoadsideSafetyMessage::VT_RSU_ID, rsu_id);
  }
  explicit MSG_RoadsideSafetyMessageBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_RoadsideSafetyMessage> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_RoadsideSafetyMessage>(end);
    fbb_.Required(o, MSG_RoadsideSafetyMessage::VT_REFPOS);
    fbb_.Required(o, MSG_RoadsideSafetyMessage::VT_PARTICIPANTS);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_RoadsideSafetyMessage> CreateMSG_RoadsideSafetyMessage(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t id = 0,
    ::flatbuffers::Offset<MECData::DF_Position3D> refPos = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ParticipantData>>> participants = 0,
    ::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records = 0,
    int64_t msg_id = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> rsu_id = 0) {
  MSG_RoadsideSafetyMessageBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_rsu_id(rsu_id);
  builder_.add_time_records(time_records);
  builder_.add_participants(participants);
  builder_.add_refPos(refPos);
  builder_.add_id(id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_RoadsideSafetyMessage> CreateMSG_RoadsideSafetyMessageDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t id = 0,
    ::flatbuffers::Offset<MECData::DF_Position3D> refPos = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_ParticipantData>> *participants = nullptr,
    ::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records = 0,
    int64_t msg_id = 0,
    const std::vector<uint8_t> *rsu_id = nullptr) {
  auto participants__ = participants ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_ParticipantData>>(*participants) : 0;
  auto rsu_id__ = rsu_id ? _fbb.CreateVector<uint8_t>(*rsu_id) : 0;
  return MECData::CreateMSG_RoadsideSafetyMessage(
      _fbb,
      id,
      refPos,
      participants__,
      time_records,
      msg_id,
      rsu_id__);
}

inline const MECData::MSG_RoadsideSafetyMessage *GetMSG_RoadsideSafetyMessage(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_RoadsideSafetyMessage>(buf);
}

inline const MECData::MSG_RoadsideSafetyMessage *GetSizePrefixedMSG_RoadsideSafetyMessage(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_RoadsideSafetyMessage>(buf);
}

inline bool VerifyMSG_RoadsideSafetyMessageBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_RoadsideSafetyMessage>(nullptr);
}

inline bool VerifySizePrefixedMSG_RoadsideSafetyMessageBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_RoadsideSafetyMessage>(nullptr);
}

inline void FinishMSG_RoadsideSafetyMessageBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_RoadsideSafetyMessage> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_RoadsideSafetyMessageBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_RoadsideSafetyMessage> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_ROADSIDESAFETYMESSAGE_MECDATA_H_
