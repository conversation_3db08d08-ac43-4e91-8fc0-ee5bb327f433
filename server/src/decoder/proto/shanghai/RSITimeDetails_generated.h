// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_RSITIMEDETAILS_MECDATA_H_
#define FLATBUFFERS_GENERATED_RSITIMEDETAILS_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "TimeConfidence_generated.h"

namespace MECData {

struct DF_RSITimeDetails;
struct DF_RSITimeDetailsBuilder;

struct DF_RSITimeDetails FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_RSITimeDetailsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_STARTTIME = 4,
    VT_ENDTIME = 6,
    VT_ENDTIMECONFIDENCE = 8
  };
  uint32_t startTime() const {
    return GetField<uint32_t>(VT_STARTTIME, 0);
  }
  uint32_t endTime() const {
    return GetField<uint32_t>(VT_ENDTIME, 0);
  }
  MECData::DE_TimeConfidence endTimeConfidence() const {
    return static_cast<MECData::DE_TimeConfidence>(GetField<int8_t>(VT_ENDTIMECONFIDENCE, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_STARTTIME, 4) &&
           VerifyField<uint32_t>(verifier, VT_ENDTIME, 4) &&
           VerifyField<int8_t>(verifier, VT_ENDTIMECONFIDENCE, 1) &&
           verifier.EndTable();
  }
};

struct DF_RSITimeDetailsBuilder {
  typedef DF_RSITimeDetails Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_startTime(uint32_t startTime) {
    fbb_.AddElement<uint32_t>(DF_RSITimeDetails::VT_STARTTIME, startTime, 0);
  }
  void add_endTime(uint32_t endTime) {
    fbb_.AddElement<uint32_t>(DF_RSITimeDetails::VT_ENDTIME, endTime, 0);
  }
  void add_endTimeConfidence(MECData::DE_TimeConfidence endTimeConfidence) {
    fbb_.AddElement<int8_t>(DF_RSITimeDetails::VT_ENDTIMECONFIDENCE, static_cast<int8_t>(endTimeConfidence), 0);
  }
  explicit DF_RSITimeDetailsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_RSITimeDetails> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_RSITimeDetails>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_RSITimeDetails> CreateDF_RSITimeDetails(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t startTime = 0,
    uint32_t endTime = 0,
    MECData::DE_TimeConfidence endTimeConfidence = MECData::DE_TimeConfidence_unavailable) {
  DF_RSITimeDetailsBuilder builder_(_fbb);
  builder_.add_endTime(endTime);
  builder_.add_startTime(startTime);
  builder_.add_endTimeConfidence(endTimeConfidence);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_RSITIMEDETAILS_MECDATA_H_
