// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_VERTOFFSETB12_MECDATA_H_
#define FLATBUFFERS_GENERATED_VERTOFFSETB12_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_VertOffsetB12;
struct DF_VertOffsetB12Builder;

struct DF_VertOffsetB12 FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_VertOffsetB12Builder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VERT = 4
  };
  int16_t vert() const {
    return GetField<int16_t>(VT_VERT, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int16_t>(verifier, VT_VERT, 2) &&
           verifier.EndTable();
  }
};

struct DF_VertOffsetB12Builder {
  typedef DF_VertOffsetB12 Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_vert(int16_t vert) {
    fbb_.AddElement<int16_t>(DF_VertOffsetB12::VT_VERT, vert, 0);
  }
  explicit DF_VertOffsetB12Builder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_VertOffsetB12> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_VertOffsetB12>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_VertOffsetB12> CreateDF_VertOffsetB12(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int16_t vert = 0) {
  DF_VertOffsetB12Builder builder_(_fbb);
  builder_.add_vert(vert);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_VERTOFFSETB12_MECDATA_H_
