// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_VEHINTENTIONANDREQUEST_MECDATA_H_
#define FLATBUFFERS_GENERATED_VEHINTENTIONANDREQUEST_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "IARData_generated.h"
#include "Position3D_generated.h"

namespace MECData {

struct MSG_VehIntentionAndRequest;
struct MSG_VehIntentionAndRequestBuilder;

struct MSG_VehIntentionAndRequest FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_VehIntentionAndRequestBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ID = 4,
    VT_SECMARK = 6,
    VT_REFPOS = 8,
    VT_INTANDREQ = 10,
    VT_MSG_ID = 12,
    VT_RSU_ID = 14
  };
  const ::flatbuffers::String *id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_ID);
  }
  uint16_t secMark() const {
    return GetField<uint16_t>(VT_SECMARK, 65535);
  }
  const MECData::DF_Position3D *refPos() const {
    return GetPointer<const MECData::DF_Position3D *>(VT_REFPOS);
  }
  const MECData::DF_IARData *intAndReq() const {
    return GetPointer<const MECData::DF_IARData *>(VT_INTANDREQ);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  const ::flatbuffers::Vector<uint8_t> *rsu_id() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_RSU_ID);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_ID) &&
           verifier.VerifyString(id()) &&
           VerifyField<uint16_t>(verifier, VT_SECMARK, 2) &&
           VerifyOffsetRequired(verifier, VT_REFPOS) &&
           verifier.VerifyTable(refPos()) &&
           VerifyOffsetRequired(verifier, VT_INTANDREQ) &&
           verifier.VerifyTable(intAndReq()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           VerifyOffset(verifier, VT_RSU_ID) &&
           verifier.VerifyVector(rsu_id()) &&
           verifier.EndTable();
  }
};

struct MSG_VehIntentionAndRequestBuilder {
  typedef MSG_VehIntentionAndRequest Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_id(::flatbuffers::Offset<::flatbuffers::String> id) {
    fbb_.AddOffset(MSG_VehIntentionAndRequest::VT_ID, id);
  }
  void add_secMark(uint16_t secMark) {
    fbb_.AddElement<uint16_t>(MSG_VehIntentionAndRequest::VT_SECMARK, secMark, 65535);
  }
  void add_refPos(::flatbuffers::Offset<MECData::DF_Position3D> refPos) {
    fbb_.AddOffset(MSG_VehIntentionAndRequest::VT_REFPOS, refPos);
  }
  void add_intAndReq(::flatbuffers::Offset<MECData::DF_IARData> intAndReq) {
    fbb_.AddOffset(MSG_VehIntentionAndRequest::VT_INTANDREQ, intAndReq);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_VehIntentionAndRequest::VT_MSG_ID, msg_id, 0);
  }
  void add_rsu_id(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> rsu_id) {
    fbb_.AddOffset(MSG_VehIntentionAndRequest::VT_RSU_ID, rsu_id);
  }
  explicit MSG_VehIntentionAndRequestBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_VehIntentionAndRequest> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_VehIntentionAndRequest>(end);
    fbb_.Required(o, MSG_VehIntentionAndRequest::VT_ID);
    fbb_.Required(o, MSG_VehIntentionAndRequest::VT_REFPOS);
    fbb_.Required(o, MSG_VehIntentionAndRequest::VT_INTANDREQ);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_VehIntentionAndRequest> CreateMSG_VehIntentionAndRequest(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> id = 0,
    uint16_t secMark = 65535,
    ::flatbuffers::Offset<MECData::DF_Position3D> refPos = 0,
    ::flatbuffers::Offset<MECData::DF_IARData> intAndReq = 0,
    int64_t msg_id = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> rsu_id = 0) {
  MSG_VehIntentionAndRequestBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_rsu_id(rsu_id);
  builder_.add_intAndReq(intAndReq);
  builder_.add_refPos(refPos);
  builder_.add_id(id);
  builder_.add_secMark(secMark);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_VehIntentionAndRequest> CreateMSG_VehIntentionAndRequestDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *id = nullptr,
    uint16_t secMark = 65535,
    ::flatbuffers::Offset<MECData::DF_Position3D> refPos = 0,
    ::flatbuffers::Offset<MECData::DF_IARData> intAndReq = 0,
    int64_t msg_id = 0,
    const std::vector<uint8_t> *rsu_id = nullptr) {
  auto id__ = id ? _fbb.CreateString(id) : 0;
  auto rsu_id__ = rsu_id ? _fbb.CreateVector<uint8_t>(*rsu_id) : 0;
  return MECData::CreateMSG_VehIntentionAndRequest(
      _fbb,
      id__,
      secMark,
      refPos,
      intAndReq,
      msg_id,
      rsu_id__);
}

inline const MECData::MSG_VehIntentionAndRequest *GetMSG_VehIntentionAndRequest(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_VehIntentionAndRequest>(buf);
}

inline const MECData::MSG_VehIntentionAndRequest *GetSizePrefixedMSG_VehIntentionAndRequest(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_VehIntentionAndRequest>(buf);
}

inline bool VerifyMSG_VehIntentionAndRequestBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_VehIntentionAndRequest>(nullptr);
}

inline bool VerifySizePrefixedMSG_VehIntentionAndRequestBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_VehIntentionAndRequest>(nullptr);
}

inline void FinishMSG_VehIntentionAndRequestBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_VehIntentionAndRequest> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_VehIntentionAndRequestBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_VehIntentionAndRequest> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_VEHINTENTIONANDREQUEST_MECDATA_H_
