// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_ROADPOINT_MECDATA_H_
#define FLATBUFFERS_GENERATED_ROADPOINT_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "PositionOffsetLLV_generated.h"

namespace MECData {

struct DF_RoadPoint;
struct DF_RoadPointBuilder;

struct DF_RoadPoint FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_RoadPointBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_POSOFFSET = 4
  };
  const MECData::DF_PositionOffsetLLV *posOffset() const {
    return GetPointer<const MECData::DF_PositionOffsetLLV *>(VT_POSOFFSET);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_POSOFFSET) &&
           verifier.VerifyTable(posOffset()) &&
           verifier.EndTable();
  }
};

struct DF_RoadPointBuilder {
  typedef DF_RoadPoint Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_posOffset(::flatbuffers::Offset<MECData::DF_PositionOffsetLLV> posOffset) {
    fbb_.AddOffset(DF_RoadPoint::VT_POSOFFSET, posOffset);
  }
  explicit DF_RoadPointBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_RoadPoint> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_RoadPoint>(end);
    fbb_.Required(o, DF_RoadPoint::VT_POSOFFSET);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_RoadPoint> CreateDF_RoadPoint(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_PositionOffsetLLV> posOffset = 0) {
  DF_RoadPointBuilder builder_(_fbb);
  builder_.add_posOffset(posOffset);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_ROADPOINT_MECDATA_H_
