// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_SIZEVALUECONFIDENCE_MECDATA_H_
#define FLATBUFFERS_GENERATED_SIZEVALUECONFIDENCE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_SizeValueConfidence : uint8_t {
  DE_SizeValueConfidence_unavailable = 0,
  DE_SizeValueConfidence_size_100_00 = 1,
  DE_SizeValueConfidence_size_050_00 = 2,
  DE_SizeValueConfidence_size_020_00 = 3,
  DE_SizeValueConfidence_size_010_00 = 4,
  DE_SizeValueConfidence_size_005_00 = 5,
  DE_SizeValueConfidence_size_002_00 = 6,
  DE_SizeValueConfidence_size_001_00 = 7,
  DE_SizeValueConfidence_size_000_50 = 8,
  DE_SizeValueConfidence_size_000_20 = 9,
  DE_SizeValueConfidence_size_000_10 = 10,
  DE_SizeValueConfidence_size_000_05 = 11,
  DE_SizeValueConfidence_size_000_02 = 12,
  DE_SizeValueConfidence_size_000_01 = 13,
  DE_SizeValueConfidence_MIN = DE_SizeValueConfidence_unavailable,
  DE_SizeValueConfidence_MAX = DE_SizeValueConfidence_size_000_01
};

inline const DE_SizeValueConfidence (&EnumValuesDE_SizeValueConfidence())[14] {
  static const DE_SizeValueConfidence values[] = {
    DE_SizeValueConfidence_unavailable,
    DE_SizeValueConfidence_size_100_00,
    DE_SizeValueConfidence_size_050_00,
    DE_SizeValueConfidence_size_020_00,
    DE_SizeValueConfidence_size_010_00,
    DE_SizeValueConfidence_size_005_00,
    DE_SizeValueConfidence_size_002_00,
    DE_SizeValueConfidence_size_001_00,
    DE_SizeValueConfidence_size_000_50,
    DE_SizeValueConfidence_size_000_20,
    DE_SizeValueConfidence_size_000_10,
    DE_SizeValueConfidence_size_000_05,
    DE_SizeValueConfidence_size_000_02,
    DE_SizeValueConfidence_size_000_01
  };
  return values;
}

inline const char * const *EnumNamesDE_SizeValueConfidence() {
  static const char * const names[15] = {
    "unavailable",
    "size_100_00",
    "size_050_00",
    "size_020_00",
    "size_010_00",
    "size_005_00",
    "size_002_00",
    "size_001_00",
    "size_000_50",
    "size_000_20",
    "size_000_10",
    "size_000_05",
    "size_000_02",
    "size_000_01",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_SizeValueConfidence(DE_SizeValueConfidence e) {
  if (::flatbuffers::IsOutRange(e, DE_SizeValueConfidence_unavailable, DE_SizeValueConfidence_size_000_01)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_SizeValueConfidence()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_SIZEVALUECONFIDENCE_MECDATA_H_
