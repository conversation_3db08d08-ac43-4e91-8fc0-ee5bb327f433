// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_STABILITYCONTROLSTATUS_MECDATA_H_
#define FLATBUFFERS_GENERATED_STABILITYCONTROLSTATUS_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_StabilityControlStatus : int8_t {
  DE_StabilityControlStatus_unavailable = 0,
  DE_StabilityControlStatus_off = 1,
  DE_StabilityControlStatus_on = 2,
  DE_StabilityControlStatus_engaged = 3,
  DE_StabilityControlStatus_MIN = DE_StabilityControlStatus_unavailable,
  DE_StabilityControlStatus_MAX = DE_StabilityControlStatus_engaged
};

inline const DE_StabilityControlStatus (&EnumValuesDE_StabilityControlStatus())[4] {
  static const DE_StabilityControlStatus values[] = {
    DE_StabilityControlStatus_unavailable,
    DE_StabilityControlStatus_off,
    DE_StabilityControlStatus_on,
    DE_StabilityControlStatus_engaged
  };
  return values;
}

inline const char * const *EnumNamesDE_StabilityControlStatus() {
  static const char * const names[5] = {
    "unavailable",
    "off",
    "on",
    "engaged",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_StabilityControlStatus(DE_StabilityControlStatus e) {
  if (::flatbuffers::IsOutRange(e, DE_StabilityControlStatus_unavailable, DE_StabilityControlStatus_engaged)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_StabilityControlStatus()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_STABILITYCONTROLSTATUS_MECDATA_H_
