// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_REQSIGNALPRIORITY_MECDATA_H_
#define FLATBUFFERS_GENERATED_REQSIGNALPRIORITY_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "MovementEx_generated.h"
#include "NodeReferenceID_generated.h"

namespace MECData {

struct DF_ReqSignalPriority;
struct DF_ReqSignalPriorityBuilder;

struct DF_ReqSignalPriority FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ReqSignalPriorityBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_INTERSECTIONID = 4,
    VT_REQUIREDMOV = 6,
    VT_ESTIMATEDARRIVALTIME = 8,
    VT_DISTANCE2INTERSECTION = 10
  };
  const MECData::DF_NodeReferenceID *intersectionId() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_INTERSECTIONID);
  }
  const MECData::DF_MovementEx *requiredMov() const {
    return GetPointer<const MECData::DF_MovementEx *>(VT_REQUIREDMOV);
  }
  uint16_t estimatedArrivalTime() const {
    return GetField<uint16_t>(VT_ESTIMATEDARRIVALTIME, 0);
  }
  uint16_t distance2Intersection() const {
    return GetField<uint16_t>(VT_DISTANCE2INTERSECTION, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_INTERSECTIONID) &&
           verifier.VerifyTable(intersectionId()) &&
           VerifyOffsetRequired(verifier, VT_REQUIREDMOV) &&
           verifier.VerifyTable(requiredMov()) &&
           VerifyField<uint16_t>(verifier, VT_ESTIMATEDARRIVALTIME, 2) &&
           VerifyField<uint16_t>(verifier, VT_DISTANCE2INTERSECTION, 2) &&
           verifier.EndTable();
  }
};

struct DF_ReqSignalPriorityBuilder {
  typedef DF_ReqSignalPriority Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_intersectionId(::flatbuffers::Offset<MECData::DF_NodeReferenceID> intersectionId) {
    fbb_.AddOffset(DF_ReqSignalPriority::VT_INTERSECTIONID, intersectionId);
  }
  void add_requiredMov(::flatbuffers::Offset<MECData::DF_MovementEx> requiredMov) {
    fbb_.AddOffset(DF_ReqSignalPriority::VT_REQUIREDMOV, requiredMov);
  }
  void add_estimatedArrivalTime(uint16_t estimatedArrivalTime) {
    fbb_.AddElement<uint16_t>(DF_ReqSignalPriority::VT_ESTIMATEDARRIVALTIME, estimatedArrivalTime, 0);
  }
  void add_distance2Intersection(uint16_t distance2Intersection) {
    fbb_.AddElement<uint16_t>(DF_ReqSignalPriority::VT_DISTANCE2INTERSECTION, distance2Intersection, 0);
  }
  explicit DF_ReqSignalPriorityBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ReqSignalPriority> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ReqSignalPriority>(end);
    fbb_.Required(o, DF_ReqSignalPriority::VT_INTERSECTIONID);
    fbb_.Required(o, DF_ReqSignalPriority::VT_REQUIREDMOV);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ReqSignalPriority> CreateDF_ReqSignalPriority(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> intersectionId = 0,
    ::flatbuffers::Offset<MECData::DF_MovementEx> requiredMov = 0,
    uint16_t estimatedArrivalTime = 0,
    uint16_t distance2Intersection = 0) {
  DF_ReqSignalPriorityBuilder builder_(_fbb);
  builder_.add_requiredMov(requiredMov);
  builder_.add_intersectionId(intersectionId);
  builder_.add_distance2Intersection(distance2Intersection);
  builder_.add_estimatedArrivalTime(estimatedArrivalTime);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_REQSIGNALPRIORITY_MECDATA_H_
