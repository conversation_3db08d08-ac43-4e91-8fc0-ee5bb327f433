#include "communication.h"
#include "commonFunction.h"
#include "datacapture.Communicate.pb.h"
#include "message.h"
#include "struct.h"
#include "unistd.h"
#include <iostream>
#include <memory>
#include <regex>
#include "logger.hpp"

CCommunication::CCommunication(CMediator *pMediator) : CModuleBase(pMediator)
{
	m_dwPort = 9999;
	m_dwTelnetPort = 10099;
	m_bUIEnble = false;
	m_bTelnetEnable = false;
	m_bIsUpdateStatus = false;
}

CCommunication::~CCommunication() {}

void CCommunication::Init()
{
	// 判断类型
	m_dwPort = gPort;
	if (gConnectType == CAPTURE_TERMIAL)
	{
		m_bTelnetEnable = true;
		m_bUIEnble = false;
		INFO("client type is terminal connect!");
	}
	else
	{
		m_bUIEnble = true;
		m_bTelnetEnable = false;
		INFO("client type is GUI connect!");
	}
}

void CCommunication::Start()
{
	if (m_bIsRunning)
	{
		return;
	}
	/*****UI端服务*******/
	if (m_bUIEnble)
	{
		m_pServer = new CTcpServer();
		m_pServer->Init(m_dwPort);

		// 设置回调函数
		std::function<void(int dwDeviceID, std::string strMsg)> cb =
			[this](int dwDeviceID, std::string strMsg)
		{
			this->dataRecvd(dwDeviceID, strMsg);
		};

		std::function<void(int dwDeviceId)> connectCb = [this](int dwDeviceId)
		{
			this->clientConnected(dwDeviceId);
		};

		m_pServer->SetConnected(connectCb);
		m_pServer->SetCallBack(cb);

		if (!m_pServer->Start())
		{
			ERROR("TCP Server start failed!");
			return;
		}
		INFO("TCP Server start succeed!");
	}

	// /*******telnet服务***********/
	// if(m_bTelnetEnable)
	// {
	//     m_pTelnet = new CTelnetServer();
	//     m_pTelnet->Init(m_dwTelnetPort);

	//     //设置回调函数
	//     std::function<void(std::string strMsg)> cb = [this](std::string strMsg)
	//     {
	//         this->dataRecvdTelnet(strMsg);
	//     };

	//     std::function<void(int dwDeviceId)> connectCb  = [this](int dwDeviceId)
	//     {
	//         this->clientConnected(dwDeviceId);
	//     };
	//     m_pTelnet->SetCallBack(cb);
	//     m_pTelnet->SetConnected(connectCb);

	//     if(!m_pTelnet->Start())
	//     {
	//         std::cout << "telnet server start error" << std::endl;
	//         return;
	//     }
	// }

	m_bIsRunning = true;
}
void CCommunication::Stop()
{
	if (m_pServer != nullptr)
	{
		m_pServer->Stop();
	}
	// if (m_pTelnet != nullptr)
	// {
	//     m_pTelnet->Stop();
	// }

	m_bIsRunning = false;
}
void CCommunication::Pause()
{
}

bool CCommunication::MsgFilter(u32 msgType)
{
	bool ret = true;
	if (msgType == SYSTEM_INIT || msgType == SYSTEM_START ||
		msgType == SYSTEM_STOP || msgType == SYSTEM_PAUSE ||
		msgType == DATA_QUERY_RES || msgType == CAPTURE_MONITOR_UPDATE ||
		msgType == DATA_COMPRESS_RES || msgType == CAPTURE_CONFIG_RES ||
		msgType == DATA_ROADINFO_UPDATE || msgType == SYSTEM_UPDATE_STATUS ||
		msgType == DATA_QUERY_BATCH_RES || msgType == SYSTEM_INFO_RES ||
		msgType == CLIENT_INFO_RES || msgType == CLIENT_INFO_REQ ||
		msgType == SYSTEM_NET_SET_RES || msgType == SYSTEM_STORE_SET_RES ||
		msgType == SYSTEM_CMD_RES || msgType == DATA_DATABSE_CLOSED ||
		msgType == TESTCASE_GET_UPLOAD_FILE_LIST_RES || msgType == TESTCASE_UPDATE_INFO ||
		msgType == TESTCASE_ADD_TESTCASE_RES || msgType == TESTCASE_CONTROL_DATA_PUSH_RES ||
		msgType == TESTCASE_LOG || msgType == TESTCASE_STATUS)
	{
		ret = false;
	}
	return ret;
}

void CCommunication::HandleMsg(u32 msgLevel, u32 deviceId, u32 msgType,
							   std::shared_ptr<void> spData)
{

	switch (msgType)
	{
	case SYSTEM_INIT:
		Init();
		break;
	case SYSTEM_START:
		// Start();
		break;
	case SYSTEM_STOP:
		// Stop();
		break;
	case SYSTEM_PAUSE:
		Pause();
		break;
	case DATA_QUERY_RES:
		handleDataQueryRes(msgLevel, deviceId, spData);
		break;
	case DATA_QUERY_BATCH_RES:
		handleDataQueryBatchRes(msgLevel, deviceId, spData);
		break;
	case CAPTURE_MONITOR_UPDATE:
		handleUpdateMonitor(msgLevel, deviceId, spData);
		// handleUpdateMonitor2Telnet(spData);
		break;
	case DATA_COMPRESS_RES:
		handleDataCompressRes(msgLevel, deviceId, spData);
		// handleDataCompressRes2Telnet(spData);
		break;
	case CAPTURE_CONFIG_RES:
		handleConfigRes(msgLevel, deviceId, spData);
		break;
	case DATA_ROADINFO_UPDATE:
		handleRoadInfoUpdate(msgLevel, deviceId, spData);
		break;
	case SYSTEM_UPDATE_STATUS:
		handleSystemStatusUpdate(msgLevel, deviceId, spData);
		// handleSystemStatusUpdate2Telnet(spData);
		break;
	case SYSTEM_INFO_RES:
		handleSystemInfoRes(msgLevel, deviceId, spData);
		break;
	case CLIENT_INFO_REQ:
		handleClientInfoReq(msgLevel, deviceId, spData);
		break;
	case CLIENT_INFO_RES:
		handleClientInfoRes(msgLevel, deviceId, spData);
		break;
	case SYSTEM_NET_SET_RES:
		handleInternetSettingRes(msgLevel, deviceId, spData);
		break;
	case SYSTEM_STORE_SET_RES:
		handleStoreSettingRes(msgLevel, deviceId, spData);
		break;
	case SYSTEM_CMD_RES:
		handleSystemCMDRes(msgLevel, deviceId, spData);
		break;
	case DATA_DATABSE_CLOSED:
		handleDatabaseClose(msgLevel, deviceId, spData);
		break;
	case SYSTEM_LOG_UPDATE:
		handleSystemLogUpdate(msgLevel, deviceId, spData);
		break;
	case TESTCASE_GET_UPLOAD_FILE_LIST_RES:
		handleTestCaseGetUploadFileListRes(msgLevel, deviceId, spData);
		break;
	case TESTCASE_UPDATE_INFO:
		handleTestCaseUpdateInfo(msgLevel, deviceId, spData);
		break;
	case TESTCASE_ADD_TESTCASE_RES:
		handleTestCaseAddTestCaseRes(msgLevel, deviceId, spData);
		break;
	case TESTCASE_DELETE_ONE_RES:
		handleTestCaseDeleteOneRes(msgLevel, deviceId, spData);
	// TESTCASE_DELETE_ONE_REQ
		break;
	case TESTCASE_CONTROL_DATA_PUSH_RES:
		handleTestCaseControlRes(msgLevel, deviceId, spData);
		break;
	case TESTCASE_LOG:
		handleTestCaseLog(msgLevel, deviceId, spData);
		break;
	case TESTCASE_STATUS:
		handleTestCaseStatus(msgLevel, deviceId, spData);
		break;
	default:
		break;
	}
}

void CCommunication::clientConnected(int dwDeviceId)
{
	Notify(MSG_LEVEL_ONE_DEVICE, dwDeviceId, SYSTEM_CLIENT_CONNECTED, nullptr);
}

void CCommunication::dataRecvd(int dwDeviceID, std::string strMsg)
{
	datacapture::Message protoMsg;

	if (!protoMsg.ParseFromString(strMsg))
	{
		ERROR("decode protobuf failed");
		return;
	}

	switch (protoMsg.type())
	{
	case datacapture::Message::PROTO_COMMON_CMD:
		handleCommonCmd(dwDeviceID, strMsg);
		break;
	case datacapture::Message::PROTO_UPDATE_CONFIG:
		handleUpdateConfig(dwDeviceID, strMsg);
		break;
	case datacapture::Message::PROTO_DATA_QUERY_REQ:
		handleDataQueryReq(dwDeviceID, strMsg);
		break;
	case datacapture::Message::PROTO_DATA_COMPRESS_REQ:
		handleDataCompressReq(dwDeviceID, strMsg);
		break;
	case datacapture::Message::PROTO_CONFIG_REQ:
		handleConfigReq(dwDeviceID, strMsg);
		break;
	case datacapture::Message::PROTO_DATA_QUERY_BATCHES_REQ:
		handleDataQueryBatchesReq(dwDeviceID, strMsg);
		break;
	case datacapture::Message::PROTO_SYSTEM_INFO_REQ:
		handleSystemInfoReq(dwDeviceID, strMsg);
		break;
	case datacapture::Message::PROTO_CLIENT_INFO_REQ:
		handleClientInfoReq(dwDeviceID, strMsg);
		break;
	case datacapture::Message::PROTO_SYSTEM_CMD_REQ:
		handleSystemCMDReq(dwDeviceID, strMsg);
		break;
	case datacapture::Message::PROTO_SYSTEM_NET_REQ:
		handleInternetSettingReq(dwDeviceID, strMsg);
		break;
	case datacapture::Message::PROTO_SYSTEM_STORE_REQ:
		handleStoreSettingReq(dwDeviceID, strMsg);
		break;
	case datacapture::Message::PROTO_TESTCASE_ADD_TESTCASE_REQ:
		handleTestCaseAddTestCaseReq(dwDeviceID, strMsg);
		break;
	case datacapture::Message::PROTO_TESTCASE_CONTROL_DATA_PUSH_REQ:
		handleTestCaseControlDataPushReq(dwDeviceID, strMsg);
		break;
	case datacapture::Message::PROTO_TESTCASE_GET_ALL_INFO:
		handleTestCaseGetAllInfo(dwDeviceID, strMsg);
		break;
	case datacapture::Message::PROTO_TESTCASE_GET_UPLOAD_FILE_LIST_REQ:
		handleTestCaseGetUploadFileListReq(dwDeviceID, strMsg);
		break;
	case datacapture::Message::PROTO_TESTCASE_DELETE_ONE_REQ:
		handleTestCaseDeleteOneReq(dwDeviceID, strMsg);
		break;
	default:
		break;
	}
}

// void CCommunication::dataRecvdTelnet(std::string strMsg)
// {
//     // strMsg.resize(strMsg.size() -2);    //去除字符串尾部不可见字符
//     std::cout << "recv strMsg = " << strMsg << std::endl;
//     if (strMsg == "startcapture")
//     {
//         std::cout << "starcapure" << std::endl;
//         Notify(SYSTEM_START, NULL);
//     }
//     else if (strMsg == "stopcapture")
//     {
//         Notify(SYSTEM_STOP, NULL);
//     }
//     else if (strMsg == "showstatus 1")
//     {
//         m_bIsUpdateStatus = true;
//     }
//     else if (strMsg == "showstatus 0")
//     {
//         m_bIsUpdateStatus = false;
//     }
//     else if (strMsg.find("package") != std::string::npos)
//     {
//         handleDataCompressReq2Telnet(strMsg);
//     }
//     else
//     {
//         std::string strTable =
//         "输入指令:\n开始采集:startcapture\n停止采集:stopcapture\n查看状态:showstatus
//         0/1(关闭/打开状态)\n打包压缩:(格式如下)\n"; strTable += "固定头
//         路口id        起始时间            结束时间       包名\n"; strTable +=
//         "package:10312 2024-01-01 01:01:00 2024-02-02 00:00:00 test.zip\n" ;
//         m_pTelnet->SendMessageToClient(strTable);
//     }
// }

bool CCommunication::handleCommonCmd(int dwDeviceID, std::string strMsg)
{
	datacapture::Message protoMsg;

	if (!protoMsg.ParseFromString(strMsg))
	{
		ERROR("Decode protobuf failed! msg type : commoncmd");
		return false;
	}
	std::string strCmd = "";
	u32 msgType = 0;
	switch (protoMsg.commoncmd().type())
	{
	case datacapture::CommonCMD::PROTO_SYSTEM_INIT:
		msgType = SYSTEM_INIT;
		strCmd = "SYSTEM_INIT";
		break;
	case datacapture::CommonCMD::PROTO_SYSTEM_START:
		msgType = SYSTEM_START;
		strCmd = "SYSTEM_START";
		break;
	case datacapture::CommonCMD::PROTO_SYSTEM_STOP:
		msgType = SYSTEM_STOP;
		strCmd = "SYSTEM_STOP";
		break;
	case datacapture::CommonCMD::PROTO_SYSTEM_PAUSE:
		msgType = SYSTEM_PAUSE;
		strCmd = "SYSTEM_PAUSE";
		break;

	default:
		break;
	}
	INFO("{}", strCmd);
	Notify(MSG_LEVEL_ONE_DEVICE, dwDeviceID, msgType, nullptr);

	return true;
}

bool CCommunication::handleUpdateConfig(int dwDeviceID, std::string strMsg)
{
	datacapture::Message protoMsg;
	if (!protoMsg.ParseFromString(strMsg))
	{
		ERROR("Decode protobuf failed! msg type : UpdateConfig");
		return false;
	}

	std::shared_ptr<TArgsList> spArgsList = std::make_shared<TArgsList>();
	spArgsList->dwCnt = protoMsg.argslist().dwcnt();
	for (int i = 0; i < protoMsg.argslist().argslist_size(); ++i)
	{
		datacapture::ConnectArgs msgConnectArgs = protoMsg.argslist().argslist(i);
		TConnectArgs stConnectArgs{};
		stConnectArgs.dwCnt = msgConnectArgs.dwno();
		stConnectArgs.bEnable = msgConnectArgs.isenable();
		stConnectArgs.strAddr = msgConnectArgs.straddr();
		stConnectArgs.strClientId = msgConnectArgs.strclientid();
		stConnectArgs.strPassword = msgConnectArgs.strpassword();
		stConnectArgs.strRoadId = msgConnectArgs.strcrossroadid();
		stConnectArgs.strTopic = msgConnectArgs.strtopice();
		stConnectArgs.strUsername = msgConnectArgs.strusername();
		stConnectArgs.strDecribe = msgConnectArgs.strdescribe();
		stConnectArgs.dwFactory = msgConnectArgs.dwfactory();
		switch (msgConnectArgs.type())
		{
		case datacapture::ConnectArgs::PROTO_MQTT:
			stConnectArgs.argsType = TConnectArgs::MQTT;
			break;
		case datacapture::ConnectArgs::PROTO_KAFKA:
			stConnectArgs.argsType = TConnectArgs::KAFKA;
			break;
		case datacapture::ConnectArgs::PROTO_HTTP:
			stConnectArgs.argsType = TConnectArgs::HTTP;
			break;
		case datacapture::ConnectArgs::PROTO_TCP_SERVER:
			stConnectArgs.argsType = TConnectArgs::TCPSERVER;
			break;
		case datacapture::ConnectArgs::PROTO_UDP:
			stConnectArgs.argsType = TConnectArgs::UDP;
		break;
		default:
			break;
		}
		spArgsList->vecList.push_back(stConnectArgs);
	}

	Notify(MSG_LEVEL_ONE_DEVICE, dwDeviceID, CAPTURE_CONFIG_UPDATE,
		   std::static_pointer_cast<void>(spArgsList));

	return true;
}

bool CCommunication::handleConfigReq(int dwDeviceID, std::string strMsg)
{
	// 没有需要解析的数据

	// 直接制作消息体透传
	std::shared_ptr<TArgsList> spArgsList = std::make_shared<TArgsList>();
	Notify(MSG_LEVEL_ONE_DEVICE, dwDeviceID, CAPTURE_CONFIG_REQ,
		   std::static_pointer_cast<void>(spArgsList));

	return true;
}

bool CCommunication::handleDataQueryBatchesReq(int dwDeviceID,
											   std::string strMsg)
{
	datacapture::Message protoMsg;
	if (!protoMsg.ParseFromString(strMsg))
	{
		ERROR("Decode protobuf failed! msg type : DataQueryBatchesReq");
		return false;
	}
	std::shared_ptr<TBatchesDataQueryReq> spDataQueryReq =
		std::make_shared<TBatchesDataQueryReq>();
	spDataQueryReq->llStartTime = protoMsg.dataquerybatchesreq().llstarttime();
	spDataQueryReq->llEndTime = protoMsg.dataquerybatchesreq().llendtime();
	spDataQueryReq->dwNowPage = protoMsg.dataquerybatchesreq().dwnowpage();
	spDataQueryReq->dwPageSize = protoMsg.dataquerybatchesreq().dwpagesize();
	spDataQueryReq->strCrossId = protoMsg.dataquerybatchesreq().strcrossid();

	Notify(MSG_LEVEL_ONE_DEVICE, dwDeviceID, DATA_QUERY_BATCH_REQ,
		   std::static_pointer_cast<void>(spDataQueryReq));
	return true;
}

bool CCommunication::handleSystemInfoReq(int deDeviceID, std::string strMsg)
{
	datacapture::Message protoMsg;
	if (!protoMsg.ParseFromString(strMsg))
	{
		ERROR("Decode protobuf failed! msg type : SystemInfoReq");
		return false;
	}
	std::shared_ptr<TSystemInfo> spSystemInfo = std::make_shared<TSystemInfo>();
	spSystemInfo->fCpuUsed = protoMsg.systeminfo().cpuused();
	spSystemInfo->fCpuCoreNum = protoMsg.systeminfo().cpucorenum();
	spSystemInfo->fCpuFreq = protoMsg.systeminfo().cpufreq();
	spSystemInfo->fCpuTemp = protoMsg.systeminfo().cputemp();
	spSystemInfo->fMemoryTotal = protoMsg.systeminfo().memorytotal();
	spSystemInfo->fMemoryUsed = protoMsg.systeminfo().memoryused();
	spSystemInfo->fDiskUsed = protoMsg.systeminfo().diskused();

	Notify(MSG_LEVEL_ONE_DEVICE, deDeviceID, SYSTEM_INFO_REQ,
		   std::static_pointer_cast<void>(spSystemInfo));
	return true;
}

bool CCommunication::handleClientInfoReq(int deDeviceID, std::string strMsg)
{
	datacapture::Message protoMsg;
	if (!protoMsg.ParseFromString(strMsg))
	{
		ERROR("Decode protobuf failed! msg type : ClientInfoReq");
		return false;
	}
	std::shared_ptr<TClientInfo> spClientInfo = std::make_shared<TClientInfo>();
	spClientInfo->strIp = protoMsg.clientinfo().ip();
	spClientInfo->dwPort = protoMsg.clientinfo().port();

	// 消息内部循环
	//    Notify(MSG_LEVEL_ONE_DEVICE, deDeviceID, CLIENT_INFO_REQ,
	//           std::static_pointer_cast<void>(spClientInfo));
	handleClientInfoReq(MSG_LEVEL_ONE_DEVICE, deDeviceID,
						std::static_pointer_cast<void>(spClientInfo));

	return true;
}

bool CCommunication::handleInternetSettingReq(int dwDeviceId, std::string strMsg)
{
	datacapture::Message protoMsg;
	if (protoMsg.ParseFromString(strMsg))
	{
		ERROR("Decode protobuf failed! msg type : handleInternetSetting");
		return false;
	}
	std::shared_ptr<TNetConfig> spNetSetting = std::make_shared<TNetConfig>();
	spNetSetting->strLocalIp = protoMsg.netconfig().localip();
	spNetSetting->strGateway = protoMsg.netconfig().gateway();
	spNetSetting->strMask = protoMsg.netconfig().mask();
	spNetSetting->strRoute = protoMsg.netconfig().route();
	spNetSetting->strTargetIp = protoMsg.netconfig().targetip();
	Notify(MSG_LEVEL_ONE_DEVICE, dwDeviceId, SYSTEM_NET_SET_REQ,
		   std::static_pointer_cast<void>(spNetSetting));
	return true;
}

bool CCommunication::handleStoreSettingReq(int dwDeviceId, std::string strMsg)
{
	datacapture::Message protoMsg;
	if (protoMsg.ParseFromString(strMsg))
	{
		ERROR("Decode protobuf failed! msg type : handleStoreSetting");
		return false;
	}
	std::shared_ptr<TStoreInfo> spStoreInfo = std::make_shared<TStoreInfo>();
	spStoreInfo->strStorePath = protoMsg.storeinfo().storepath();
	Notify(MSG_LEVEL_ONE_DEVICE, dwDeviceId, SYSTEM_STORE_SET_REQ,
		   std::static_pointer_cast<void>(spStoreInfo));
	return true;
}

bool CCommunication::handleSystemCMDReq(int dwDeviceId, std::string strMsg)
{
	datacapture::Message protoMsg;
	if (protoMsg.ParseFromString(strMsg))
	{
		ERROR("Decode protobuf failed! msg type : handleSystemCMD");
		return false;
	}
	std::shared_ptr<TSystemControl> spSystemCtl = std::make_shared<TSystemControl>();
	spSystemCtl->dwPort = protoMsg.systemcmd().port();
	switch (protoMsg.type())
	{
	case datacapture::SystemCmd::RECONNECTSERVER:
		spSystemCtl->type = TSystemControl::RECONNECTSERVER;
		break;
	case datacapture::SystemCmd::RESTARTSERVER:
		spSystemCtl->type = TSystemControl::RESTARTSERVER;
		break;
	case datacapture::SystemCmd::REBOOT:
		spSystemCtl->type = TSystemControl::REBOOT;
		break;
	default:
		break;
	}
	Notify(MSG_LEVEL_ONE_DEVICE, dwDeviceId, SYSTEM_CMD_REQ,
		   std::static_pointer_cast<void>(spSystemCtl));
	return true;
}

bool CCommunication::handleDataCompressReq(int dwDeviceID, std::string strMsg)
{
	datacapture::Message protoMsg;
	if (!protoMsg.ParseFromString(strMsg))
	{
		ERROR("Decode protobuf failed! msg type : DataCompressReq");
		return false;
	}
	std::shared_ptr<TDataCompressReqList> spDataCompressReq =
		std::make_shared<TDataCompressReqList>();
	spDataCompressReq->chCompressType = protoMsg.compressreq().compresstype();
	spDataCompressReq->dwReqCnt = protoMsg.compressreq().dwcnt();
	spDataCompressReq->strPackageName = protoMsg.compressreq().strpackagename();
	spDataCompressReq->strSerial = protoMsg.compressreq().strserialnum();
	spDataCompressReq->strHttpUrl = protoMsg.compressreq().strhttpurl();
	for (int i = 0; i < protoMsg.compressreq().querylist_size(); ++i)
	{
		datacapture::DataQuery msgDataQuery = protoMsg.compressreq().querylist(i);
		TDataQuery stDataQuery{};
		stDataQuery.strCrossroadId = msgDataQuery.strcrossroadid();
		stDataQuery.llStartTime = msgDataQuery.llstarttime();
		stDataQuery.llEndTime = msgDataQuery.llendtime();
		switch (msgDataQuery.type())
		{
		case datacapture::DataQuery::DATA_TYPE_RAW:
			stDataQuery.dwDataType = TDataQuery::RAWDATA;
			break;
		case datacapture::DataQuery::DATA_TYPE_STABILITY:
			stDataQuery.dwDataType = TDataQuery::STABILITY_DATA;
			break;
		case datacapture::DataQuery::DATA_TYPE_TSARI:
			stDataQuery.dwDataType = TDataQuery::TSARI_DATA;
			break;
		default:
			stDataQuery.dwDataType = TDataQuery::RAWDATA;
			break;
		}
		spDataCompressReq->vecList.push_back(stDataQuery);

	}
	Notify(MSG_LEVEL_ONE_DEVICE, dwDeviceID, DATA_COMPRESS_QUERY_REQ,
		   std::static_pointer_cast<void>(spDataCompressReq));
	return true;
}

bool CCommunication::handleDataQueryReq(int dwDeviceID, std::string strMsg)
{
	datacapture::Message protoMsg;
	if (!protoMsg.ParseFromString(strMsg))
	{
		ERROR("Decode protobuf failed! msg type : DataQueryReq");
		return false;
	}
	std::shared_ptr<TDataQuery> spDataQuery = std::make_shared<TDataQuery>();
	spDataQuery->strCrossroadId = protoMsg.dataqueryreq().strcrossroadid();
	spDataQuery->llStartTime = protoMsg.dataqueryreq().llstarttime();
	spDataQuery->llEndTime = protoMsg.dataqueryreq().llendtime();

	// std::cout << "crossroad id = "  <<  stDataQuery.strCrossroadId << "start =
	// " <<  stDataQuery.llStartTime << " end = " << stDataQuery.llEndTime <<
	// std::endl;

	Notify(MSG_LEVEL_ONE_DEVICE, dwDeviceID, DATA_QUERY_REQ,
		   std::static_pointer_cast<void>(spDataQuery));
	return true;
}

bool CCommunication::handleDataQueryRes(u32 msgLevel, u32 deviceId,
										std::shared_ptr<void> spData)
{
	if (!m_bUIEnble)
	{
		return false;
	}

	std::shared_ptr<TDataQueryRes> spDataQueryRes =
		std::static_pointer_cast<TDataQueryRes>(spData);
	if (!spDataQueryRes)
	{
		ERROR("convert failed!");
		return false;
	}

	datacapture::Message protoMsg;
	protoMsg.set_type(datacapture::Message_MessageType_PROTO_DATA_QUERY_RES);
	datacapture::DataQueryRes *msgDataQueryRes = protoMsg.mutable_dataqueryres();
	msgDataQueryRes->set_dwcnt(spDataQueryRes->dwCnt);
	msgDataQueryRes->set_bissucceed(spDataQueryRes->bIsSucceed);
	msgDataQueryRes->set_strerr(spDataQueryRes->strErr);
	for (auto iter : spDataQueryRes->vecList)
	{
		datacapture::RawData *msgRawData = msgDataQueryRes->add_datalist();
		msgRawData->set_strcrossroadid(iter.strCrossroadId);
		msgRawData->set_llrecvtime(iter.llRecvTime);
		msgRawData->set_lldatatime(iter.llDataTime);
		msgRawData->set_dwdatalength(iter.wDataLength);
		msgRawData->set_strdata(iter.strData);
	}

	std::string serialMsg;
	if (!protoMsg.SerializeToString(&serialMsg))
	{
		ERROR("DATA_QUERY_RES SerialieToString failed!");
		return false;
	}

	m_pServer->SendMessageToClient(msgLevel, deviceId, serialMsg);

	return true;
}

bool CCommunication::handleDataQueryBatchRes(u32 msgLevel, u32 deviceId,
											 std::shared_ptr<void> spData)
{
	if (!m_bUIEnble)
	{
		return false;
	}

	std::shared_ptr<TBatchesDataQueryRes> spBatchesDataQueryRes =
		std::static_pointer_cast<TBatchesDataQueryRes>(spData);
	if (!spBatchesDataQueryRes)
	{
		ERROR("convert failed!");
		return false;
	}
	datacapture::Message protoMsg;
	protoMsg.set_type(
		datacapture::Message_MessageType_PROTO_DATA_QUERY_BATCHES_RES);
	datacapture::DataQueryBatchesRes *msgDataQueryBatchesRes =
		protoMsg.mutable_dataquerybatchesres();
	msgDataQueryBatchesRes->set_bissucceed(spBatchesDataQueryRes->bIsSucceed);
	msgDataQueryBatchesRes->set_dwcnt(spBatchesDataQueryRes->dwCnt);
	msgDataQueryBatchesRes->set_strerr(spBatchesDataQueryRes->strErr);
	msgDataQueryBatchesRes->set_dwnowpage(spBatchesDataQueryRes->dwNowPage);
	msgDataQueryBatchesRes->set_dwpagesize(spBatchesDataQueryRes->dwPageSize);
	msgDataQueryBatchesRes->set_dwtotaldatas(spBatchesDataQueryRes->dwTotalDatas);
	msgDataQueryBatchesRes->set_dwtotalpages(spBatchesDataQueryRes->dwTotalPages);
	for (auto iter : spBatchesDataQueryRes->vecList)
	{
		datacapture::RawData *msgRawData = msgDataQueryBatchesRes->add_datalist();
		msgRawData->set_strcrossroadid(iter.strCrossroadId);
		msgRawData->set_llrecvtime(iter.llRecvTime);
		msgRawData->set_lldatatime(iter.llDataTime);
		msgRawData->set_dwdatalength(iter.wDataLength);
		msgRawData->set_strdata(iter.strData);
	}
	std::string serialMsg;
	if (!protoMsg.SerializeToString(&serialMsg))
	{
		ERROR("DATA_QUERY_BATCHES_RES SerialieToString failed!");
		return false;
	}

	m_pServer->SendMessageToClient(msgLevel, deviceId, serialMsg);

	return true;
}

bool CCommunication::handleUpdateMonitor(u32 msgLevel, u32 deviceId,
										 std::shared_ptr<void> spData)
{
	if (!m_bUIEnble)
	{
		return false;
	}

	std::shared_ptr<TMonitor> spMonitor =
		std::static_pointer_cast<TMonitor>(spData);
	if (!spMonitor)
	{
		ERROR("convert failed!");
		return false;
	}
	datacapture::Message protoMsg;
	protoMsg.set_type(datacapture::Message::PROTO_DATA_MONITOR);
	datacapture::Monitor *msgMonitor = protoMsg.mutable_monitor();
	msgMonitor->set_dwstatuscnt(spMonitor->dwStatusCnt);
	msgMonitor->set_llupdatetime(spMonitor->llUpdateTime);
	msgMonitor->set_llstarttime(spMonitor->llStartTime);
	msgMonitor->set_lldurationtime(spMonitor->llDurationTime);
	for (auto iter : spMonitor->vecStatusList)
	{
		datacapture::CapStatus *msgCapStatus = msgMonitor->add_statuslist();
		msgCapStatus->set_strcrossroadid(iter.strCrossroadId);
		msgCapStatus->set_lltimestamp(iter.llTimestamp);
		msgCapStatus->set_biscaptruing(iter.bIsCaptruing);
		msgCapStatus->set_ffreq(iter.fFreq);
		msgCapStatus->set_strdescribe(iter.strDescribe);
		msgCapStatus->set_dwrecvdatacnt(iter.dwRecvDataCnt);
	}

	std::string serialMsg;
	if (!protoMsg.SerializeToString(&serialMsg))
	{
		ERROR("DATA_MONITOR SerialieToString failed!");
		return false;
	}

	m_pServer->SendMessageToClient(msgLevel, deviceId, serialMsg);

	return true;
}

bool CCommunication::handleDataCompressRes(u32 msgLevel, u32 deviceId,
										   std::shared_ptr<void> spData)
{
	if (!m_bUIEnble)
	{
		return false;
	}

	std::shared_ptr<TDataCompressResList> spDataCompressRes =
		std::static_pointer_cast<TDataCompressResList>(spData);
	if (!spDataCompressRes)
	{
		ERROR("convert failed!");
		return false;
	}
	datacapture::Message protoMsg;
	protoMsg.set_type(datacapture::Message::PROTO_DATA_COMPRESS_RES);
	datacapture::CompressRes *msgCompressRes = protoMsg.mutable_compressres();
	msgCompressRes->set_bissucceed(spDataCompressRes->bIsSucceed);
	msgCompressRes->set_filepath(spDataCompressRes->strPath);
	msgCompressRes->set_strerr(spDataCompressRes->strErr);
	msgCompressRes->set_strserialnum(spDataCompressRes->strSerial);
	msgCompressRes->set_compresstype(spDataCompressRes->chCompressType);
	msgCompressRes->set_strpackagename(spDataCompressRes->strPackageName);

	std::string serialMsg;
	if (!protoMsg.SerializeToString(&serialMsg))
	{
		ERROR("DATA_COMPRESS_RES SerialieToString failed!");
		return false;
	}

	m_pServer->SendMessageToClient(msgLevel, deviceId, serialMsg);

	return true;
}

bool CCommunication::handleConfigRes(u32 msgLevel, u32 deviceId,
									 std::shared_ptr<void> spData)
{
	if (!m_bUIEnble)
	{
		return false;
	}

	std::shared_ptr<TArgsList> spList =
		std::static_pointer_cast<TArgsList>(spData);
	if (!spList)
	{
		ERROR("convert failed!");
		return false;
	}

	datacapture::Message protoMsg;
	protoMsg.set_type(datacapture::Message::PROTO_CONFIG_RES);
	datacapture::ArgsList *msgArgsList = protoMsg.mutable_argslist();
	msgArgsList->set_dwcnt(spList->dwCnt);
	for (auto iter : spList->vecList)
	{
		datacapture::ConnectArgs *msgConnectArgs = msgArgsList->add_argslist();
		switch (iter.argsType)
		{
		case TConnectArgs::HTTP:
			msgConnectArgs->set_type(datacapture::ConnectArgs::PROTO_HTTP);
			break;
		case TConnectArgs::MQTT:
			msgConnectArgs->set_type(datacapture::ConnectArgs::PROTO_MQTT);
			break;
		case TConnectArgs::KAFKA:
			msgConnectArgs->set_type(datacapture::ConnectArgs::PROTO_KAFKA);
			break;
		case TConnectArgs::TCPSERVER:
			msgConnectArgs->set_type(datacapture::ConnectArgs::PROTO_TCP_SERVER);
			break;
		case TConnectArgs::UDP:
			msgConnectArgs->set_type(datacapture::ConnectArgs::PROTO_UDP);
			break;
		default:
			break;
		}
		msgConnectArgs->set_dwno(iter.dwCnt);
		msgConnectArgs->set_isenable(iter.bEnable);
		msgConnectArgs->set_strcrossroadid(iter.strRoadId);
		msgConnectArgs->set_strtopice(iter.strTopic);
		msgConnectArgs->set_strpassword(iter.strPassword);
		msgConnectArgs->set_strclientid(iter.strClientId);
		msgConnectArgs->set_straddr(iter.strAddr);
		msgConnectArgs->set_strdescribe(iter.strDecribe);
		msgConnectArgs->set_strusername(iter.strUsername);
		msgConnectArgs->set_dwfactory(iter.dwFactory);
	}

	std::string serialMsg;
	if (!protoMsg.SerializeToString(&serialMsg))
	{
		ERROR("CONFIG_RES SerialieToString failed!");
		return false;
	}
	// std::cout << "argsize = " << spList->vecList.size() << std::endl;
	m_pServer->SendMessageToClient(msgLevel, deviceId, serialMsg);

	return true;
}

bool CCommunication::handleRoadInfoUpdate(u32 msgLevel, u32 deviceId,
										  std::shared_ptr<void> spData)
{
	if (!m_bUIEnble)
	{
		return false;
	}

	std::shared_ptr<TRoadInfoList> spList =
		std::static_pointer_cast<TRoadInfoList>(spData);
	if (!spList)
	{
		ERROR("convert failed!");
		return false;
	}
	datacapture::Message protoMsg;
	protoMsg.set_type(datacapture::Message::PROTO_ROADINFO_UPDATE);
	datacapture::RoadInfoList *msgRoadInfoList = protoMsg.mutable_roadinfolist();
	msgRoadInfoList->set_dwcnt(spList->dwCnt);
	for (auto it : spList->vecList)
	{
		datacapture::RoadInfo *msgRoadInfo = msgRoadInfoList->add_list();
		msgRoadInfo->set_strid(it.strID);
		msgRoadInfo->set_strdescribe(it.strDescribe);
	}

	std::string serialMsg;
	if (!protoMsg.SerializeToString(&serialMsg))
	{
		ERROR("CONFIG_RES SerialieToString failed!");
		return false;
	}

	m_pServer->SendMessageToClient(msgLevel, deviceId, serialMsg);

	return true;
}

bool CCommunication::handleSystemStatusUpdate(u32 msgLevel, u32 deviceId,
											  std::shared_ptr<void> spData)
{
	if (!m_bUIEnble)
	{
		return false;
	}

	std::shared_ptr<TSystemStatus> spStatus =
		std::static_pointer_cast<TSystemStatus>(spData);
	if (!spStatus)
	{
		ERROR("convert failed!");
		return false;
	}
	datacapture::Message protoMsg;
	protoMsg.set_type(datacapture::Message::PROTO_UPDATE_STSTEM_STATUS);
	datacapture::SystemStatus *msgStatus = protoMsg.mutable_systemstatus();
	msgStatus->set_lltimestamp(spStatus->llTimstamp);
	switch (spStatus->emStatus)
	{
	case TSystemStatus::STATR:
		msgStatus->set_type(datacapture::SystemStatus::START);
		break;
	case TSystemStatus::STOP:
		msgStatus->set_type(datacapture::SystemStatus::STOP);
		break;
	case TSystemStatus::PAUSE:
		msgStatus->set_type(datacapture::SystemStatus::PAUSE);
		break;
	default:
		break;
	}
	std::string serialMsg;
	if (!protoMsg.SerializeToString(&serialMsg))
	{
		ERROR("UPDATE_STSTEM_STATUS SerialieToString failed!");
		return false;
	}

	m_pServer->SendMessageToClient(msgLevel, deviceId, serialMsg);

	return true;
}

bool CCommunication::handleSystemInfoRes(u32 msgLevel, u32 deviceId,
										 std::shared_ptr<void> spData)
{
	std::shared_ptr<TSystemInfo> spSystemInfo =
		std::static_pointer_cast<TSystemInfo>(spData);

	if (!spSystemInfo)
	{
		ERROR("convert failed!");
		return false;
	}

	datacapture::Message protoMsg;
	protoMsg.set_type(datacapture::Message::PROTO_SYSTEM_INFO_RES);
	datacapture::SystemInfo *msgSystemInfo = protoMsg.mutable_systeminfo();
	msgSystemInfo->set_cpucorenum(spSystemInfo->fCpuCoreNum);
	msgSystemInfo->set_cpufreq(spSystemInfo->fCpuFreq);
	msgSystemInfo->set_cputemp(spSystemInfo->fCpuTemp);
	msgSystemInfo->set_cpuused(spSystemInfo->fCpuUsed);
	msgSystemInfo->set_memoryused(spSystemInfo->fMemoryUsed);
	msgSystemInfo->set_memorytotal(spSystemInfo->fMemoryTotal);
	msgSystemInfo->set_diskused(spSystemInfo->fDiskUsed);
	std::string serialMsg;
	if (!protoMsg.SerializeToString(&serialMsg))
	{
		ERROR("SYSTEM_INFO_RES SerialieToString failed!");
		return false;
	}
	m_pServer->SendMessageToClient(msgLevel, deviceId, serialMsg);

	return true;
}

bool CCommunication::handleClientInfoRes(u32 msgLevel, u32 deviceId,
										 std::shared_ptr<void> spData)
{
	std::shared_ptr<TClientInfo> spClientInfo =
		std::static_pointer_cast<TClientInfo>(spData);

	if (!spClientInfo)
	{
		ERROR("convert failed!");
		return false;
	}

	datacapture::Message protoMsg;
	protoMsg.set_type(datacapture::Message::PROTO_CLIENT_INFO_RES);
	datacapture::ClientInfo *msgClientInfo = protoMsg.mutable_clientinfo();
	msgClientInfo->set_ip(spClientInfo->strIp);
	msgClientInfo->set_port(spClientInfo->dwPort);

	std::string serialMsg;
	if (!protoMsg.SerializeToString(&serialMsg))
	{
		ERROR("CLIENT_INFO_RES SerialieToString failed!");
		return false;
	}
	m_pServer->SendMessageToClient(msgLevel, deviceId, serialMsg);

	return true;
}

bool CCommunication::handleClientInfoReq(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData)
{
	std::string strIp = "";
	int dwPort = 0;
	int dwDeviceId = (int)deviceId;
	m_pServer->GetClientInfo(dwDeviceId, &strIp, &dwPort);

	std::shared_ptr<TClientInfo> spClientInfo = std::make_shared<TClientInfo>();
	spClientInfo->strIp = strIp;
	spClientInfo->dwPort = dwPort;

	// 内部消息直接调用函数
	//  Notify(MSG_LEVEL_ONE_DEVICE, deviceId, CLIENT_INFO_RES, std::static_pointer_cast<void>(spClientInfo));
	handleClientInfoRes(MSG_LEVEL_ONE_DEVICE, deviceId, std::static_pointer_cast<void>(spClientInfo));
	return true;
}

bool CCommunication::handleInternetSettingRes(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData)
{
	std::shared_ptr<TNetConfig> spNetConfig = std::static_pointer_cast<TNetConfig>(spData);
	if (!spNetConfig)
	{
		ERROR("convert failed!");
		return false;
	}
	datacapture::Message protoMsg;
	protoMsg.set_type(datacapture::Message::PROTO_SYSTEM_NET_RES);
	datacapture::NetConfig *msgCmd = protoMsg.mutable_netconfig();
	msgCmd->set_localip(spNetConfig->strLocalIp);
	msgCmd->set_gateway(spNetConfig->strGateway);
	msgCmd->set_mask(spNetConfig->strMask);
	msgCmd->set_route(spNetConfig->strRoute);
	msgCmd->set_targetip(spNetConfig->strTargetIp);
	std::string serialMsg;
	if (!protoMsg.SerializeToString(&serialMsg))
	{
		ERROR("PROTO_SYSTEM_NET_RES SerialieToString failed!");
		return false;
	}
	m_pServer->SendMessageToClient(msgLevel, deviceId, serialMsg);
	return true;
}

bool CCommunication::handleStoreSettingRes(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData)
{
	std::shared_ptr<TStoreInfo> spStoreInfo = std::static_pointer_cast<TStoreInfo>(spData);
	if (!spStoreInfo)
	{
		ERROR("convert failed!");
		return false;
	}
	datacapture::Message protoMsg;
	protoMsg.set_type(datacapture::Message::PROTO_SYSTEM_STORE_RES);
	datacapture::storeInfo *msgCmd = protoMsg.mutable_storeinfo();
	msgCmd->set_storepath(spStoreInfo->strStorePath);
	std::string serialMsg;
	if (!protoMsg.SerializeToString(&serialMsg))
	{
		ERROR("PROTO_SYSTEM_NET_RES SerialieToString failed!");
		return false;
	}
	m_pServer->SendMessageToClient(msgLevel, deviceId, serialMsg);
	return true;
}

bool CCommunication::handleSystemCMDRes(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData)
{
	std::shared_ptr<TSystemControl> spSystemControl = std::static_pointer_cast<TSystemControl>(spData);
	if (!spSystemControl)
	{
		ERROR("convert failed!");
		return false;
	}
	datacapture::Message protoMsg;
	protoMsg.set_type(datacapture::Message::PROTO_SYSTEM_CMD_RES);
	datacapture::SystemCmd *msgCmd = protoMsg.mutable_systemcmd();
	msgCmd->set_port(spSystemControl->dwPort);
	switch (spSystemControl->type)
	{
	case TSystemControl::RESTARTSERVER:
		msgCmd->set_type(datacapture::SystemCmd::RESTARTSERVER);
		break;
	case TSystemControl::RECONNECTSERVER:
		msgCmd->set_type(datacapture::SystemCmd::RECONNECTSERVER);
		break;
	case TSystemControl::REBOOT:
		msgCmd->set_type(datacapture::SystemCmd::REBOOT);
		break;
	default:
		break;
	}
	std::string serialMsg;
	if (!protoMsg.SerializeToString(&serialMsg))
	{
		ERROR("PROTO_SYSTEM_CMD_RES SerialieToString failed!");
		return false;
	}
	m_pServer->SendMessageToClient(msgLevel, deviceId, serialMsg);
	return true;
}

bool CCommunication::handleDatabaseClose(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData)
{
	std::shared_ptr<TSystemLog> spDatabaseStatus = std::static_pointer_cast<TSystemLog>(spData);
	if (!spDatabaseStatus)
	{
		ERROR("convert failed!");
		return false;
	}
	datacapture::Message protoMsg;
	protoMsg.set_type(datacapture::Message::PROTO_DATABASE_CLOSED);
	datacapture::SystemLog *msgDatabaseStatus = protoMsg.mutable_systemlog();
	msgDatabaseStatus->set_status(spDatabaseStatus->bStatus);
	std::string serialMsg;
	if (!protoMsg.SerializeToString(&serialMsg))
	{
		ERROR("PROTO_SYSTEM_CMD_RES SerialieToString failed!");
		return false;
	}
	m_pServer->SendMessageToClient(msgLevel, deviceId, serialMsg);
	return true;
}

bool CCommunication::handleSystemLogUpdate(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData)
{
	std::shared_ptr<TSystemLog> spSystemLog = std::static_pointer_cast<TSystemLog>(spData);
	if (!spSystemLog)
	{
		ERROR("convert failed!");
		return false;
	}
	datacapture::Message protoMsg;
	protoMsg.set_type(datacapture::Message::PROTO_COMMON_LOG);
	datacapture::SystemLog *msgSystemLog = protoMsg.mutable_systemlog();
	msgSystemLog->set_status(spSystemLog->bStatus);
	msgSystemLog->set_log(spSystemLog->strLog);
	std::string serialMsg;
	if (!protoMsg.SerializeToString(&serialMsg))
	{
		ERROR("PROTO_SYSTEM_CMD_RES SerialieToString failed!");
		return false;
	}
	m_pServer->SendMessageToClient(msgLevel, deviceId, serialMsg);
	return true;
}

bool CCommunication::handleTestCaseGetUploadFileListRes(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData)
{
	std::shared_ptr<TTestCaseCompressFileInfoList> spTestCaseCompressFileInfoList = std::static_pointer_cast<TTestCaseCompressFileInfoList>(spData);
	if (!spTestCaseCompressFileInfoList)
	{
		ERROR("convert failed!");
		return false;
	}
	datacapture::Message protoMsg;
	protoMsg.set_type(datacapture::Message::PROTO_TESTCASE_GET_UPLOAD_FILE_LIST_RES);
	datacapture::TestCaseCompressFileInfoList *msgList = protoMsg.mutable_testcasecompressfileinfolist();
	msgList->set_issucceed(spTestCaseCompressFileInfoList->isSucceed);
	msgList->set_strerrmsg(spTestCaseCompressFileInfoList->strErrMsg);
	for (auto iter : spTestCaseCompressFileInfoList->vecList)
	{
		datacapture::TestCaseCompressFileInfo *msgInfo = msgList->add_list();
		msgInfo->set_strfilename(iter.strFileName);
		msgInfo->set_fileuid(iter.fileUID);
	}
	std::string serialMsg;
	if (!protoMsg.SerializeToString(&serialMsg))
	{
		ERROR("PROTO_TESTCASE_GET_UPLOAD_FILE_LIST_RES SerialieToString failed!");
		return false;
	}
	m_pServer->SendMessageToClient(msgLevel, deviceId, serialMsg);
	return true;
}
bool CCommunication::handleTestCaseUpdateInfo(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData)
{
	std::shared_ptr<TTestCaseInfoList> spTestCaseInfoList = std::static_pointer_cast<TTestCaseInfoList>(spData);
	if (!spTestCaseInfoList)
	{
		ERROR("convert failed!");
		return false;
	}
	datacapture::Message protoMsg;
	protoMsg.set_type(datacapture::Message::PROTO_TESTCASE_UPDATE_INFO);
	datacapture::TestCaseInfoList *msgInfoList = protoMsg.mutable_testcaseinfolist();
	msgInfoList->set_issucceed(spTestCaseInfoList->isSucceed);
	msgInfoList->set_strerrmsg(spTestCaseInfoList->strErrMsg);
	for (auto iter : spTestCaseInfoList->vecList)
	{
		datacapture::TestCaseInfo *msgInfo = msgInfoList->add_list();
		msgInfo->set_strdatauid(iter.strDataUID);
		msgInfo->set_dwcaseid(iter.dwCaseID);
		msgInfo->set_strcasename(iter.strCaseName);
		msgInfo->set_type(iter.strType);
		msgInfo->set_llimporttime(iter.llImportTime);
	}
	std::string serialMsg;
	if (!protoMsg.SerializeToString(&serialMsg))
	{
		ERROR("PROTO_TESTCASE_UPDATE_INFO SerialieToString failed!");
		return false;
	}
	m_pServer->SendMessageToClient(msgLevel, deviceId, serialMsg);
	return true;
}
bool CCommunication::handleTestCaseAddTestCaseRes(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData)
{
	std::shared_ptr<TAddTestCaseRes> spAddTestCaseRes = std::static_pointer_cast<TAddTestCaseRes>(spData);
	if (!spAddTestCaseRes)
	{
		ERROR("convert failed!");
		return false;
	}
	datacapture::Message protoMsg;
	protoMsg.set_type(datacapture::Message::PROTO_TESTCASE_ADD_TESTCASE_RES);
	datacapture::AddTestCaseRes *msgAddTestCaseRes = protoMsg.mutable_addtestcaseres();
	msgAddTestCaseRes->set_strfilename(spAddTestCaseRes->strFileName);
	msgAddTestCaseRes->set_fileuid(spAddTestCaseRes->fileUID);
	msgAddTestCaseRes->set_issucceed(spAddTestCaseRes->isSucceed);
	msgAddTestCaseRes->set_strerrmsg(spAddTestCaseRes->strErrMsg);
	std::string serialMsg;
	if (!protoMsg.SerializeToString(&serialMsg))
	{
		ERROR("PROTO_TESTCASE_ADD_TESTCASE_RES SerialieToString failed!");
		return false;
	}
	m_pServer->SendMessageToClient(msgLevel, deviceId, serialMsg);
	return true;
}
bool CCommunication::handleTestCaseDeleteOneRes(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData)
{
	std::shared_ptr<TTestCaseCommonRes> spRes = std::static_pointer_cast<TTestCaseCommonRes>(spData);
	if (!spRes)
	{
		ERROR("convert failed!");
		return false;
	}
	datacapture::Message protoMsg;
	protoMsg.set_type(datacapture::Message::PROTO_TESTCASE_DELETE_ONE_RES);
	datacapture::TestCaseCommonRes *msgRes = protoMsg.mutable_testcasecommonres();
	msgRes->set_issucceed(spRes->isSucceed);
	msgRes->set_strerrmsg(spRes->strErrMsg);
	std::string serialMsg;
	if (!protoMsg.SerializeToString(&serialMsg))
	{
		ERROR("PROTO_TESTCASE_DELETE_ONE_RES SerialieToString failed!");
		return false;
	}
	m_pServer->SendMessageToClient(msgLevel, deviceId, serialMsg);
	return true;
}

bool CCommunication::handleTestCaseControlRes(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData)
{
	std::shared_ptr<TTestCaseCommonRes> spRes = std::static_pointer_cast<TTestCaseCommonRes>(spData);
	if (!spRes)
	{
		ERROR("convert failed!");
		return false;
	}
	datacapture::Message protoMsg;
	protoMsg.set_type(datacapture::Message::PROTO_TESTCASE_CONTROL_DATA_PUSH_RES);
	datacapture::TestCaseCommonRes *msgRes = protoMsg.mutable_testcasecommonres();
	msgRes->set_issucceed(spRes->isSucceed);
	msgRes->set_strerrmsg(spRes->strErrMsg);
	std::string serialMsg;
	if (!protoMsg.SerializeToString(&serialMsg))
	{
		ERROR("PROTO_TESTCASE_CONTROL_DATA_PUSH_RES SerialieToString failed!");
		return false;
	}
	m_pServer->SendMessageToClient(msgLevel, deviceId, serialMsg);
	return true;
}

bool CCommunication::handleTestCaseLog(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData)
{
	std::shared_ptr<TTestCaseLog> spLog = std::static_pointer_cast<TTestCaseLog>(spData);
	if (!spLog)
	{
		ERROR("convert failed!");
		return false;
	}
	datacapture::Message protoMsg;
	protoMsg.set_type(datacapture::Message::PROTO_TESTCASE_LOG);
	datacapture::TestCaseLog *msgLog = protoMsg.mutable_testcaselog();
	msgLog->set_type(datacapture::TestCaseLog_logType(spLog->type));
	msgLog->set_strlog(spLog->strLog);
	std::string serialMsg;
	if (!protoMsg.SerializeToString(&serialMsg))
	{
		ERROR("handleTestCaseLog SerialieToString failed!");
		return false;
	}
	m_pServer->SendMessageToClient(msgLevel, deviceId, serialMsg);
	return true;
}

bool CCommunication::handleTestCaseStatus(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData)
{
	std::shared_ptr<TTestCaseStatus> spStatus = std::static_pointer_cast<TTestCaseStatus>(spData);
	if (!spStatus)
	{
		ERROR("convert failed!");
		return false;
	}
	datacapture::Message protoMsg;
	protoMsg.set_type(datacapture::Message::PROTO_TESTCASE_STATUS);
	datacapture::TestCaseStatus* msgStatus = protoMsg.mutable_testcasestatus();
	msgStatus->set_status(datacapture::TestCaseStatus_enumStatus(spStatus->status));
	msgStatus->set_llstarttime(spStatus->llStartTime);
	datacapture::TestCasePushConf *msgConf = msgStatus->mutable_stconf();

    msgConf->set_epushway(datacapture::TestCasePushConf::pushWay(spStatus->stConf.ePushWay));
    msgConf->set_straddress(spStatus->stConf.strAddress);
    msgConf->set_strclientid(spStatus->stConf.strClientID);
    msgConf->set_strdatauid(spStatus->stConf.strDataUid);
    msgConf->set_strpwd(spStatus->stConf.strPWD);
    msgConf->set_strtopic(spStatus->stConf.strTopic);
    msgConf->set_strusername(spStatus->stConf.strUsername);
	std::string serialMsg;
	if (!protoMsg.SerializeToString(&serialMsg))
	{
		ERROR("handleTestCaseLog SerialieToString failed!");
		return false;
	}
	m_pServer->SendMessageToClient(msgLevel, deviceId, serialMsg);
	return true;
}

// bool CCommunication::handleDataCompressReq2Telnet(u32 msgLevel,u32
// deviceId,std::string strMsg)
// {
//     // "package: 路口id 2023-01-01 09:30:30 2023-01-01 09:30:30 路口.zip"
//     // std::regex
//     pattern(R"(\s*package:\s*\".*\"\s+\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}\s+\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}\s+\".*\")");
//     std::regex pattern(R"(package:([^ ]+) (\d{4}-\d{2}-\d{2}
//     \d{2}:\d{2}:\d{2}) (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (.+))");
//     if(!std::regex_match(strMsg, pattern))
//     {
//         m_pTelnet->SendMessageToClient("格式错误\n");
//         return false;
//     }

//     std::smatch matches;
//     std::string strRoadId, strPackage;
//     struct DateTime
//     {
//         int year;
//         int month;
//         int day;
//         int hour;
//         int minute;
//         int second;
//     } stStartTime, stEndTime;
//     if (std::regex_search(strMsg, matches, pattern))
//     {
//         strRoadId = matches[1];

//         std::sscanf(matches[2].str().c_str(), "%d-%d-%d %d:%d:%d",
//         &stStartTime.year, &stStartTime.month, &stStartTime.day,
//         &stStartTime.hour, &stStartTime.minute, &stStartTime.second);
//         std::sscanf(matches[3].str().c_str(), "%d-%d-%d %d:%d:%d",
//         &stEndTime.year, &stEndTime.month, &stEndTime.day, &stEndTime.hour,
//         &stEndTime.minute, &stEndTime.second); strPackage = matches[4];
//     }

//     std::shared_ptr<TDataCompressReqList> spDataCompressReq =
//     std::make_shared<TDataCompressReqList>();
//     spDataCompressReq->chCompressType = 0x01;
//     spDataCompressReq->dwReqCnt = 1;
//     spDataCompressReq->strPackageName = strPackage;
//     spDataCompressReq->strSerial = commonFunc::generateUniqueRandomString(8);

//     TDataQuery stDataQuery{};
//     stDataQuery.strCrossroadId = strRoadId;

//     // 转换起始时间
//     std::tm timeInfo{};
//     timeInfo.tm_year = stStartTime.year - 1900; // 年份需要减去1900
//     timeInfo.tm_mon = stStartTime.month - 1;    // 月份需要减去1
//     timeInfo.tm_mday = stStartTime.day;
//     timeInfo.tm_hour = stStartTime.hour;
//     timeInfo.tm_min = stStartTime.minute;
//     timeInfo.tm_sec = stStartTime.second;
//     timeInfo.tm_isdst = -1; // 自动判断夏令时

//     time_t time = std::mktime(&timeInfo);
//     auto startTimestamp =
//     std::chrono::system_clock::from_time_t(time).time_since_epoch();
//     stDataQuery.llStartTime =
//     std::chrono::duration_cast<std::chrono::milliseconds>(startTimestamp).count();

//     timeInfo.tm_year = stEndTime.year - 1900; // 年份需要减去1900
//     timeInfo.tm_mon = stEndTime.month - 1;    // 月份需要减去1
//     timeInfo.tm_mday = stEndTime.day;
//     timeInfo.tm_hour = stEndTime.hour;
//     timeInfo.tm_min = stEndTime.minute;
//     timeInfo.tm_sec = stEndTime.second;
//     timeInfo.tm_isdst = -1; // 自动判断夏令时

//     time = std::mktime(&timeInfo);
//     auto endTimestamp =
//     std::chrono::system_clock::from_time_t(time).time_since_epoch();
//     stDataQuery.llEndTime =
//     std::chrono::duration_cast<std::chrono::milliseconds>(endTimestamp).count();

//     spDataCompressReq->vecList.push_back(stDataQuery);

//     Notify(DATA_COMPRESS_QUERY_REQ,
//     std::static_pointer_cast<void>(spDataCompressReq));

//     return true;
// }

// bool CCommunication::handleUpdateMonitor2Telnet(std::shared_ptr<void> spData)
// {
//     if(!m_bTelnetEnable || !m_bIsUpdateStatus)
//     {
//         return false;
//     }

//     std::shared_ptr<TMonitor> spMonitor =
//     std::static_pointer_cast<TMonitor>(spData); if(!spMonitor)
//     {
//         std::cout << "convert failed!" << std::endl;
//         return false;
//     }
//     std::string strMsg = "";
//     strMsg += "updatetime:" + std::to_string(spMonitor->llUpdateTime) + "\n";
//     for(auto iter : spMonitor->vecStatusList)
//     {
//         strMsg += iter.strCrossroadId +" "+iter.strDescribe+"
//         freq:"+std::to_string(iter.fFreq)+"hz "; if(iter.bIsCaptruing)
//         {
//             strMsg += "capturing";
//         }else
//         {
//             strMsg += "error!";
//         }
//         strMsg += "\n";
//     }

//     m_pTelnet->SendMessageToClient(strMsg);

//     return true;
// }

// bool CCommunication::handleSystemStatusUpdate2Telnet(std::shared_ptr<void>
// spData)
// {
//     if(!m_bTelnetEnable)
//     {
//         return false;
//     }

//     std::shared_ptr<TSystemStatus> spStatus =
//     std::static_pointer_cast<TSystemStatus>(spData); if (!spStatus)
//     {
//         std::cout << "convert failed !" << std::endl;
//         return false;
//     }
//     std::string strMsg ="";
//     switch (spStatus->emStatus)
//     {
//     case TSystemStatus::STATR:
//     strMsg="[system is running]";
//         break;
//     case TSystemStatus::STOP:
//     case TSystemStatus::PAUSE:
//         strMsg = "[system is no running]";
//         break;
//     default:
//         break;
//     }

//     m_pTelnet->SendMessageToClient(strMsg);

//     return true;
// }

// bool CCommunication::handleDataCompressRes2Telnet(std::shared_ptr<void>
// spData)
// {
//     if (!m_bTelnetEnable)
//     {
//         return false;
//     }
//     std::shared_ptr<TDataCompressResList> spDataCompressRes =
//     std::static_pointer_cast<TDataCompressResList>(spData); if
//     (!spDataCompressRes)
//     {
//         std::cout << "convert failed!" << std::endl;
//         return false;
//     }

//     std::string strMsg;
//     if(spDataCompressRes->bIsSucceed)
//     {
//         strMsg = spDataCompressRes->strPath +
//         "/"+spDataCompressRes->strPackageName;
//     }else{
//         strMsg = spDataCompressRes->strErr;
//     }

//     m_pTelnet->SendMessageToClient(strMsg);

//     return true;
// }

bool CCommunication::handleTestCaseAddTestCaseReq(int dwDeviceId, std::string strMsg)
{
	datacapture::Message protoMsg;
	if (!protoMsg.ParseFromString(strMsg))
	{
		ERROR("Decode protobuf failed! msg type : handleTestCaseAddTestCaseReq");
		return false;
	}
	std::shared_ptr<TTestCaseCompressFileInfo> spTestCaseCompressFileInfo = std::make_shared<TTestCaseCompressFileInfo>();

	spTestCaseCompressFileInfo->strFileName = protoMsg.testcasecompressfileinfo().strfilename();
	spTestCaseCompressFileInfo->fileUID = protoMsg.testcasecompressfileinfo().fileuid();

	Notify(MSG_LEVEL_ONE_DEVICE, dwDeviceId, TESTCASE_ADD_TESTCASE_REQ,
		   std::static_pointer_cast<void>(spTestCaseCompressFileInfo));
	return true;
}
bool CCommunication::handleTestCaseControlDataPushReq(int dwDeviceId, std::string strMsg)
{
	datacapture::Message protoMsg;
	if (!protoMsg.ParseFromString(strMsg))
	{
		ERROR("Decode protobuf failed! msg type : handleTestCaseControlDataPushReq");
		return false;
	}
	std::shared_ptr<TTestCasePushControl> spTestCasePushControl = std::make_shared<TTestCasePushControl>();
	spTestCasePushControl->llTimstamp = protoMsg.testcasepushcontrol().lltimstamp();
	spTestCasePushControl->emStatus = (TTestCasePushControl::enumStatus)protoMsg.testcasepushcontrol().emstatus();
	spTestCasePushControl->stConf.ePushWay = (TTestCasePushConf::pushWay)protoMsg.testcasepushcontrol().stconf().epushway();
	spTestCasePushControl->stConf.strAddress = protoMsg.testcasepushcontrol().stconf().straddress();
	spTestCasePushControl->stConf.strTopic = protoMsg.testcasepushcontrol().stconf().strtopic();
	spTestCasePushControl->stConf.strClientID = protoMsg.testcasepushcontrol().stconf().strclientid();
	spTestCasePushControl->stConf.strUsername = protoMsg.testcasepushcontrol().stconf().strusername();
	spTestCasePushControl->stConf.strPWD = protoMsg.testcasepushcontrol().stconf().strpwd();
	spTestCasePushControl->stConf.strDataUid = protoMsg.testcasepushcontrol().stconf().strdatauid();
	Notify(MSG_LEVEL_ONE_DEVICE, dwDeviceId, TESTCASE_CONTROL_DATA_PUSH_REQ,
		   std::static_pointer_cast<void>(spTestCasePushControl));
	return true;
}

bool CCommunication::handleTestCaseGetAllInfo(int dwDeviceId, std::string strMsg)
{
	datacapture::Message protoMsg;
	if (!protoMsg.ParseFromString(strMsg))
	{
		ERROR("Decode protobuf failed! msg type : handleTestCaseGetAllInfo");
	}
	Notify(MSG_LEVEL_ONE_DEVICE, dwDeviceId, TESTCASE_GET_ALL_INFO,nullptr);
	
	return true;
}

bool CCommunication::handleTestCaseGetUploadFileListReq(int dwDeviceId, std::string strMsg)
{
	datacapture::Message protoMsg;
	if (!protoMsg.ParseFromString(strMsg))
	{
		ERROR("Decode protobuf failed! msg type : handleTestCaseGetUploadFileListReq");
	}
	Notify(MSG_LEVEL_ONE_DEVICE, dwDeviceId, TESTCASE_GET_UPLOAD_FILE_LIST_REQ,nullptr);
	
	return true;
}

bool CCommunication::handleTestCaseDeleteOneReq(int dwDeviceId,std::string strMsg)
{
	std::cout << __func__ <<std::endl;
	datacapture::Message protoMsg;
	if (!protoMsg.ParseFromString(strMsg))
	{
		ERROR("Decode protobuf failed! msg type : handleTestCaseDeleteOneReq");
		return false;
	}
	std::shared_ptr<TTestCaseInfo> spTestCaseInfo = std::make_shared<TTestCaseInfo>();
	spTestCaseInfo->dwCaseID = protoMsg.testcaseinfo().dwcaseid();
	spTestCaseInfo->llImportTime = protoMsg.testcaseinfo().llimporttime();
	spTestCaseInfo->strCaseName = protoMsg.testcaseinfo().strcasename();
	spTestCaseInfo->strDataUID = protoMsg.testcaseinfo().strdatauid();
	spTestCaseInfo->strType = protoMsg.testcaseinfo().type();
	Notify(MSG_LEVEL_ONE_DEVICE, dwDeviceId, TESTCASE_DELETE_ONE_REQ,
		   std::static_pointer_cast<void>(spTestCaseInfo));
	return true;
}