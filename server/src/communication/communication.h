#ifndef _COMMUNICATION_H_
#define _COMMUNICATION_H_
#include "moduleBase.h"
#include "tcpServer.h"
#include "telnetServer.h"
#include "utils/inimanager/config_util.h"
// #include "utils/stringutil/stringutils.h"

volatile extern int gConnectType;
volatile extern int gPort;

class CCommunication : public CModuleBase
{
public:
    CCommunication(CMediator *pMediator);
    ~CCommunication();

    void Init() override;
    void Start() override;
    void Stop() override;
    void Pause() override;

    bool MsgFilter(u32 msgType);
    void HandleMsg(u32 msgLevel,u32 deviceId,u32 msgType,std::shared_ptr<void> spData);

private:
    void dataRecvd(int dwDeviceID,std::string strMsg);
    // void dataRecvdTelnet(std::string strMsg);
    void clientConnected(int dwDeviceId);


    //tcp server
    bool handleCommonCmd(int dwDeviceID,std::string strMsg);
    bool handleUpdateConfig(int dwDeviceID,std::string strMsg);
    bool handleDataQueryReq(int dwDeviceID,std::string strMsg);
    bool handleDataCompressReq(int dwDeviceID,std::string strMsg);
    bool handleConfigReq(int dwDeviceID,std::string strMsg);
    bool handleDataQueryBatchesReq(int dwDeviceID,std::string strMsg);
    bool handleSystemInfoReq(int deDeviceID,std::string strMsg);
    bool handleClientInfoReq(int deDeviceID,std::string strMsg);
    bool handleInternetSettingReq(int dwDeviceId,std::string strMsg);
    bool handleStoreSettingReq(int dwDeviceId,std::string strMsg);
    bool handleSystemCMDReq(int dwDeviceId,std::string strMsg);
    bool handleTestCaseAddTestCaseReq(int dwDeviceId,std::string strMsg);
    bool handleTestCaseControlDataPushReq(int dwDeviceId,std::string strMsg); 
    bool handleTestCaseGetAllInfo(int dwDeviceId,std::string strMsg);
    bool handleTestCaseGetUploadFileListReq(int dwDeviceId,std::string strMsg);
    bool handleTestCaseDeleteOneReq(int dwDeviceId,std::string strMsg);

    bool handleDataQueryRes(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);
    bool handleDataQueryBatchRes(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);
    bool handleUpdateMonitor(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);
    bool handleDataCompressRes(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);
    bool handleConfigRes(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);
    bool handleRoadInfoUpdate(u32 msgLevel,u32 deviceId,std::shared_ptr<void>spData);
    bool handleSystemStatusUpdate(u32 msgLevel,u32 deviceId,std::shared_ptr<void>spData);
    bool handleSystemInfoRes(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);
    bool handleClientInfoRes(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);
    bool handleClientInfoReq(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);
    bool handleInternetSettingRes(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);
    bool handleStoreSettingRes(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);
    bool handleSystemCMDRes(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);
    bool handleDatabaseClose(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);
    bool handleSystemLogUpdate(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData);
    bool handleTestCaseGetUploadFileListRes(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData);
    bool handleTestCaseUpdateInfo(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData);
    bool handleTestCaseAddTestCaseRes(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData);
    bool handleTestCaseDeleteOneRes(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData);
    bool handleTestCaseControlRes(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData);
    bool handleTestCaseLog(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData);
    bool handleTestCaseStatus(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData);
    

    // //telnet server
    // bool handleDataCompressReq2Telnet(std::string strMsg);

    // bool handleUpdateMonitor2Telnet(std::shared_ptr<void> spData);
    // bool handleSystemStatusUpdate2Telnet(std::shared_ptr<void> spData);
    // bool handleDataCompressRes2Telnet(std::shared_ptr<void> spData);


private:
    CTcpServer *m_pServer;
    // CTelnetServer *m_pTelnet;
    int m_dwPort;
    int m_dwTelnetPort;
    bool m_bUIEnble;
    bool m_bTelnetEnable;
    bool m_bIsUpdateStatus;     //telnet 是否更新状态;
    fileutil::ConfigUtil m_config;

};





#endif