#!/bin/bash

# OBU状态接收器测试脚本

echo "=== OBU状态接收器测试脚本 ==="

# 检查netcat是否安装
if ! command -v nc &> /dev/null; then
    echo "错误: 未找到netcat工具，请先安装: sudo apt-get install netcat"
    exit 1
fi

# 配置参数
HOST="localhost"
PORT="8080"

echo "目标地址: $HOST:$PORT"
echo ""

# 测试1: 发送JSON格式数据
echo "测试1: 发送JSON格式的OBU状态数据"
JSON_DATA='{"obu_id":"OBU001","status":"active","latitude":39.9042,"longitude":116.4074,"speed":60,"heading":90}'
echo "发送数据: $JSON_DATA"
echo "$JSON_DATA" | nc -u -w1 $HOST $PORT
echo "JSON数据发送完成"
echo ""

sleep 1

# 测试2: 发送自定义格式数据
echo "测试2: 发送自定义格式的OBU状态数据"
CUSTOM_DATA="OBU002:active:31.2304:121.4737:45:180"
echo "发送数据: $CUSTOM_DATA"
echo "$CUSTOM_DATA" | nc -u -w1 $HOST $PORT
echo "自定义格式数据发送完成"
echo ""

sleep 1

# 测试3: 发送多个JSON数据包
echo "测试3: 发送多个JSON数据包"
for i in {1..3}; do
    JSON_DATA="{\"obu_id\":\"OBU00$i\",\"status\":\"active\",\"latitude\":$((39 + i)).90$i,\"longitude\":$((116 + i)).40$i,\"speed\":$((50 + i * 10)),\"heading\":$((i * 90))}"
    echo "发送数据包 $i: $JSON_DATA"
    echo "$JSON_DATA" | nc -u -w1 $HOST $PORT
    sleep 0.5
done
echo "多个数据包发送完成"
echo ""

# 测试4: 发送无效数据
echo "测试4: 发送无效数据（测试错误处理）"
INVALID_DATA="invalid_data_test"
echo "发送数据: $INVALID_DATA"
echo "$INVALID_DATA" | nc -u -w1 $HOST $PORT
echo "无效数据发送完成"
echo ""

echo "=== 测试完成 ==="
echo "请检查服务器日志以查看数据接收和处理情况"
