#ifndef _STRUCT_H_
#define _STRUCT_H_

#include <vector>
#include <string>

typedef unsigned char u8;
typedef unsigned short u16;
typedef unsigned int u32;
typedef unsigned long long u64;

typedef char s8;
typedef short s16;
typedef int s32;
typedef long long s64;

/* server端结构定义*/

// 消息头
typedef struct tagMsgHead
{
    u8 chVersion = 0xF1; // 版本号
    u32 wPackageLength = 0; // 单包数据体长度
    u32 wDataLenghth = 0;    //数据总长度
    bool bIsCompleted = true;  //是否单包完整
    bool bIsEnd = true;     //是否为最后一个包
    char szSerial[20];      //唯一标识符 13位UTC时间戳+7位随机数
} TMsgHead;

// 数据更新
typedef struct tagRawData
{
    std::string strCrossroadId; // 路口id
    u64 llRecvTime = 0;         // 数据接收时间，采用13位UTC时间戳
    u64 llDataTime = 0;         // 数据时间，采用13位UTC时间戳
    u16 wDataLength = 0;        // 数据长度
    std::string strData;
} TRawData;

// 采集状态
typedef struct tagCaptureStatusUpdate
{
    std::string strCrossroadId; // 路口id
    std::string strDescribe;    // 路口描述
    u64 llTimestamp = 0;        // 时间，采用13位UTC时间戳
    bool bIsCaptruing = false;  // 是否在采集，true：采集中 false：不在采集中
    float fFreq = 0;            // 时间频率
    u32 dwRecvDataCnt = 0;      // 接收数据量
} TCapStatusUpdate;

// 状态监控
typedef struct tagMonitor
{
    u32 dwStatusCnt = 0;
    u64 llUpdateTime = 0; // 更新时间 采用13位UTC时间戳
    u64 llStartTime = 0;    //采集开始时间 采用13位UTC时间戳
    u64 llDurationTime = 0; //持续时间 采用13位UTC时间戳
    std::vector<TCapStatusUpdate> vecStatusList;
} TMonitor;

// 数据查询请求
typedef struct tagDataQuery
{
    enum enumDataType
    {
        RAWDATA,
        STABILITY_DATA,
        TSARI_DATA
    };
    std::string strCrossroadId; // 路口id
    enumDataType dwDataType; // 数据类型
    u64 llStartTime = 0;        // 起始时间，采用13位UTC时间戳
    u64 llEndTime = 0;          // 终止时间，采用13位UTC时间戳
} TDataQuery;

// 数据查询响应
typedef struct tagDataQueryRes
{
    u32 dwCnt;
    bool bIsSucceed = false;
    std::string strErr;
    std::vector<TRawData> vecList;
} TDataQueryRes;

typedef struct tagBatchesDataQueryReq
{
    std::string strCrossId; // 路口id
    u64 llStartTime = 0;    // 开始时间
    u64 llEndTime = 0;      // 结束时间
    u32 dwNowPage = 0;      // 当前页数
    u32 dwPageSize = 0;     // 每页数据量
} TBatchesDataQueryReq;

typedef struct tagBatchesDataQueryRes
{
    bool bIsSucceed = false;       // 是否成功
    u32 dwNowPage = 0;             // 当前页数
    u32 dwPageSize = 0;            // 每页数据量
    u32 dwTotalPages = 0;          // 总页数
    u32 dwTotalDatas = 0;          // 总数据量
    std::string strErr;            // 错误信息
    u32 dwCnt = 0;                 // 数据个数
    std::vector<TRawData> vecList; // 数据
} TBatchesDataQueryRes;

// 文件压缩批量请求
typedef struct tagDataCompressReqList
{
    u32 chCompressType = 0; // 压缩方式：0x01:zip,0x02:tar;0x03:7z
    u32 dwReqCnt;
    std::string strHttpUrl;
    std::string strPackageName; // 名称
    std::string strSerial;
    std::vector<TDataQuery> vecList;
} TDataCompressReqList;

typedef struct tagDataCompressResList
{
    u32 chCompressType = 0; // 压缩方式：0x01:zip,0x02:tar;0x03:7z
    u32 dwResCnt;
    std::string strHttpUrl;
    std::string strSerial;
    std::string strPackageName; // 名称
    bool bIsSucceed = false;
    std::string strErr;
    std::string strPath;
} TDataCompressResList;

// 参数
typedef struct tagConnectArgs
{
    enum enumArgsType
    {
        MQTT,
        HTTP,
        KAFKA,
        TCPSERVER,
        UDP
    };
    int dwCnt = 0;
    bool bEnable = false;
    enumArgsType argsType;
    std::string strRoadId;
    std::string strTopic;
    std::string strUsername;
    std::string strPassword;
    std::string strClientId;
    std::string strAddr;
    std::string strDecribe;
    int dwFactory;
} TConnectArgs;

// 参数更新
typedef struct tagArgsList
{
    u32 dwCnt;
    std::vector<TConnectArgs> vecList;
} TArgsList;

// 路口信息
typedef struct tagRoadInfo
{
    std::string strID;
    std::string strDescribe;
} TRoadInfo;

// 路口信息列表
typedef struct tagRoadInfoList
{
    u32 dwCnt;
    std::vector<TRoadInfo> vecList;
} TRoadInfoList;

typedef struct tagSystemStatus
{
    enum enumStatus
    {
        STATR,
        STOP,
        PAUSE,
    };
    u64 llTimstamp;
    enumStatus emStatus;
} TSystemStatus;

typedef struct tagSystemInfo{
    float fCpuUsed = 0;
    float fCpuTemp = 0;
    float fCpuFreq = 0;
    float fCpuCoreNum = 0;
    float fMemoryUsed = 0;
    float fMemoryTotal = 0;
    float fDiskUsed = 0;

}TSystemInfo;

typedef struct tagClientInfo{
    std::string strIp;
    u32 dwPort = 0;
}TClientInfo;

//网络设置
typedef struct tagNetConfig{
    std::string strLocalIp;
    std::string strMask;
    std::string strGateway;
    std::string strTargetIp;
    std::string strRoute;
}TNetConfig;

//存储设置
typedef struct tagStoreInfo{
    std::string strStorePath;
}TStoreInfo;

//系统控制
typedef struct tagSystemControl{
    int dwPort;
    enum cmdType{
        RECONNECTSERVER,
        RESTARTSERVER,
        REBOOT,
        NOCMD,
    };
    cmdType type;
}TSystemControl;

//系统日志
typedef struct tagSystemLog{
    bool bStatus;
    std::string strLog;
}TSystemLog;

// 测试用例信息
typedef struct tagTestCaseInfo{
    std::string strDataUID;
    int dwCaseID;
    std::string strCaseName;
    enum dataType{
        RSM,
        BSM,
        MAP,
        RSI,
        SPAT
    };
    std::string strType;
    unsigned long long llImportTime;
}TTestCaseInfo;

// 测试用例信息列表
typedef struct tagTestCaseInfoList{
    std::vector<TTestCaseInfo> vecList;
    bool isSucceed;
    std::string strErrMsg;
}TTestCaseInfoList;

// 测试用例推送方式及参数
typedef struct tagTestCasePushConf{
    enum pushWay{
        MQTT,
        UDP
    };
    pushWay ePushWay;
    std::string strAddress;
    std::string strTopic;
    std::string strClientID;
    std::string strUsername;
    std::string strPWD;
    std::string strDataUid;
}TTestCasePushConf;

// 测试用例推送结果
typedef struct tagCasePushRes{
    enum PushStatus{
        waiting,
        pushing,
        error
    };
    PushStatus status;
    std::string strDataUid;
    std::string strErrMsg;

}TCasePushRes;

// 测试用例压缩包文件信息
typedef struct tagTestCaseCompressFileInfo{
    std::string strFileName;
    std::string fileUID;
}TTestCaseCompressFileInfo;

// 文件列表
typedef struct tagTestCaseCompressFileInfoList{
    std::vector<TTestCaseCompressFileInfo> vecList;
    bool isSucceed;
    std::string strErrMsg;
}TTestCaseCompressFileInfoList;

// 添加测试用例Res
typedef struct tagAddTestCaseRes{
    std::string strFileName;
    std::string fileUID;
    bool isSucceed;
    std::string strErrMsg;
}TAddTestCaseRes;

// 推送数据状态
typedef struct tagTestCasePushControl
{
    enum enumStatus
    {
        STATR,
        STOP,
        PAUSE,
    };
    u64 llTimstamp;
    enumStatus emStatus;
    TTestCasePushConf stConf;
} TTestCasePushControl;

typedef struct tagTestCaseCommonRes{
    bool isSucceed;
    std::string strErrMsg;
}TTestCaseCommonRes;

typedef struct tagTestCaseLog{
    enum logType{
        INFO,
        WARN,
        ERROR,
    };
    logType type;
    std::string strLog;
}TTestCaseLog;

typedef struct tagTestCaseStatus{
    u64 llStartTime;
    enum enumStatus{
        init =0,
        standBy,
        capturing,
    };
    enumStatus status;
    TTestCasePushConf stConf;
}TTestCaseStatus;

/* server 与 gui 通信*/

#endif
