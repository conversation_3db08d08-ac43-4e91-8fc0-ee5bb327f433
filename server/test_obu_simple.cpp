#include <iostream>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <string>

int main() {
    int sock = socket(AF_INET, SOCK_DGRAM, 0);
    if (sock < 0) {
        std::cerr << "Socket creation failed" << std::endl;
        return -1;
    }

    struct sockaddr_in server_addr;
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(8080);
    server_addr.sin_addr.s_addr = inet_addr("127.0.0.1");

    std::string test_data = R"({"obu_id":"TEST001","status":"active","latitude":39.9042,"longitude":116.4074,"speed":60,"heading":90})";
    
    std::cout << "Sending test data: " << test_data << std::endl;
    
    int result = sendto(sock, test_data.c_str(), test_data.length(), 0,
                       (struct sockaddr*)&server_addr, sizeof(server_addr));
    
    if (result < 0) {
        std::cerr << "Send failed" << std::endl;
    } else {
        std::cout << "Data sent successfully, " << result << " bytes" << std::endl;
    }

    close(sock);
    return 0;
}
